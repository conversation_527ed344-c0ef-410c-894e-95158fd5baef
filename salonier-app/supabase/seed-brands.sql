-- Insertar marcas principales de coloración profesional

-- Marcas Españolas
INSERT INTO brands (name, country, is_premium) VALUES
('Salerm Cosmetics', 'ES', true),
('Lendan Cosmetics', 'ES', false),
('J Beverly Hills', 'ES', true);

-- Marcas Internacionales Premium
INSERT INTO brands (name, country, is_premium) VALUES
('L''Oréal Professional', 'FR', true),
('Wella Professionals', 'DE', true),
('Schwarzkopf Professional', 'DE', true),
('Matrix', 'US', true),
('Redken', 'US', true),
('Goldwell', 'DE', true),
('Joico', 'US', true),
('<PERSON>', 'US', true),
('Pravana', 'US', true),
('Clairol Professional', 'US', false);

-- Marcas Europeas
INSERT INTO brands (name, country, is_premium) VALUES
('Alfaparf Milano', 'IT', true),
('Davines', 'IT', true),
('<PERSON>mon', 'IT', true),
('Lisap Milano', 'IT', true),
('Selective Professional', 'IT', true),
('Farmavita', 'IT', false),
('Vitality''s', 'IT', false),
('Inebrya', 'IT', false),
('Echosline', 'IT', false);

-- Marcas Especializadas y de Fantasía
INSERT INTO brands (name, country, is_premium) VALUES
('Manic Panic', 'US', false),
('Arctic Fox', 'US', false),
('Punky Colour', 'US', false),
('Ion Color Brilliance', 'US', false),
('Adore', 'US', false),
('Overtone', 'US', true),
('Lime Crime', 'US', true);

-- Marcas Asiáticas
INSERT INTO brands (name, country, is_premium) VALUES
('Milbon', 'JP', true),
('Lebel', 'JP', true),
('Napla', 'JP', true),
('Shiseido Professional', 'JP', true),
('Arimino', 'JP', true);

-- Insertar líneas de productos para las marcas principales

-- L'Oréal Professional
WITH loreal AS (SELECT id FROM brands WHERE name = 'L''Oréal Professional')
INSERT INTO product_lines (brand_id, name, type, ammonia_free) 
SELECT id, 'Majirel', 'permanent', false FROM loreal
UNION ALL SELECT id, 'Inoa', 'permanent', true FROM loreal
UNION ALL SELECT id, 'Dia Light', 'demi_permanent', true FROM loreal
UNION ALL SELECT id, 'Majiblond', 'bleach', false FROM loreal
UNION ALL SELECT id, 'Dialight', 'toner', true FROM loreal;

-- Wella Professionals
WITH wella AS (SELECT id FROM brands WHERE name = 'Wella Professionals')
INSERT INTO product_lines (brand_id, name, type, ammonia_free) 
SELECT id, 'Koleston Perfect', 'permanent', false FROM wella
UNION ALL SELECT id, 'Illumina Color', 'permanent', false FROM wella
UNION ALL SELECT id, 'Color Touch', 'demi_permanent', true FROM wella
UNION ALL SELECT id, 'Blondor', 'bleach', false FROM wella
UNION ALL SELECT id, 'Color Fresh', 'semi_permanent', true FROM wella;

-- Schwarzkopf Professional
WITH schwarzkopf AS (SELECT id FROM brands WHERE name = 'Schwarzkopf Professional')
INSERT INTO product_lines (brand_id, name, type, ammonia_free) 
SELECT id, 'Igora Royal', 'permanent', false FROM schwarzkopf
UNION ALL SELECT id, 'Igora Vibrance', 'demi_permanent', true FROM schwarzkopf
UNION ALL SELECT id, 'BlondMe', 'bleach', false FROM schwarzkopf
UNION ALL SELECT id, 'Igora Zero Amm', 'permanent', true FROM schwarzkopf;

-- Salerm Cosmetics
WITH salerm AS (SELECT id FROM brands WHERE name = 'Salerm Cosmetics')
INSERT INTO product_lines (brand_id, name, type, ammonia_free) 
SELECT id, 'Hi-Color', 'permanent', false FROM salerm
UNION ALL SELECT id, 'Visón', 'permanent', false FROM salerm
UNION ALL SELECT id, 'Keratin Shot', 'demi_permanent', true FROM salerm;

-- Matrix
WITH matrix AS (SELECT id FROM brands WHERE name = 'Matrix')
INSERT INTO product_lines (brand_id, name, type, ammonia_free) 
SELECT id, 'SoColor', 'permanent', false FROM matrix
UNION ALL SELECT id, 'Color Sync', 'demi_permanent', true FROM matrix
UNION ALL SELECT id, 'Light Master', 'bleach', false FROM matrix
UNION ALL SELECT id, 'V-Light', 'bleach', false FROM matrix;

-- Redken
WITH redken AS (SELECT id FROM brands WHERE name = 'Redken')
INSERT INTO product_lines (brand_id, name, type, ammonia_free) 
SELECT id, 'Chromatics', 'permanent', true FROM redken
UNION ALL SELECT id, 'Shades EQ', 'demi_permanent', true FROM redken
UNION ALL SELECT id, 'Color Extend', 'permanent', false FROM redken
UNION ALL SELECT id, 'Flashlift', 'bleach', false FROM redken;

-- Insertar algunos productos de ejemplo (tonos más comunes)

-- Función para insertar productos comunes
CREATE OR REPLACE FUNCTION insert_common_shades(
  p_line_name TEXT,
  p_brand_name TEXT
) RETURNS void AS $$
DECLARE
  v_line_id UUID;
BEGIN
  SELECT pl.id INTO v_line_id
  FROM product_lines pl
  JOIN brands b ON pl.brand_id = b.id
  WHERE pl.name = p_line_name AND b.name = p_brand_name;
  
  IF v_line_id IS NOT NULL THEN
    -- Insertar tonos base (1-10)
    INSERT INTO products (product_line_id, shade_code, shade_name, base_level, tone_direction, price_per_unit, unit_size, unit_type)
    VALUES 
      (v_line_id, '1', 'Negro', 1, 'N', 8.50, 60, 'ml'),
      (v_line_id, '3', 'Castaño Oscuro', 3, 'N', 8.50, 60, 'ml'),
      (v_line_id, '4', 'Castaño Medio', 4, 'N', 8.50, 60, 'ml'),
      (v_line_id, '5', 'Castaño Claro', 5, 'N', 8.50, 60, 'ml'),
      (v_line_id, '6', 'Rubio Oscuro', 6, 'N', 8.50, 60, 'ml'),
      (v_line_id, '7', 'Rubio Medio', 7, 'N', 8.50, 60, 'ml'),
      (v_line_id, '8', 'Rubio Claro', 8, 'N', 8.50, 60, 'ml'),
      (v_line_id, '9', 'Rubio Muy Claro', 9, 'N', 8.50, 60, 'ml'),
      (v_line_id, '10', 'Rubio Platino', 10, 'N', 8.50, 60, 'ml');
    
    -- Insertar tonos con reflejos más comunes
    INSERT INTO products (product_line_id, shade_code, shade_name, base_level, tone_direction, price_per_unit, unit_size, unit_type)
    VALUES 
      (v_line_id, '6.1', 'Rubio Oscuro Ceniza', 6, 'Ash', 8.50, 60, 'ml'),
      (v_line_id, '7.1', 'Rubio Medio Ceniza', 7, 'Ash', 8.50, 60, 'ml'),
      (v_line_id, '8.1', 'Rubio Claro Ceniza', 8, 'Ash', 8.50, 60, 'ml'),
      (v_line_id, '6.3', 'Rubio Oscuro Dorado', 6, 'Gold', 8.50, 60, 'ml'),
      (v_line_id, '7.3', 'Rubio Medio Dorado', 7, 'Gold', 8.50, 60, 'ml'),
      (v_line_id, '8.3', 'Rubio Claro Dorado', 8, 'Gold', 8.50, 60, 'ml'),
      (v_line_id, '5.4', 'Castaño Claro Cobrizo', 5, 'Copper', 8.50, 60, 'ml'),
      (v_line_id, '6.4', 'Rubio Oscuro Cobrizo', 6, 'Copper', 8.50, 60, 'ml'),
      (v_line_id, '7.4', 'Rubio Medio Cobrizo', 7, 'Copper', 8.50, 60, 'ml');
  END IF;
END;
$$ LANGUAGE plpgsql;

-- Insertar productos para las líneas principales
SELECT insert_common_shades('Majirel', 'L''Oréal Professional');
SELECT insert_common_shades('Koleston Perfect', 'Wella Professionals');
SELECT insert_common_shades('Igora Royal', 'Schwarzkopf Professional');
SELECT insert_common_shades('Hi-Color', 'Salerm Cosmetics');
SELECT insert_common_shades('SoColor', 'Matrix');
SELECT insert_common_shades('Chromatics', 'Redken');

-- Limpiar función temporal
DROP FUNCTION insert_common_shades(TEXT, TEXT);