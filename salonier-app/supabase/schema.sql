-- Esquema de base de datos para Salonier

-- Habilitar extensiones necesarias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Tabla de usuarios extendida
CREATE TABLE profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    full_name TEXT NOT NULL,
    phone TEXT,
    role TEXT CHECK (role IN ('stylist', 'salon_owner', 'admin')) DEFAULT 'stylist',
    salon_name TEXT,
    specialties TEXT[],
    preferred_brands UUID[],
    region TEXT NOT NULL DEFAULT 'es',
    onboarding_completed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Tabla de marcas
CREATE TABLE brands (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL UNIQUE,
    logo_url TEXT,
    country TEXT NOT NULL,
    is_premium BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- <PERSON><PERSON> de líneas de productos
CREATE TABLE product_lines (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    brand_id UUID NOT NULL REFERENCES brands(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    type TEXT CHECK (type IN ('permanent', 'demi_permanent', 'semi_permanent', 'bleach', 'toner')) NOT NULL,
    ammonia_free BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(brand_id, name)
);

-- Tabla de productos
CREATE TABLE products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_line_id UUID NOT NULL REFERENCES product_lines(id) ON DELETE CASCADE,
    shade_code TEXT NOT NULL,
    shade_name TEXT NOT NULL,
    base_level INTEGER CHECK (base_level >= 1 AND base_level <= 10),
    tone_direction TEXT,
    price_per_unit DECIMAL(10,2),
    unit_size INTEGER NOT NULL,
    unit_type TEXT CHECK (unit_type IN ('ml', 'g')) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(product_line_id, shade_code)
);

-- Tabla de clientes
CREATE TABLE clients (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    full_name TEXT NOT NULL,
    phone TEXT,
    email TEXT,
    allergies TEXT[],
    preferences TEXT,
    last_visit TIMESTAMPTZ,
    next_appointment TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Tabla de consultas
CREATE TABLE consultations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    status TEXT CHECK (status IN ('in_progress', 'completed', 'cancelled')) DEFAULT 'in_progress',
    desired_result TEXT,
    before_photos TEXT[],
    after_photos TEXT[],
    notes TEXT,
    total_cost DECIMAL(10,2),
    final_price DECIMAL(10,2),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    completed_at TIMESTAMPTZ
);

-- Tabla de análisis de cabello
CREATE TABLE hair_analyses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    consultation_id UUID NOT NULL REFERENCES consultations(id) ON DELETE CASCADE,
    natural_level INTEGER CHECK (natural_level >= 1 AND natural_level <= 10),
    undertone TEXT CHECK (undertone IN ('warm', 'cool', 'neutral')),
    gray_percentage INTEGER CHECK (gray_percentage >= 0 AND gray_percentage <= 100),
    porosity TEXT CHECK (porosity IN ('low', 'medium', 'high')),
    density TEXT CHECK (density IN ('thin', 'medium', 'thick')),
    condition TEXT CHECK (condition IN ('healthy', 'damaged', 'very_damaged')),
    previous_treatments TEXT[],
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Tabla de formulaciones de color
CREATE TABLE color_formulations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    consultation_id UUID NOT NULL REFERENCES consultations(id) ON DELETE CASCADE,
    brand_id UUID NOT NULL REFERENCES brands(id),
    product_line_id UUID NOT NULL REFERENCES product_lines(id),
    formula JSONB NOT NULL,
    total_cost DECIMAL(10,2),
    suggested_price DECIMAL(10,2),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Tabla de servicios
CREATE TABLE services (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    base_price DECIMAL(10,2) NOT NULL,
    duration_minutes INTEGER NOT NULL,
    category TEXT CHECK (category IN ('color', 'cut', 'treatment', 'styling')) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Tabla de citas
CREATE TABLE appointments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    service_id UUID NOT NULL REFERENCES services(id),
    date TIMESTAMPTZ NOT NULL,
    duration_minutes INTEGER NOT NULL,
    status TEXT CHECK (status IN ('scheduled', 'confirmed', 'completed', 'cancelled')) DEFAULT 'scheduled',
    notes TEXT,
    reminder_sent BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Tabla de inventario
CREATE TABLE inventory (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id),
    quantity_available DECIMAL(10,2) NOT NULL DEFAULT 0,
    minimum_stock DECIMAL(10,2) NOT NULL DEFAULT 1,
    cost_per_unit DECIMAL(10,2),
    expiry_date DATE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, product_id)
);

-- Tabla de conversiones de marcas
CREATE TABLE brand_conversions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    from_product_id UUID NOT NULL REFERENCES products(id),
    to_product_id UUID NOT NULL REFERENCES products(id),
    confidence_level INTEGER CHECK (confidence_level >= 0 AND confidence_level <= 100) DEFAULT 80,
    notes TEXT,
    verified_by_professionals INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(from_product_id, to_product_id)
);

-- Tabla de consentimientos y seguridad
CREATE TABLE safety_checks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    consultation_id UUID NOT NULL REFERENCES consultations(id) ON DELETE CASCADE,
    patch_test_done BOOLEAN DEFAULT FALSE,
    patch_test_date TIMESTAMPTZ,
    patch_test_result TEXT,
    consent_signed BOOLEAN DEFAULT FALSE,
    consent_date TIMESTAMPTZ,
    allergies_confirmed TEXT[],
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Índices para mejorar rendimiento
CREATE INDEX idx_consultations_user_id ON consultations(user_id);
CREATE INDEX idx_consultations_client_id ON consultations(client_id);
CREATE INDEX idx_appointments_user_id ON appointments(user_id);
CREATE INDEX idx_appointments_date ON appointments(date);
CREATE INDEX idx_clients_user_id ON clients(user_id);
CREATE INDEX idx_inventory_user_id ON inventory(user_id);
CREATE INDEX idx_services_user_id ON services(user_id);

-- Row Level Security (RLS)
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE clients ENABLE ROW LEVEL SECURITY;
ALTER TABLE consultations ENABLE ROW LEVEL SECURITY;
ALTER TABLE appointments ENABLE ROW LEVEL SECURITY;
ALTER TABLE services ENABLE ROW LEVEL SECURITY;
ALTER TABLE inventory ENABLE ROW LEVEL SECURITY;

-- Políticas de seguridad básicas
CREATE POLICY "Users can view and update their own profile" ON profiles
    FOR ALL USING (auth.uid() = id);

CREATE POLICY "Users can manage their own clients" ON clients
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own consultations" ON consultations
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own appointments" ON appointments
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own services" ON services
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own inventory" ON inventory
    FOR ALL USING (auth.uid() = user_id);

-- Triggers para actualizar timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_clients_updated_at BEFORE UPDATE ON clients
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_services_updated_at BEFORE UPDATE ON services
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_appointments_updated_at BEFORE UPDATE ON appointments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_inventory_updated_at BEFORE UPDATE ON inventory
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();