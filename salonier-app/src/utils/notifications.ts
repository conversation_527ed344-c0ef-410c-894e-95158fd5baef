import { Platform, Linking } from 'react-native';
import * as Device from 'expo-device';

export const openSystemNotificationSettings = () => {
  if (Platform.OS === 'ios') {
    Linking.openURL('app-settings:');
  } else if (Platform.OS === 'android') {
    Linking.openSettings();
  }
};

export const getDeviceInfo = () => {
  return {
    brand: Device.brand,
    modelName: Device.modelName,
    osName: Device.osName,
    osVersion: Device.osVersion,
    deviceType: Device.deviceType,
    isDevice: Device.isDevice,
  };
};

export const formatNotificationTime = (minutes: number): string => {
  if (minutes < 60) {
    return `${minutes} minutos`;
  } else if (minutes < 1440) {
    const hours = Math.floor(minutes / 60);
    return hours === 1 ? '1 hora' : `${hours} horas`;
  } else {
    const days = Math.floor(minutes / 1440);
    return days === 1 ? '1 día' : `${days} días`;
  }
};

export const getNotificationIcon = (type: string): string => {
  switch (type) {
    case 'appointment':
      return '🗓️';
    case 'inventory':
      return '📦';
    case 'client_care':
      return '💇‍♀️';
    case 'business':
      return '📊';
    default:
      return '🔔';
  }
};

export const shouldShowNotificationPrompt = (lastPromptDate?: string): boolean => {
  if (!lastPromptDate) return true;
  
  const daysSinceLastPrompt = Math.floor(
    (Date.now() - new Date(lastPromptDate).getTime()) / (1000 * 60 * 60 * 24)
  );
  
  return daysSinceLastPrompt >= 7; // Show prompt once a week
};