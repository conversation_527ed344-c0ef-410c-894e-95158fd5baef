import Toast from 'react-native-toast-message';

// Hook para manejar errores en componentes
import { useCallback } from 'react';

export enum ErrorCode {
  NETWORK_ERROR = 'NETWORK_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  AUTH_ERROR = 'AUTH_ERROR',
  PERMISSION_ERROR = 'PERMISSION_ERROR',
  NOT_FOUND = 'NOT_FOUND',
  SERVER_ERROR = 'SERVER_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

export class AppError extends Error {
  constructor(
    public code: ErrorCode,
    message: string,
    public details?: Record<string, unknown>
  ) {
    super(message);
    this.name = 'AppError';
  }
}

export interface ErrorHandler {
  handle(error: Error): void;
  handleAsync(error: Error): Promise<void>;
}

class ErrorHandlerImpl implements ErrorHandler {
  private userFriendlyMessages: Record<ErrorCode, string> = {
    [ErrorCode.NETWORK_ERROR]: 'No se pudo conectar. Verifica tu conexión a internet.',
    [ErrorCode.VALIDATION_ERROR]: 'Por favor revisa los datos ingresados.',
    [ErrorCode.AUTH_ERROR]: 'Necesitas iniciar sesión para continuar.',
    [ErrorCode.PERMISSION_ERROR]: 'No tienes permisos para realizar esta acción.',
    [ErrorCode.NOT_FOUND]: 'No se encontró lo que buscabas.',
    [ErrorCode.SERVER_ERROR]: 'Algo salió mal. Por favor intenta más tarde.',
    [ErrorCode.UNKNOWN_ERROR]: 'Ocurrió un error inesperado.',
  };

  handle(error: Error): void {
    console.error('Error handled:', error);

    const message = this.getUserFriendlyMessage(error);

    Toast.show({
      type: 'error',
      text1: 'Error',
      text2: message,
    });

    // En producción, aquí enviaríamos el error a un servicio de monitoreo
    if (__DEV__) {
      console.error('Error details:', error);
    }
  }

  async handleAsync(error: Error): Promise<void> {
    console.error('Async error handled:', error);

    const message = this.getUserFriendlyMessage(error);

    Toast.show({
      type: 'error',
      text1: 'Error',
      text2: message,
    });
  }

  private getUserFriendlyMessage(error: Error): string {
    if (error instanceof AppError) {
      return this.userFriendlyMessages[error.code] || error.message;
    }

    // Errores de red comunes
    if (error.message.includes('Network request failed')) {
      return this.userFriendlyMessages[ErrorCode.NETWORK_ERROR];
    }

    // Errores de permisos
    if (error.message.includes('User denied')) {
      return 'Necesitamos tu permiso para continuar.';
    }

    return this.userFriendlyMessages[ErrorCode.UNKNOWN_ERROR];
  }
}

export const errorHandler = new ErrorHandlerImpl();

export function useErrorHandler() {
  const handleError = useCallback((error: Error) => {
    errorHandler.handle(error);
  }, []);

  const handleAsyncError = useCallback(async (error: Error) => {
    await errorHandler.handleAsync(error);
  }, []);

  return {
    handleError,
    handleAsyncError,
  };
}

// Decorador para métodos asíncronos
export function catchErrors(_target: any, _propertyKey: string, descriptor: PropertyDescriptor) {
  const originalMethod = descriptor.value;

  descriptor.value = async function (...args: any[]) {
    try {
      return await originalMethod.apply(this, args);
    } catch (error) {
      errorHandler.handle(error as Error);
      throw error;
    }
  };

  return descriptor;
}

// Utilidad para validar respuestas de API
export function validateApiResponse<T>(response: any): T {
  if (!response) {
    throw new AppError(ErrorCode.SERVER_ERROR, 'No se recibió respuesta del servidor');
  }

  if (response.error) {
    throw new AppError(
      ErrorCode.SERVER_ERROR,
      response.error.message || 'Error en el servidor',
      response.error
    );
  }

  if (!response.data) {
    throw new AppError(ErrorCode.SERVER_ERROR, 'Respuesta inválida del servidor');
  }

  return response.data as T;
}

// Utilidad para reintentar operaciones
export async function retry<T>(
  fn: () => Promise<T>,
  options: {
    retries?: number;
    delay?: number;
    onRetry?: (error: Error, attempt: number) => void;
  } = {}
): Promise<T> {
  const { retries = 3, delay = 1000, onRetry } = options;

  let lastError: Error;

  for (let i = 0; i < retries; i++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;

      if (i < retries - 1) {
        onRetry?.(lastError, i + 1);
        await new Promise(resolve => setTimeout(resolve, delay * (i + 1)));
      }
    }
  }

  throw lastError!;
}
