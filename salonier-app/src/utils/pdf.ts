import { Platform } from 'react-native';
import Toast from 'react-native-toast-message';
import * as Print from 'expo-print';
import * as Sharing from 'expo-sharing';
// import { formatConsultationMessage } from './whatsapp';
// import { Product } from '../types';

export const generateConsultationPDF = async (consultation: any) => {
  const { client, hairAnalysis, desiredColor, formulation } = consultation;

  const html = `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="utf-8">
        <title>Consulta - ${client?.name}</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
          }
          .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #7C3AED;
          }
          .logo {
            font-size: 32px;
            color: #7C3AED;
            font-weight: bold;
            margin-bottom: 10px;
          }
          .title {
            font-size: 24px;
            color: #333;
            margin-bottom: 5px;
          }
          .date {
            color: #666;
            font-size: 14px;
          }
          .section {
            margin-bottom: 25px;
            padding: 15px;
            background-color: #f8f8f8;
            border-radius: 8px;
          }
          .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #7C3AED;
            margin-bottom: 10px;
          }
          .info-row {
            margin-bottom: 8px;
            display: flex;
            justify-content: space-between;
          }
          .label {
            font-weight: 500;
            color: #666;
          }
          .value {
            color: #333;
          }
          .formula-item {
            background-color: white;
            padding: 10px;
            margin-bottom: 8px;
            border-radius: 5px;
            border-left: 3px solid #7C3AED;
          }
          .financial-summary {
            background-color: #7C3AED;
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
          }
          .price-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
          }
          .total-price {
            font-size: 24px;
            font-weight: bold;
            border-top: 2px solid white;
            padding-top: 10px;
            margin-top: 10px;
          }
          .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            color: #666;
            font-size: 12px;
          }
          .notes {
            background-color: #fff9e6;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
          }
        </style>
      </head>
      <body>
        <div class="header">
          <div class="logo">Salonier</div>
          <div class="title">Resumen de Consulta</div>
          <div class="date">${new Date().toLocaleDateString('es-ES', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
          })}</div>
        </div>

        <div class="section">
          <div class="section-title">Información del Cliente</div>
          <div class="info-row">
            <span class="label">Nombre:</span>
            <span class="value">${client?.name || 'N/A'}</span>
          </div>
          <div class="info-row">
            <span class="label">Teléfono:</span>
            <span class="value">${client?.phone || 'N/A'}</span>
          </div>
          ${
            client?.email
              ? `
          <div class="info-row">
            <span class="label">Email:</span>
            <span class="value">${client.email}</span>
          </div>
          `
              : ''
          }
        </div>

        <div class="section">
          <div class="section-title">Análisis del Cabello</div>
          <div class="info-row">
            <span class="label">Nivel Natural:</span>
            <span class="value">${hairAnalysis?.natural_level || 'N/A'}</span>
          </div>
          <div class="info-row">
            <span class="label">Porcentaje de Canas:</span>
            <span class="value">${hairAnalysis?.gray_percentage || 0}%</span>
          </div>
          <div class="info-row">
            <span class="label">Estado del Cabello:</span>
            <span class="value">${
              hairAnalysis?.condition === 'damaged'
                ? 'Dañado'
                : hairAnalysis?.condition === 'very_healthy'
                  ? 'Muy saludable'
                  : 'Saludable'
            }</span>
          </div>
          ${
            hairAnalysis?.previous_treatments
              ? `
          <div class="info-row">
            <span class="label">Tratamientos Previos:</span>
            <span class="value">${hairAnalysis.previous_treatments}</span>
          </div>
          `
              : ''
          }
        </div>

        <div class="section">
          <div class="section-title">Color Deseado</div>
          <div class="info-row">
            <span class="label">Nivel Objetivo:</span>
            <span class="value">${desiredColor?.level || 'N/A'}</span>
          </div>
          <div class="info-row">
            <span class="label">Tono:</span>
            <span class="value">${desiredColor?.tone || 'N/A'}</span>
          </div>
          <div class="info-row">
            <span class="label">Técnica:</span>
            <span class="value">${
              desiredColor?.technique === 'global'
                ? 'Color Global'
                : desiredColor?.technique === 'highlights'
                  ? 'Mechas'
                  : desiredColor?.technique === 'balayage'
                    ? 'Balayage'
                    : desiredColor?.technique === 'ombre'
                      ? 'Ombré'
                      : desiredColor?.technique === 'babylights'
                        ? 'Babylights'
                        : 'Global'
            }</span>
          </div>
        </div>

        <div class="section">
          <div class="section-title">Fórmula Aplicada</div>
          ${
            formulation?.formula?.products
              ?.map(
                (product: any) => `
            <div class="formula-item">
              <strong>${product.name}</strong><br>
              Cantidad: ${product.amount}${product.unit}
            </div>
          `
              )
              .join('') || '<p>No hay productos registrados</p>'
          }
          <div class="info-row" style="margin-top: 15px;">
            <span class="label">Tiempo de Procesamiento:</span>
            <span class="value">${formulation?.formula?.processing_time || 35} minutos</span>
          </div>
        </div>

        <div class="financial-summary">
          <div class="section-title" style="color: white;">Resumen Financiero</div>
          <div class="price-row">
            <span>Costo de Productos:</span>
            <span>€${formulation?.total_cost?.toFixed(2) || '0.00'}</span>
          </div>
          <div class="price-row">
            <span>Precio del Servicio:</span>
            <span>€${formulation?.suggested_price?.toFixed(2) || '0.00'}</span>
          </div>
          <div class="price-row total-price">
            <span>TOTAL:</span>
            <span>€${formulation?.suggested_price?.toFixed(2) || '0.00'}</span>
          </div>
        </div>

        ${
          formulation?.notes
            ? `
        <div class="notes">
          <div class="section-title">Notas Adicionales</div>
          <p>${formulation.notes}</p>
        </div>
        `
            : ''
        }

        <div class="footer">
          <p>Documento generado por Salonier App</p>
          <p>¡Gracias por tu confianza!</p>
        </div>
      </body>
    </html>
  `;

  try {
    // Generar el PDF
    const { uri } = await Print.printToFileAsync({ html });

    // Opciones para compartir
    if (Platform.OS === 'ios') {
      await Sharing.shareAsync(uri, {
        UTI: '.pdf',
        mimeType: 'application/pdf',
      });
    } else {
      // En Android, usar el sistema de compartir nativo
      await Sharing.shareAsync(uri);
    }

    return uri;
  } catch (error) {
    console.error('Error generating PDF:', error);
    Toast.show({
      type: 'error',
      text1: 'Error',
      text2: 'No se pudo generar el PDF',
    });
    throw error;
  }
};

export const previewPDF = async (consultation: any) => {
  try {
    const uri = await generateConsultationPDF(consultation);
    return uri;
  } catch (error) {
    console.error('Error previewing PDF:', error);
    return null;
  }
};

export const generateShoppingListPDF = async (products: any[], totalCost: number) => {
  // Agrupar productos por marca
  const groupedProducts: { [brand: string]: any[] } = {};
  products.forEach(product => {
    if (!groupedProducts[product.brand]) {
      groupedProducts[product.brand] = [];
    }
    groupedProducts[product.brand].push(product);
  });

  const html = `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="utf-8">
        <title>Lista de Compra - Salonier</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
          }
          .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #D4A574;
          }
          .logo {
            font-size: 32px;
            color: #D4A574;
            font-weight: bold;
            margin-bottom: 10px;
          }
          .title {
            font-size: 24px;
            color: #333;
            margin-bottom: 5px;
          }
          .date {
            color: #666;
            font-size: 14px;
          }
          .brand-section {
            margin-bottom: 25px;
            padding: 15px;
            background-color: #f8f8f8;
            border-radius: 8px;
            page-break-inside: avoid;
          }
          .brand-title {
            font-size: 18px;
            font-weight: bold;
            color: #D4A574;
            margin-bottom: 10px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
          }
          .product-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px dashed #ddd;
          }
          .product-item:last-child {
            border-bottom: none;
          }
          .product-info {
            flex: 1;
          }
          .product-name {
            font-weight: 500;
            margin-bottom: 3px;
          }
          .product-details {
            font-size: 12px;
            color: #666;
          }
          .product-quantity {
            font-weight: bold;
            color: #333;
            margin-left: 20px;
          }
          .stock-warning {
            display: inline-block;
            background-color: #FEE2E2;
            color: #DC2626;
            font-size: 11px;
            padding: 2px 6px;
            border-radius: 4px;
            margin-left: 8px;
          }
          .summary {
            margin-top: 30px;
            padding: 20px;
            background-color: #D4A574;
            color: white;
            border-radius: 8px;
            text-align: center;
          }
          .summary-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
          }
          .total-products {
            font-size: 16px;
            margin-bottom: 5px;
          }
          .total-cost {
            font-size: 24px;
            font-weight: bold;
          }
          .footer {
            text-align: center;
            margin-top: 40px;
            color: #999;
            font-size: 12px;
          }
          .notes {
            margin-top: 20px;
            padding: 15px;
            background-color: #FFF9E6;
            border-left: 4px solid #F59E0B;
            border-radius: 4px;
          }
          .notes-title {
            font-weight: bold;
            margin-bottom: 5px;
          }
        </style>
      </head>
      <body>
        <div class="header">
          <div class="logo">SALONIER</div>
          <div class="title">Lista de Compra</div>
          <div class="date">Generada el ${new Date().toLocaleDateString('es-ES', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
          })}</div>
        </div>

        ${Object.entries(groupedProducts)
          .map(
            ([brand, brandProducts]) => `
          <div class="brand-section">
            <div class="brand-title">${brand}</div>
            ${brandProducts
              .map(
                product => `
              <div class="product-item">
                <div class="product-info">
                  <div class="product-name">${product.name}</div>
                  <div class="product-details">
                    Código: ${product.code} | Stock actual: ${product.current_stock}/${product.min_stock} ${product.unit}
                    ${product.current_stock <= product.min_stock * 0.5 ? '<span class="stock-warning">CRÍTICO</span>' : ''}
                  </div>
                </div>
                <div class="product-quantity">
                  ${product.adjustedQuantity} ${product.unit}
                </div>
              </div>
            `
              )
              .join('')}
          </div>
        `
          )
          .join('')}

        <div class="notes">
          <div class="notes-title">📌 Recordatorios:</div>
          <ul style="margin: 5px 0; padding-left: 20px;">
            <li>Verificar fechas de caducidad al recibir</li>
            <li>Comparar precios con otros proveedores</li>
            <li>Solicitar descuentos por volumen</li>
          </ul>
        </div>

        <div class="summary">
          <div class="summary-title">RESUMEN DEL PEDIDO</div>
          <div class="total-products">Total de productos: ${products.length}</div>
          <div class="total-cost">Total estimado: €${totalCost.toFixed(2)}</div>
        </div>

        <div class="footer">
          <p>Lista generada automáticamente por Salonier App</p>
          <p>www.salonier.app | <EMAIL></p>
        </div>
      </body>
    </html>
  `;

  try {
    const { uri } = await Print.printToFileAsync({ html });

    if (Platform.OS === 'ios') {
      await Sharing.shareAsync(uri, {
        UTI: '.pdf',
        mimeType: 'application/pdf',
      });
    } else {
      await Sharing.shareAsync(uri);
    }

    return uri;
  } catch (error) {
    console.error('Error generating shopping list PDF:', error);
    Toast.show({
      type: 'error',
      text1: 'Error',
      text2: 'No se pudo generar el PDF de la lista de compra',
    });
    throw error;
  }
};
