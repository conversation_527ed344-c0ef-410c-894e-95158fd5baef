import { Linking } from 'react-native';
import Toast from 'react-native-toast-message';

export const sendWhatsAppMessage = async (phoneNumber: string, message: string) => {
  try {
    // Limpiar el número de teléfono (quitar espacios, guiones, etc.)
    const cleanedNumber = phoneNumber.replace(/[\s\-\(\)]/g, '');

    // Asegurarse de que el número tenga el código de país
    let formattedNumber = cleanedNumber;
    if (!formattedNumber.startsWith('+')) {
      // Asumimos España (+34) si no hay código de país
      formattedNumber = `+34${formattedNumber}`;
    }

    // Codificar el mensaje para URL
    const encodedMessage = encodeURIComponent(message);

    // Crear la URL de WhatsApp
    const whatsappUrl = `whatsapp://send?phone=${formattedNumber}&text=${encodedMessage}`;

    // Verificar si WhatsApp está instalado
    const canOpen = await Linking.canOpenURL(whatsappUrl);

    if (!canOpen) {
      // Open WhatsApp Web directly without confirmation
      const webUrl = `https://wa.me/${formattedNumber}?text=${encodedMessage}`;
      await Linking.openURL(webUrl);
      Toast.show({
        type: 'info',
        text1: 'WhatsApp Web',
        text2: 'Abriendo WhatsApp Web en el navegador',
      });
      return;
    }

    // Abrir WhatsApp
    await Linking.openURL(whatsappUrl);
  } catch (error) {
    console.error('Error opening WhatsApp:', error);
    Toast.show({
      type: 'error',
      text1: 'Error',
      text2: 'No se pudo abrir WhatsApp',
    });
  }
};

export const formatConsultationMessage = (consultation: any): string => {
  const { client, hairAnalysis, desiredColor, formulation } = consultation;

  const message = `
*Resumen de Consulta - ${client?.name}*
📅 ${new Date().toLocaleDateString('es-ES')}

*Análisis del Cabello:*
• Nivel actual: ${hairAnalysis?.natural_level || 'N/A'}
• Canas: ${hairAnalysis?.gray_percentage || 0}%
• Estado: ${
    hairAnalysis?.condition === 'damaged'
      ? 'Dañado'
      : hairAnalysis?.condition === 'very_healthy'
        ? 'Muy saludable'
        : 'Saludable'
  }

*Color Deseado:*
• Nivel: ${desiredColor?.level || 'N/A'}
• Tono: ${desiredColor?.tone || 'N/A'}
• Técnica: ${
    desiredColor?.technique === 'global'
      ? 'Color Global'
      : desiredColor?.technique === 'highlights'
        ? 'Mechas'
        : desiredColor?.technique === 'balayage'
          ? 'Balayage'
          : desiredColor?.technique === 'ombre'
            ? 'Ombré'
            : desiredColor?.technique === 'babylights'
              ? 'Babylights'
              : 'Global'
  }

*Fórmula Aplicada:*
${
  formulation?.formula?.products
    ?.map((p: any) => `• ${p.name} - ${p.amount}${p.unit}`)
    .join('\n') || 'No disponible'
}

*Tiempo de procesamiento:* ${formulation?.formula?.processing_time || 35} minutos

*Precio Total: €${formulation?.suggested_price?.toFixed(2) || '0.00'}*

¡Gracias por tu visita! 💕
`.trim();

  return message;
};
