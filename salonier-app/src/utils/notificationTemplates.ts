import { format } from 'date-fns';
import { es } from 'date-fns/locale';

export interface NotificationTemplate {
  title: string;
  body: string;
  icon?: string;
}

export const notificationTemplates = {
  // Appointment reminders
  appointmentReminder: (clientName: string, minutes: number): NotificationTemplate => ({
    title: '🗓️ Recordatorio de Cita',
    body: `Tienes una cita con ${clientName} en ${minutes} minutos`,
  }),

  appointmentConfirmed: (clientName: string, date: Date): NotificationTemplate => ({
    title: '✅ Cita Confirmada',
    body: `Cita con ${clientName} confirmada para ${format(date, "EEEE d 'de' MMMM 'a las' HH:mm", { locale: es })}`,
  }),

  appointmentCancelled: (clientName: string): NotificationTemplate => ({
    title: '❌ Cita Cancelada',
    body: `La cita con ${clientName} ha sido cancelada`,
  }),

  // Inventory alerts
  lowStock: (productName: string, currentStock: number, unit: string): NotificationTemplate => ({
    title: '📦 Stock Bajo',
    body: `${productName} está por debajo del mínimo (${currentStock} ${unit})`,
  }),

  stockExpiring: (productName: string, daysUntilExpiry: number): NotificationTemplate => ({
    title: '⚠️ Producto Próximo a Vencer',
    body: `${productName} vencerá en ${daysUntilExpiry} días`,
  }),

  // Client care
  retouchReminder: (clientName: string, weeks: number): NotificationTemplate => ({
    title: '💇‍♀️ Retoque Recomendado',
    body: `${clientName} necesita un retoque. Han pasado ${weeks} semanas desde su último servicio`,
  }),

  birthdayReminder: (clientName: string): NotificationTemplate => ({
    title: '🎂 Cumpleaños',
    body: `Hoy es el cumpleaños de ${clientName}. ¡No olvides felicitarle!`,
  }),

  clientMissing: (clientName: string, months: number): NotificationTemplate => ({
    title: '👋 Cliente Inactivo',
    body: `${clientName} no ha visitado en ${months} meses. Considera contactarle`,
  }),

  // Business insights
  dailySummary: (appointments: number, revenue: number): NotificationTemplate => ({
    title: '📊 Resumen del Día',
    body: `Hoy tienes ${appointments} citas programadas. Ingresos estimados: €${revenue.toFixed(2)}`,
  }),

  weeklySummary: (appointments: number, revenue: number, newClients: number): NotificationTemplate => ({
    title: '📈 Resumen Semanal',
    body: `Esta semana: ${appointments} citas, €${revenue.toFixed(2)} en ingresos, ${newClients} nuevos clientes`,
  }),

  goalAchieved: (goalType: string, value: number): NotificationTemplate => ({
    title: '🎯 ¡Meta Alcanzada!',
    body: `Has alcanzado tu meta de ${goalType}: ${value}`,
  }),

  // System notifications
  updateAvailable: (version: string): NotificationTemplate => ({
    title: '🆕 Actualización Disponible',
    body: `Salonier ${version} está disponible. Actualiza para nuevas funciones`,
  }),

  syncCompleted: (): NotificationTemplate => ({
    title: '☁️ Sincronización Completa',
    body: 'Todos tus datos han sido sincronizados correctamente',
  }),

  backupCompleted: (): NotificationTemplate => ({
    title: '💾 Copia de Seguridad',
    body: 'Se ha completado la copia de seguridad de tus datos',
  }),

  // WhatsApp integration
  whatsappSent: (clientName: string): NotificationTemplate => ({
    title: '📱 WhatsApp Enviado',
    body: `Mensaje enviado a ${clientName} exitosamente`,
  }),

  // Formula suggestions
  formulaSaved: (clientName: string): NotificationTemplate => ({
    title: '🎨 Fórmula Guardada',
    body: `Fórmula de color para ${clientName} guardada exitosamente`,
  }),
};

// Helper functions for notification formatting
export const formatNotificationTime = (date: Date): string => {
  return format(date, 'HH:mm', { locale: es });
};

export const formatNotificationDate = (date: Date): string => {
  return format(date, "d 'de' MMMM", { locale: es });
};

export const getNotificationPriority = (type: string): 'high' | 'default' | 'low' => {
  switch (type) {
    case 'appointment':
    case 'lowStock':
      return 'high';
    case 'retouchReminder':
    case 'clientMissing':
      return 'default';
    case 'dailySummary':
    case 'backupCompleted':
      return 'low';
    default:
      return 'default';
  }
};

export const getNotificationSound = (type: string): string | null => {
  switch (type) {
    case 'appointment':
      return 'notification_appointment.wav';
    case 'lowStock':
      return 'notification_alert.wav';
    default:
      return null; // Use default sound
  }
};