// Sistema de validación ligero sin dependencias externas

// Hook para usar validación en formularios
import { useState, useCallback } from 'react';

export type ValidationRule<T> = (value: T) => string | null;

export type ValidationSchema<T> = {
  [K in keyof T]?: ValidationRule<T[K]>[];
}

export type ValidationErrors<T> = {
  [K in keyof T]?: string;
}

// Reglas de validación comunes
export const validators = {
  required: (message = 'Este campo es requerido'): ValidationRule<any> => 
    (value) => {
      if (value === null || value === undefined || value === '') {
        return message;
      }
      if (Array.isArray(value) && value.length === 0) {
        return message;
      }
      return null;
    },

  email: (message = 'Email inválido'): ValidationRule<string> => 
    (value) => {
      if (!value) return null;
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(value) ? null : message;
    },

  phone: (message = 'Teléfono inválido'): ValidationRule<string> => 
    (value) => {
      if (!value) return null;
      const phoneRegex = /^[\d\s\-\+\(\)]+$/;
      return phoneRegex.test(value) && value.length >= 10 ? null : message;
    },

  minLength: (min: number, message?: string): ValidationRule<string> => 
    (value) => {
      if (!value) return null;
      return value.length >= min ? null : message || `Mínimo ${min} caracteres`;
    },

  maxLength: (max: number, message?: string): ValidationRule<string> => 
    (value) => {
      if (!value) return null;
      return value.length <= max ? null : message || `Máximo ${max} caracteres`;
    },

  min: (min: number, message?: string): ValidationRule<number> => 
    (value) => {
      if (value === null || value === undefined) return null;
      return value >= min ? null : message || `Debe ser mayor o igual a ${min}`;
    },

  max: (max: number, message?: string): ValidationRule<number> => 
    (value) => {
      if (value === null || value === undefined) return null;
      return value <= max ? null : message || `Debe ser menor o igual a ${max}`;
    },

  range: (min: number, max: number, message?: string): ValidationRule<number> => 
    (value) => {
      if (value === null || value === undefined) return null;
      return value >= min && value <= max 
        ? null 
        : message || `Debe estar entre ${min} y ${max}`;
    },

  pattern: (regex: RegExp, message = 'Formato inválido'): ValidationRule<string> => 
    (value) => {
      if (!value) return null;
      return regex.test(value) ? null : message;
    },

  custom: <T>(fn: (value: T) => boolean, message: string): ValidationRule<T> => 
    (value) => fn(value) ? null : message,
};

// Función principal de validación
export function validate<T extends Record<string, any>>(
  data: T,
  schema: ValidationSchema<T>
): ValidationErrors<T> {
  const errors: ValidationErrors<T> = {};

  for (const field in schema) {
    const rules = schema[field];
    if (!rules) continue;

    const value = data[field];
    
    for (const rule of rules) {
      const error = rule(value);
      if (error) {
        errors[field] = error;
        break; // Solo mostrar el primer error por campo
      }
    }
  }

  return errors;
}

export function useValidation<T extends Record<string, any>>(
  schema: ValidationSchema<T>
) {
  const [errors, setErrors] = useState<ValidationErrors<T>>({});
  const [touched, setTouched] = useState<Partial<Record<keyof T, boolean>>>({});

  const validateField = useCallback(
    (field: keyof T, value: any) => {
      const rules = schema[field];
      if (!rules) return null;

      for (const rule of rules) {
        const error = rule(value);
        if (error) return error;
      }
      return null;
    },
    [schema]
  );

  const validateForm = useCallback(
    (data: T): boolean => {
      const newErrors = validate(data, schema);
      setErrors(newErrors);
      return Object.keys(newErrors).length === 0;
    },
    [schema]
  );

  const setFieldTouched = useCallback(
    (field: keyof T, isTouched = true) => {
      setTouched(prev => ({ ...prev, [field]: isTouched }));
    },
    []
  );

  const setFieldError = useCallback(
    (field: keyof T, error: string | null) => {
      setErrors(prev => {
        if (error) {
          return { ...prev, [field]: error };
        }
        const { [field]: _, ...rest } = prev;
        return rest as ValidationErrors<T>;
      });
    },
    []
  );

  const clearErrors = useCallback(() => {
    setErrors({});
    setTouched({});
  }, []);

  return {
    errors,
    touched,
    validateField,
    validateForm,
    setFieldTouched,
    setFieldError,
    clearErrors,
    getFieldError: (field: keyof T) => touched[field] ? errors[field] : undefined,
  };
}

// Esquemas de validación para entidades principales
export const clientValidationSchema: ValidationSchema<{
  fullName: string;
  email?: string;
  phone?: string;
}> = {
  fullName: [
    validators.required('El nombre es requerido'),
    validators.minLength(2, 'El nombre debe tener al menos 2 caracteres'),
    validators.maxLength(100, 'El nombre es demasiado largo'),
  ],
  email: [
    (value: string | undefined) => value ? validators.email('Email inválido')(value) : null,
  ],
  phone: [
    (value: string | undefined) => value ? validators.phone('Teléfono inválido')(value) : null,
  ],
};

export const appointmentValidationSchema: ValidationSchema<{
  clientId: string;
  serviceIds: string[];
  startTime: Date;
  duration: number;
}> = {
  clientId: [
    validators.required('Debes seleccionar un cliente'),
  ],
  serviceIds: [
    validators.required('Debes seleccionar al menos un servicio'),
  ],
  startTime: [
    validators.required('La fecha y hora son requeridas'),
    validators.custom(
      (value: Date) => value > new Date(),
      'La cita debe ser en el futuro'
    ),
  ],
  duration: [
    validators.min(15, 'La duración mínima es 15 minutos'),
    validators.max(480, 'La duración máxima es 8 horas'),
  ],
};

export const productValidationSchema: ValidationSchema<{
  name: string;
  code: string;
  size: number;
  currentStock?: number;
  minStock?: number;
  cost?: number;
  salePrice?: number;
}> = {
  name: [
    validators.required('El nombre es requerido'),
    validators.minLength(2),
    validators.maxLength(100),
  ],
  code: [
    validators.required('El código es requerido'),
    validators.pattern(/^[A-Z0-9\-]+$/, 'Solo mayúsculas, números y guiones'),
  ],
  size: [
    validators.required('El tamaño es requerido'),
    validators.min(1, 'El tamaño debe ser mayor a 0'),
  ],
  currentStock: [
    (value: number | undefined) => value !== undefined ? validators.min(0, 'El stock no puede ser negativo')(value) : null,
  ],
  minStock: [
    (value: number | undefined) => value !== undefined ? validators.min(0, 'El stock mínimo no puede ser negativo')(value) : null,
  ],
  cost: [
    (value: number | undefined) => value !== undefined ? validators.min(0, 'El costo no puede ser negativo')(value) : null,
  ],
  salePrice: [
    (value: number | undefined) => value !== undefined ? validators.min(0, 'El precio no puede ser negativo')(value) : null,
    // Note: Cross-field validation should be done at form level
  ],
};

// Sanitización de inputs
export const sanitize = {
  text: (value: string): string => {
    return value.trim().replace(/\s+/g, ' ');
  },

  email: (value: string): string => {
    return value.toLowerCase().trim();
  },

  phone: (value: string): string => {
    return value.replace(/[^\d\+]/g, '');
  },

  number: (value: string): number => {
    const num = parseFloat(value.replace(/[^\d.-]/g, ''));
    return isNaN(num) ? 0 : num;
  },

  html: (value: string): string => {
    // Eliminar tags HTML básicos
    return value.replace(/<[^>]*>/g, '');
  },
};