import {
  User,
  Client,
  Brand,
  Product,
  ProductLine,
  Appointment,
  Service,
  Consultation,
  DashboardMetrics,
  InventoryProduct,
  StockMovement,
  ConversionOption,
  UserPreferences,
  BehaviorMetrics,
} from './improved-types';

export interface AuthResponse {
  user: User;
  session: {
    access_token: string;
    refresh_token: string;
    expires_at: number;
  };
}

export interface SignUpMetadata {
  full_name?: string;
  business_name?: string;
  phone?: string;
}

export interface ProductSearchResult extends Product {
  brand_name: string;
  line_name: string;
}

export interface StockMovementInput {
  productId: string;
  type: 'purchase' | 'sale' | 'adjustment' | 'consumption';
  quantity: number;
  reason?: string;
  notes?: string;
  userId: string;
}

export interface DataService {
  auth: {
    signIn: (email: string, password: string) => Promise<AuthResponse>;
    signUp: (email: string, password: string, metadata?: SignUpMetadata) => Promise<AuthResponse>;
    signOut: () => Promise<void>;
    getCurrentUser: () => Promise<User | null>;
    getSession: () => Promise<AuthResponse['session'] | null>;
  };
  clients: {
    getAll: (userId: string) => Promise<Client[]>;
    getById: (id: string) => Promise<Client | null>;
    create: (client: Omit<Client, 'id' | 'created_at' | 'updated_at'>) => Promise<Client>;
    update: (id: string, updates: Partial<Client>) => Promise<Client>;
    delete: (id: string) => Promise<void>;
    searchByName: (query: string) => Promise<Client[]>;
    getUpcomingRetouch: (userId: string) => Promise<Client[]>;
  };
  brands: {
    getAll: () => Promise<Brand[]>;
    getProductLines: (brandId: string) => Promise<ProductLine[]>;
    getProducts: (productLineId: string) => Promise<Product[]>;
    searchProducts: (query: string) => Promise<ProductSearchResult[]>;
    getConversions: (productId: string) => Promise<ConversionOption[]>;
  };
  appointments: {
    getAll: (userId: string) => Promise<Appointment[]>;
    getByDate: (userId: string, date: Date) => Promise<Appointment[]>;
    create: (appointment: Omit<Appointment, 'id' | 'created_at' | 'updated_at'>) => Promise<Appointment>;
    update: (id: string, updates: Partial<Appointment>) => Promise<Appointment>;
    cancel: (id: string) => Promise<void>;
  };
  services: {
    getAll: (userId: string) => Promise<Service[]>;
    getActive: (userId: string) => Promise<Service[]>;
    create: (service: Omit<Service, 'id' | 'created_at' | 'updated_at'>) => Promise<Service>;
    update: (id: string, updates: Partial<Service>) => Promise<Service>;
  };
  consultations: {
    getAll: (userId: string) => Promise<Consultation[]>;
    getByClient: (clientId: string) => Promise<Consultation[]>;
    create: (consultation: Omit<Consultation, 'id' | 'created_at' | 'updated_at'>) => Promise<Consultation>;
    update: (id: string, updates: Partial<Consultation>) => Promise<Consultation>;
    getLastByClient: (clientId: string) => Promise<Consultation | null>;
    getHistory: (clientId: string, limit?: number) => Promise<Consultation[]>;
    updateResult: (id: string, result: Partial<Consultation>) => Promise<Consultation>;
  };
  metrics: {
    getDashboard: (userId: string) => Promise<DashboardMetrics>;
  };
  inventory: {
    getProducts: (userId: string) => Promise<InventoryProduct[]>;
    getProductById: (id: string) => Promise<InventoryProduct | null>;
    createProduct: (product: Omit<InventoryProduct, 'id' | 'created_at' | 'updated_at'>) => Promise<InventoryProduct>;
    updateProduct: (id: string, updates: Partial<InventoryProduct>) => Promise<InventoryProduct>;
    deleteProduct: (id: string) => Promise<void>;
    addStockMovement: (movement: StockMovementInput) => Promise<StockMovement>;
    getStockMovements: (productId?: string) => Promise<StockMovement[]>;
    getLowStockProducts: (userId: string) => Promise<InventoryProduct[]>;
  };
  users: {
    getById: (id: string) => Promise<User | null>;
    update: (id: string, updates: Partial<User>) => Promise<User>;
    updateBehaviorMetrics: (id: string, metrics: Partial<BehaviorMetrics>) => Promise<void>;
    updatePreferences: (id: string, preferences: Partial<UserPreferences>) => Promise<User>;
  };
}