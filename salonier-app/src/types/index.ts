// Tipos principales de la aplicación Salonier

export interface User {
  id: string;
  email: string;
  full_name: string;
  phone?: string;
  profile_image?: string;
  role: 'stylist' | 'salon_owner' | 'admin';
  salon_name?: string;
  specialties?: string[];
  preferred_brands?: Brand[];
  custom_services?: Service[];
  business_hours?: BusinessHours;
  notification_preferences?: NotificationPreferences;
  inventory_level?: 'none' | 'smart_cost' | 'full_control';
  product_pricing?: ProductPricing[];
  behavior_metrics?: UserBehaviorMetrics;
  created_at: string;
  updated_at: string;
}

export interface BusinessHours {
  monday: DaySchedule;
  tuesday: DaySchedule;
  wednesday: DaySchedule;
  thursday: DaySchedule;
  friday: DaySchedule;
  saturday: DaySchedule;
  sunday: DaySchedule;
  buffer_time_minutes: number;
}

export interface DaySchedule {
  is_open: boolean;
  open_time?: string; // "09:00"
  close_time?: string; // "19:00"
  break_start?: string;
  break_end?: string;
}

export interface NotificationPreferences {
  appointment_reminder: boolean;
  reminder_time_minutes: number; // minutes before appointment
  low_stock_alert: boolean;
  client_retouch_reminder: boolean;
  notification_method: 'push' | 'email' | 'sms' | 'whatsapp';
}

export interface Client {
  id: string;
  user_id: string;
  full_name: string;
  name?: string; // Alias for compatibility
  phone?: string;
  email?: string;
  allergies?: string[];
  preferences?: string;
  notes?: string; // Client notes
  lastVisit?: string;
  last_visit?: string;
  next_appointment?: string;
  // Historial de color
  hair_characteristics?: HairCharacteristics;
  color_history?: ColorHistory;
  // Protocolo de seguridad
  safety_protocol?: SafetyProtocol;
  created_at: string;
  updated_at: string;
}

export interface SafetyProtocol {
  last_allergy_test?: string; // Fecha del último test
  allergy_test_valid_until?: string; // Válido por 6 meses
  last_consent_signed?: string; // Fecha del último consentimiento
  consent_valid_until?: string; // Válido por 1 año
  digital_signature?: string; // Firma digital base64
  known_allergies?: string[];
  special_considerations?: string;
}

export interface HairAnalysis {
  id: string;
  consultation_id: string;
  natural_level: number; // 1-10 con decimales
  undertone: 'warm' | 'cool' | 'neutral';
  gray_percentage: number;
  porosity: 'low' | 'medium' | 'high';
  density: 'thin' | 'medium' | 'thick';
  condition: 'healthy' | 'damaged' | 'very_healthy';
  previous_treatments: string[];
  // Nuevos campos para análisis mejorado
  underlying_pigment?: 'red' | 'red-orange' | 'orange' | 'yellow' | 'pale-yellow';
  white_hair_distribution?: 'none' | 'scattered' | 'concentrated' | 'patches';
  white_hair_texture?: 'same' | 'coarser' | 'finer';
  damage_level?: {
    roots: number; // 0-10
    mids: number;
    ends: number;
  };
  last_chemical_service?: {
    type: 'color' | 'bleach' | 'perm' | 'straightening' | 'none';
    weeks_ago: number;
    products_used?: string;
  };
  zone_analysis?: {
    roots: {
      level: number;
      porosity: 'low' | 'medium' | 'high';
      condition: 'damaged' | 'healthy' | 'very_healthy';
      grayPercentage: number;
    };
    mids: {
      level: number;
      porosity: 'low' | 'medium' | 'high';
      condition: 'damaged' | 'healthy' | 'very_healthy';
      grayPercentage: number;
    };
    ends: {
      level: number;
      porosity: 'low' | 'medium' | 'high';
      condition: 'damaged' | 'healthy' | 'very_healthy';
      grayPercentage: number;
    };
  };
  created_at: string;
}

export interface ColorFormulation {
  id: string;
  consultation_id: string;
  brand_id?: string;
  product_line_id?: string;
  formula: {
    products: {
      product_id: string;
      name: string;
      amount: number;
      unit: 'ml' | 'g';
    }[];
    developer_volume: number;
    processing_time: number;
    technique: string;
  };
  developer_volume?: number; // Duplicate for compatibility
  processing_time?: number; // Duplicate for compatibility
  technique?: string; // Duplicate for compatibility
  notes?: string;
  total_cost: number;
  suggested_price: number;
  created_at: string;
}

export interface Consultation {
  id: string;
  user_id: string;
  client_id: string;
  status: 'in_progress' | 'completed' | 'cancelled';
  current_hair_analysis?: HairAnalysis;
  desired_result?: string;
  formulation?: ColorFormulation;
  before_photos?: string[];
  after_photos?: string[];
  notes?: string;
  total_cost?: number;
  final_price?: number;
  // Referencias al historial
  previous_consultation_id?: string;
  is_correction?: boolean;
  correction_reason?: string;
  // Resultados post-servicio
  actual_result?: ColorResult;
  client_satisfaction?: 1 | 2 | 3 | 4 | 5;
  follow_up_notes?: string;
  created_at: string;
  completed_at?: string;
}

export interface Brand {
  id: string;
  name: string;
  logo_url?: string;
  country: string;
  is_premium: boolean;
  product_lines?: string[]; // Líneas específicas seleccionadas por el usuario
  created_at: string;
}

export interface ProductLine {
  id: string;
  brand_id: string;
  name: string;
  type: 'permanent' | 'demi_permanent' | 'semi_permanent' | 'bleach' | 'toner';
  ammonia_free: boolean;
  created_at: string;
}

export interface ColorProduct {
  id: string;
  product_line_id: string;
  shade_code: string;
  shade_name: string;
  base_level: number;
  tone_direction: string;
  price_per_unit: number;
  unit_size: number;
  unit_type: 'ml' | 'g';
  created_at: string;
}

export interface Appointment {
  id: string;
  user_id: string;
  client_id: string;
  service_id: string;
  date: string;
  duration_minutes: number;
  status: 'scheduled' | 'confirmed' | 'completed' | 'cancelled';
  notes?: string;
  reminder_sent: boolean;
  created_at: string;
  updated_at: string;
}

export interface Service {
  id: string;
  user_id: string;
  name: string;
  description?: string;
  base_price: number;
  duration_minutes: number;
  category: 'color' | 'cut' | 'treatment' | 'styling';
  color?: string; // Color hex para mostrar en calendario
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface InventoryItem {
  id: string;
  user_id: string;
  product_id: string;
  quantity_available: number;
  minimum_stock: number;
  cost_per_unit: number;
  expiry_date?: string;
  created_at: string;
  updated_at: string;
}

export interface BrandConversion {
  id: string;
  from_product_id: string;
  to_product_id: string;
  confidence_level: number; // 0-100
  notes?: string;
  verified_by_professionals: number;
  created_at: string;
}

// Sistema de inventario
export interface Product {
  id: string;
  name: string;
  brand: string;
  line?: string;
  code: string;
  category: 'dye' | 'developer' | 'treatment' | 'tool';
  current_stock: number;
  min_stock: number;
  unit: 'ml' | 'g' | 'units';
  purchase_price: number;
  sale_price?: number;
  last_purchase_date?: string;
  expiry_date?: string;
  barcode?: string;
  notes?: string;
  created_at?: string;
  updated_at?: string;
}

export interface StockMovement {
  id: string;
  productId: string;
  type: 'purchase' | 'sale' | 'consumption' | 'adjustment' | 'return' | 'damage';
  quantity: number; // positive for incoming, negative for outgoing
  date: string;
  notes?: string;
  cost?: number;
  userId: string;
}

export interface ProductPricing {
  id: string;
  product_name: string;
  brand: string;
  unit_price: number;
  unit_size: number;
  unit_type: 'ml' | 'g';
  category: 'dye' | 'developer' | 'treatment' | 'other';
  last_updated: string;
}

export interface UserBehaviorMetrics {
  consultations_completed: number;
  favorite_products: { name: string; count: number }[];
  average_service_price: number;
  average_margin: number;
  last_suggestion_date?: string;
  dismissed_suggestions: string[];
  feature_usage: {
    inventory_checked: number;
    costs_viewed: number;
    reports_generated: number;
    brand_conversions: number;
  };
  first_consultation_date?: string;
  last_active_date: string;
}

// Nuevos tipos para historial de color
export interface HairCharacteristics {
  // Comportamiento conocido del cabello
  lifts_easily: boolean;
  resistant_to_color: boolean;
  tends_to_pull: 'warm' | 'cool' | 'neutral' | 'green';
  average_processing_time: number; // minutos
  preferred_developer_volume: number;
  // Sensibilidades conocidas
  scalp_sensitivity: 'normal' | 'sensitive' | 'very_sensitive';
  ammonia_sensitive: boolean;
  ppd_sensitive: boolean;
  last_patch_test?: string;
  other_sensitivities?: string[];
}

export interface ColorHistory {
  // Resumen rápido
  total_colorations: number;
  last_coloration_date?: string;
  favorite_shades: string[];
  // Historial de problemas
  past_corrections: number;
  common_issues: string[];
  // Patrones de mantenimiento
  average_weeks_between_services: number;
  typical_service_type: 'roots' | 'full' | 'highlights' | 'correction';
}

export interface ColorResult {
  // Resultado real obtenido
  achieved_level: number;
  achieved_tone: string;
  evenness: 'perfect' | 'good' | 'uneven';
  client_feedback: string;
  stylist_notes: string;
  // Comparación con objetivo
  matches_target: boolean;
  variation_notes?: string;
  // Recomendaciones para próxima vez
  next_session_suggestions?: string;
}