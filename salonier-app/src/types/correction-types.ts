// Types específicos para el módulo de corrección de color
export interface CorrectionData {
  problem: 'orange' | 'green' | 'uneven' | 'too_dark' | 'too_light' | 'other';
  problemDescription: string;
  lastColorDate: string;
  productsUsed: string;
  processingTime: string;
  bleachingDone: boolean;
}

export interface CorrectionAIRequest {
  problemType: 'orange' | 'green' | 'uneven' | 'too_dark' | 'too_light' | 'other';
  currentAnalysis: any; // HairAnalysis
  correctionData: CorrectionData;
  userBrands: string[];
  targetResult?: string;
}

export interface CorrectionFormulation {
  mainProducts: {
    product_id: string;
    name: string;
    amount: number;
    unit: 'ml' | 'g';
    purpose: 'neutralization' | 'base' | 'treatment' | 'additive';
  }[];
  neutralizingTone: {
    color: string;
    theory: string;
    level: number;
  };
  processingTime: number;
  technique: 'global' | 'zones' | 'selective';
  precautions: string[];
  expectedResult: string;
  sessionsNeeded: number;
  maintenanceProducts: string[];
}

export interface CorrectionDiagnosis {
  severity: 'mild' | 'moderate' | 'severe';
  canCorrectInOneSession: boolean;
  recommendedApproach: string;
  risks: string[];
  alternativeOptions: string[];
}