import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';
// import { Consultation } from './improved-types';

export type RootStackParamList = {
  Main: undefined;
  ConsultationFlow: { isCorrection?: boolean };
  ClientDetail: { clientId: string };
  BrandConverter: undefined;
  StockMovementHistory: undefined;
};

export type MainTabParamList = {
  Dashboard: undefined;
  Clients: undefined;
  Appointments: undefined;
  Inventory: undefined;
  Settings: undefined;
};

export type AppNavigationProp = StackNavigationProp<RootStackParamList>;
export type AppRouteProp<T extends keyof RootStackParamList> = RouteProp<RootStackParamList, T>;

export interface NavigationProps {
  navigation: AppNavigationProp;
  route?: AppRouteProp<keyof RootStackParamList>;
}