// Common types used across the application

export type UnknownError = unknown;

export interface ApiError extends Error {
  code?: string;
  statusCode?: number;
  details?: unknown;
}

export type ErrorCallback = (error: UnknownError) => void;
export type AsyncErrorCallback = (error: UnknownError) => Promise<void>;

export interface FormValues {
  [key: string]: string | number | boolean | string[] | undefined;
}

export interface ValidationRule {
  value?: RegExp | number | boolean;
  message: string;
}

export interface ValidationRules {
  required?: boolean | string;
  minLength?: number | ValidationRule;
  maxLength?: number | ValidationRule;
  pattern?: RegExp | ValidationRule;
  validate?: (value: unknown) => boolean | string;
}

export interface SelectOption<T = string> {
  label: string;
  value: T;
}

export interface Dimensions {
  width: number;
  height: number;
}

export interface ImageData {
  uri: string;
  width?: number;
  height?: number;
  base64?: string;
}

export type AsyncFunction<T = void> = () => Promise<T>;
export type VoidFunction = () => void;