import React, { useEffect, useState } from 'react';
import { View, StyleSheet, SafeAreaView, TouchableOpacity, Text } from 'react-native';
import { COLORS, SPACING, FONT_SIZES } from '../constants';
import { ConsultationProvider, useConsultation, ConsultationStep } from '../hooks/useConsultation';
import StepIndicator from '../components/colorist/StepIndicator';
import * as Haptics from 'expo-haptics';
import StepTransition from '../components/colorist/StepTransition';
import Toast from 'react-native-toast-message';

// Import de los pasos
import SelectClientStep from '../components/colorist/steps/SelectClientStep';
import SafetyCheckStep from '../components/colorist/steps/SafetyCheckStep';
import HairAnalysisStep from '../components/colorist/steps/HairAnalysisStep';
import DesiredColorStep from '../components/colorist/steps/DesiredColorStep';
import CorrectionGoalStep from '../components/colorist/steps/CorrectionGoalStep';
import TechniqueSelectionStep from '../components/colorist/steps/TechniqueSelectionStep';
import FormulationStep from '../components/colorist/steps/FormulationStep';
import BrandConversionStep from '../components/colorist/steps/BrandConversionStep';
import CompletionStep from '../components/colorist/steps/CompletionStep';

// Componente interno que usa el contexto
function ConsultationFlowContent({ navigation, route }: any) {
  const {
    state,
    nextStep,
    previousStep,
    canProceed,
    getElapsedTime,
    resetConsultation,
    setTechnique,
  } = useConsultation();

  const [elapsedTime, setElapsedTime] = useState(0);
  const isCorrection = route?.params?.isCorrection || false;

  // Establecer técnica si es corrección
  useEffect(() => {
    if (isCorrection && state.technique !== 'correction') {
      setTechnique('correction');
    }
  }, [isCorrection, state.technique, setTechnique]);

  // Actualizar tiempo cada segundo
  useEffect(() => {
    const interval = setInterval(() => {
      setElapsedTime(getElapsedTime());
    }, 1000);
    return () => clearInterval(interval);
  }, [getElapsedTime]);

  // Manejar botón de retroceso
  const handleBack = () => {
    if (state.currentStep === ConsultationStep.SELECT_CLIENT) {
      Toast.show({
        type: 'warning',
        text1: 'Cancelar Consulta',
        text2: '¿Estás seguro? Los cambios no guardados se perderán.',
        position: 'top',
        visibilityTime: 4000,
        onPress: () => {
          Toast.hide();
          resetConsultation();
          navigation.goBack();
        },
      });
    } else {
      previousStep();
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  };

  // Renderizar el paso actual
  const renderCurrentStep = () => {
    // Para corrección, determinar si mostrar CorrectionGoal o DesiredColor
    const showCorrectionGoal =
      isCorrection &&
      state.currentStep === ConsultationStep.DESIRED_COLOR &&
      !state.desiredColor?.correctionGoal;

    const showDesiredColor =
      state.currentStep === ConsultationStep.DESIRED_COLOR &&
      (!isCorrection || (isCorrection && state.desiredColor?.correctionGoal));

    return (
      <>
        <StepTransition active={state.currentStep === ConsultationStep.SELECT_CLIENT}>
          <SelectClientStep />
        </StepTransition>

        <StepTransition active={state.currentStep === ConsultationStep.SAFETY_CHECK}>
          <SafetyCheckStep />
        </StepTransition>

        <StepTransition active={state.currentStep === ConsultationStep.HAIR_ANALYSIS}>
          <HairAnalysisStep />
        </StepTransition>

        <StepTransition active={showCorrectionGoal}>
          <CorrectionGoalStep />
        </StepTransition>

        <StepTransition active={showDesiredColor}>
          <DesiredColorStep />
        </StepTransition>

        <StepTransition active={state.currentStep === ConsultationStep.TECHNIQUE_SELECTION}>
          <TechniqueSelectionStep />
        </StepTransition>

        <StepTransition active={state.currentStep === ConsultationStep.FORMULATION}>
          <FormulationStep />
        </StepTransition>

        <StepTransition active={state.currentStep === ConsultationStep.BRAND_CONVERSION}>
          <BrandConversionStep />
        </StepTransition>

        <StepTransition active={state.currentStep === ConsultationStep.COMPLETION}>
          <CompletionStep navigation={navigation} />
        </StepTransition>
      </>
    );
  };

  // Texto del botón según el paso
  const getButtonText = () => {
    switch (state.currentStep) {
      case ConsultationStep.COMPLETION:
        return 'Finalizar';
      case ConsultationStep.BRAND_CONVERSION:
        return 'Confirmar Selección';
      default:
        return 'Siguiente';
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Indicador de progreso */}
      <StepIndicator currentStep={state.currentStep} elapsedTime={elapsedTime} />

      {/* Contenido del paso actual */}
      <View style={styles.content}>{renderCurrentStep()}</View>

      {/* Botones de navegación */}
      <View style={styles.navigationContainer}>
        <TouchableOpacity style={styles.backButton} onPress={handleBack}>
          <Text style={styles.backButtonText}>
            {state.currentStep === ConsultationStep.SELECT_CLIENT ? 'Cancelar' : 'Atrás'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.nextButton, !canProceed() && styles.disabledButton]}
          onPress={() => {
            if (canProceed()) {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
              nextStep();
            }
          }}
          disabled={!canProceed()}
        >
          <Text style={styles.nextButtonText}>{getButtonText()}</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

// Componente principal con Provider
export default function ConsultationFlowScreen({ navigation, route }: any) {
  return (
    <ConsultationProvider>
      <ConsultationFlowContent navigation={navigation} route={route} />
    </ConsultationProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  content: {
    flex: 1,
  },
  navigationContainer: {
    flexDirection: 'row',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    backgroundColor: COLORS.white,
    borderTopWidth: 1,
    borderTopColor: COLORS.gray[200],
    gap: SPACING.md,
  },
  backButton: {
    flex: 1,
    paddingVertical: SPACING.md,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: COLORS.gray[300],
    alignItems: 'center',
  },
  backButtonText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[700],
    fontWeight: '500',
  },
  nextButton: {
    flex: 2,
    backgroundColor: COLORS.primary,
    paddingVertical: SPACING.md,
    borderRadius: 12,
    alignItems: 'center',
  },
  disabledButton: {
    backgroundColor: COLORS.gray[300],
  },
  nextButtonText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.white,
    fontWeight: '600',
  },
});
