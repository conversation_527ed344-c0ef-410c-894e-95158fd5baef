import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TextInput,
  TouchableOpacity,
  SafeAreaView,
  // ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { COLORS, SPACING, FONT_SIZES, BORDER_RADIUS, SHADOWS } from '../constants';
import { COMPONENTS, TYPOGRAPHY } from '../constants/design-system';
import { dataService } from '../services/dataService';
import { Client } from '../types';
import Card from '../components/common/Card';
import { CreateClientModal } from '../components/clients/CreateClientModal';
import { ErrorMessage } from '../components/common/ErrorMessage';
import { SkeletonList } from '../components/common/SkeletonLoader';
import { useErrorHandler } from '../hooks/useErrorHandler';
import { EmptyClients, EmptySearchResults } from '../components/common/EmptyState';

export default function ClientsScreen({ navigation }: any) {
  const [clients, setClients] = useState<Client[]>([]);
  const [filteredClients, setFilteredClients] = useState<Client[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const { error, execute, clearError } = useErrorHandler();

  useEffect(() => {
    loadClients();
  }, []);

  useEffect(() => {
    filterClients();
  }, [searchQuery, clients]);

  const loadClients = async () => {
    await execute(async () => {
      const clientsData = await dataService.clients.getAll('1');
      if (clientsData) {
        setClients(clientsData as any);
        setFilteredClients(clientsData as any);
      }
      return clientsData;
    });
    setLoading(false);
    setRefreshing(false);
  };

  const filterClients = () => {
    if (searchQuery.trim()) {
      const filtered = clients.filter(
        client =>
          client.full_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          client.phone?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          client.email?.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredClients(filtered);
    } else {
      setFilteredClients(clients);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadClients();
  };

  const handleClientCreated = (newClient: Client) => {
    setClients([newClient, ...clients]);
    setFilteredClients([newClient, ...filteredClients]);
  };

  const renderClient = ({ item }: { item: Client }) => {
    const hasAllergies = item.allergies && item.allergies.length > 0;
    const lastVisitDate = item.last_visit ? new Date(item.last_visit) : null;
    const needsRetouch =
      lastVisitDate && lastVisitDate < new Date(Date.now() - 42 * 24 * 60 * 60 * 1000);

    // Generate avatar initials
    const initials = item.full_name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);

    return (
      <TouchableOpacity
        onPress={() => navigation.navigate('ClientDetail', { client: item })}
        style={styles.clientItem}
      >
        <View style={[COMPONENTS.listItem.base, styles.clientItemContent]}>
          {/* Avatar */}
          <View style={[COMPONENTS.avatar.medium, styles.avatar]}>
            <Text style={styles.avatarText}>{initials}</Text>
          </View>

          {/* Client Info */}
          <View style={styles.clientInfo}>
            <Text style={styles.clientName}>{item.full_name}</Text>
            <View style={styles.clientMeta}>
              {item.phone && <Text style={styles.clientMetaText}>{item.phone}</Text>}
              {item.phone && item.email && <Text style={styles.metaSeparator}>•</Text>}
              {item.email && (
                <Text style={styles.clientMetaText} numberOfLines={1}>
                  {item.email}
                </Text>
              )}
            </View>
            {lastVisitDate && (
              <Text style={styles.lastVisit}>
                Última visita: {lastVisitDate.toLocaleDateString('es-ES')}
              </Text>
            )}
          </View>

          {/* Tags */}
          <View style={styles.rightSection}>
            {(hasAllergies || needsRetouch) && (
              <View style={styles.tagsContainer}>
                {hasAllergies && (
                  <View style={[styles.tag, styles.allergyTag]}>
                    <MaterialCommunityIcons name="alert" size={12} color={COLORS.warning} />
                  </View>
                )}
                {needsRetouch && (
                  <View style={[styles.tag, styles.retouchTag]}>
                    <MaterialCommunityIcons name="bell" size={12} color={COLORS.info} />
                  </View>
                )}
              </View>
            )}
            <MaterialCommunityIcons name="chevron-right" size={20} color={COLORS.gray[400]} />
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  if (loading) {
    return <SkeletonList count={8} />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={[COMPONENTS.header.base, styles.header]}>
        <Text style={[COMPONENTS.header.title, styles.title]}>Clientes</Text>
        <TouchableOpacity style={styles.addButton} onPress={() => setShowCreateModal(true)}>
          <MaterialCommunityIcons name="plus" size={24} color={COLORS.white} />
        </TouchableOpacity>
      </View>

      {error && (
        <ErrorMessage
          message="Error al cargar los clientes"
          type="error"
          onRetry={loadClients}
          onDismiss={clearError}
        />
      )}

      <View style={[COMPONENTS.input.base, styles.searchContainer]}>
        <MaterialCommunityIcons
          name="magnify"
          size={20}
          color={COLORS.gray[400]}
          style={styles.searchIcon}
        />
        <TextInput
          style={styles.searchInput}
          placeholder="Buscar por nombre, teléfono o email..."
          placeholderTextColor={COLORS.gray[400]}
          value={searchQuery}
          onChangeText={setSearchQuery}
          autoCapitalize="none"
          autoCorrect={false}
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <MaterialCommunityIcons name="close-circle" size={20} color={COLORS.gray[400]} />
          </TouchableOpacity>
        )}
      </View>

      {!error && (
        <FlatList
          data={filteredClients}
          renderItem={renderClient}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.listContent}
          refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
          ListEmptyComponent={
            searchQuery ? (
              <EmptySearchResults searchQuery={searchQuery} />
            ) : (
              <EmptyClients onAddClient={() => setShowCreateModal(true)} />
            )
          }
        />
      )}

      <CreateClientModal
        visible={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onClientCreated={handleClientCreated}
        userId="1"
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    marginBottom: SPACING.sm,
  },
  title: {
    letterSpacing: TYPOGRAPHY.letterSpacing.tight,
  },
  addButton: {
    backgroundColor: COLORS.secondary,
    width: 56,
    height: 56,
    borderRadius: BORDER_RADIUS.full,
    justifyContent: 'center',
    alignItems: 'center',
    ...SHADOWS.lg,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: SPACING.md,
    marginBottom: SPACING.md,
    borderWidth: 0,
  },
  searchIcon: {
    marginRight: SPACING.sm,
  },
  searchInput: {
    flex: 1,
    fontSize: TYPOGRAPHY.size.base,
    color: COLORS.text,
    paddingVertical: 0,
  },
  listContent: {
    paddingBottom: SPACING.xl,
  },
  clientItem: {
    marginBottom: 1,
    backgroundColor: COLORS.white,
  },
  clientItemContent: {
    paddingHorizontal: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray[100],
  },
  avatar: {
    backgroundColor: COLORS.accent,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    fontSize: TYPOGRAPHY.size.lg,
    fontWeight: '600' as const,
    color: COLORS.primary,
    letterSpacing: TYPOGRAPHY.letterSpacing.tight,
  },
  clientInfo: {
    flex: 1,
    justifyContent: 'center',
  },
  clientName: {
    fontSize: TYPOGRAPHY.size.base,
    fontWeight: '500' as const,
    color: COLORS.text,
    marginBottom: 2,
    letterSpacing: TYPOGRAPHY.letterSpacing.tight,
  },
  clientMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  clientMetaText: {
    fontSize: TYPOGRAPHY.size.sm,
    color: COLORS.textSecondary,
    flexShrink: 1,
  },
  metaSeparator: {
    fontSize: TYPOGRAPHY.size.sm,
    color: COLORS.gray[400],
    marginHorizontal: SPACING.xs,
  },
  lastVisit: {
    fontSize: TYPOGRAPHY.size.xs,
    color: COLORS.textTertiary,
    marginTop: 2,
  },
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.sm,
  },
  tagsContainer: {
    flexDirection: 'row',
    gap: SPACING.xs,
  },
  tag: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  allergyTag: {
    backgroundColor: COLORS.warning + '20',
  },
  retouchTag: {
    backgroundColor: COLORS.info + '20',
  },
  emptyContainer: {
    paddingVertical: SPACING.xxl * 2,
    alignItems: 'center',
  },
  emptyTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: '600',
    color: COLORS.gray[800],
    marginTop: SPACING.lg,
    marginBottom: SPACING.sm,
  },
  emptySubtitle: {
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[500],
    textAlign: 'center',
    paddingHorizontal: SPACING.xl,
  },
});
