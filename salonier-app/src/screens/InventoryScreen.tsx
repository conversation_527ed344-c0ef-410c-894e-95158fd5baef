import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  SafeAreaView,
  FlatList,
} from 'react-native';
import Toast from 'react-native-toast-message';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { COLORS, SPACING, FONT_SIZES, BORDER_RADIUS, SHADOWS } from '../constants';
import { COMPONENTS, TYPOGRAPHY } from '../constants/design-system';
import { colors } from '../constants/theme';
import Card from '../components/common/Card';
import { Product } from '../types';
import { dataService } from '../services/dataService';
import { ProductDetailModal } from '../components/inventory/ProductDetailModal';
import { ShoppingListModal } from '../components/inventory/ShoppingListModal';
import StockMovementModal from '../components/inventory/StockMovementModal';
import { useNotifications } from '../hooks/useNotifications';

// Categorías de productos
const CATEGORIES = [
  { id: 'all', name: 'Todos', icon: 'view-grid' },
  { id: 'dye', name: 'Tintes', icon: 'palette' },
  { id: 'developer', name: 'Oxidantes', icon: 'flask' },
  { id: 'treatment', name: 'Tratamientos', icon: 'hair-dryer' },
  { id: 'tool', name: 'Herramientas', icon: 'toolbox' },
];

export default function InventoryScreen({ navigation }: any) {
  const [products, setProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [showProductModal, setShowProductModal] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [showShoppingList, setShowShoppingList] = useState(false);
  const [showMovementsModal, setShowMovementsModal] = useState(false);
  const [movementProduct, setMovementProduct] = useState<Product | null>(null);
  const { sendLowStockAlert } = useNotifications();

  useEffect(() => {
    loadProducts();
  }, []);

  useEffect(() => {
    filterProducts();
  }, [searchQuery, selectedCategory, products]);

  const loadProducts = async () => {
    try {
      // TODO: Implementar en dataService
      const mockProducts: Product[] = [
        {
          id: '1',
          name: 'Majirel 7.31',
          brand: "L'Oréal",
          line: 'Majirel',
          code: '7.31',
          category: 'dye',
          current_stock: 3,
          min_stock: 5,
          unit: 'units',
          purchase_price: 8.5,
          last_purchase_date: '2025-06-15',
        },
        {
          id: '2',
          name: 'Oxidante 20 Vol',
          brand: "L'Oréal",
          code: '20VOL',
          category: 'developer',
          current_stock: 1500,
          min_stock: 1000,
          unit: 'ml',
          purchase_price: 3.2,
          last_purchase_date: '2025-06-10',
        },
        {
          id: '3',
          name: 'Smartbond Paso 1',
          brand: "L'Oréal",
          line: 'Smartbond',
          code: 'SB1',
          category: 'treatment',
          current_stock: 250,
          min_stock: 500,
          unit: 'ml',
          purchase_price: 25.0,
          last_purchase_date: '2025-05-20',
        },
        {
          id: '4',
          name: 'Koleston Perfect 8/0',
          brand: 'Wella',
          line: 'Koleston Perfect',
          code: '8/0',
          category: 'dye',
          current_stock: 8,
          min_stock: 5,
          unit: 'units',
          purchase_price: 7.8,
          last_purchase_date: '2025-06-20',
        },
      ];
      setProducts(mockProducts);
      setFilteredProducts(mockProducts);

      // Check for low stock products and send notifications
      const userData = await dataService.auth.getCurrentUser();
      if (userData?.notification_preferences?.low_stock_alert) {
        mockProducts.forEach(product => {
          if (product.current_stock <= product.min_stock) {
            sendLowStockAlert(product);
          }
        });
      }
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'No se pudieron cargar los productos',
      });
    }
  };

  const filterProducts = () => {
    let filtered = [...products];

    // Filtrar por categoría
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(p => p.category === selectedCategory);
    }

    // Filtrar por búsqueda
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        p =>
          p.name.toLowerCase().includes(query) ||
          p.brand.toLowerCase().includes(query) ||
          p.code.toLowerCase().includes(query) ||
          (p.line && p.line.toLowerCase().includes(query))
      );
    }

    setFilteredProducts(filtered);
  };

  const getStockStatus = (product: Product): { color: string; text: string; icon: string } => {
    const percentage = (product.current_stock / product.min_stock) * 100;

    if (percentage <= 50) {
      return { color: COLORS.error, text: 'Crítico', icon: 'alert-circle' };
    } else if (percentage <= 100) {
      return { color: COLORS.warning, text: 'Bajo', icon: 'alert' };
    } else {
      return { color: COLORS.success, text: 'Normal', icon: 'check-circle' };
    }
  };

  const getLowStockProducts = () => {
    return products.filter(p => p.current_stock <= p.min_stock);
  };

  const handleQuickUpdate = async (product: Product, change: number) => {
    const newStock = Math.max(0, product.current_stock + change);
    const updatedProducts = products.map(p =>
      p.id === product.id ? { ...p, current_stock: newStock } : p
    );
    setProducts(updatedProducts);

    // Check if product is now low on stock and send notification
    if (newStock <= product.min_stock) {
      const userData = await dataService.auth.getCurrentUser();
      if (userData?.notification_preferences?.low_stock_alert) {
        sendLowStockAlert({ ...product, current_stock: newStock });
      }
    }
    // TODO: Guardar en backend
  };

  const handleSaveProduct = (product: Product) => {
    const existingIndex = products.findIndex(p => p.id === product.id);
    let updatedProducts;

    if (existingIndex >= 0) {
      // Actualizar producto existente
      updatedProducts = [...products];
      updatedProducts[existingIndex] = product;
    } else {
      // Nuevo producto
      updatedProducts = [...products, product];
    }

    setProducts(updatedProducts);
    setShowProductModal(false);
    setSelectedProduct(null);
    // TODO: Guardar en backend
  };

  const handleDeleteProduct = (productId: string) => {
    const updatedProducts = products.filter(p => p.id !== productId);
    setProducts(updatedProducts);
    setShowProductModal(false);
    setSelectedProduct(null);
    // TODO: Eliminar en backend
  };

  const renderProduct = ({ item }: { item: Product }) => {
    const status = getStockStatus(item);
    console.log('Rendering product:', item.name, 'with id:', item.id);

    return (
      <TouchableOpacity
        onPress={() => {
          setSelectedProduct(item);
          setShowProductModal(true);
        }}
      >
        <Card style={[COMPONENTS.card.base, styles.productCard]}>
          <View style={styles.productHeader}>
            <View style={styles.productInfo}>
              <Text style={styles.productName}>{item.name}</Text>
              <Text style={styles.productBrand}>
                {item.brand} {item.line && `• ${item.line}`}
              </Text>
            </View>
            <View style={[styles.statusBadge, { backgroundColor: status.color + '20' }]}>
              <MaterialCommunityIcons name={status.icon as any} size={16} color={status.color} />
              <Text style={[styles.statusText, { color: status.color }]}>{status.text}</Text>
            </View>
          </View>

          <View style={styles.stockInfo}>
            <View style={styles.stockNumbers}>
              <Text style={styles.stockLabel}>Stock actual</Text>
              <Text style={styles.stockValue}>
                {item.current_stock} {item.unit}
              </Text>
            </View>
            <View style={styles.stockNumbers}>
              <Text style={styles.stockLabel}>Mínimo</Text>
              <Text style={styles.stockMin}>
                {item.min_stock} {item.unit}
              </Text>
            </View>
          </View>

          <View style={styles.quickActions}>
            <TouchableOpacity
              style={styles.quickButton}
              onPress={() => handleQuickUpdate(item, -1)}
            >
              <MaterialCommunityIcons name="minus" size={20} color={COLORS.error} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.quickButton} onPress={() => handleQuickUpdate(item, 5)}>
              <Text style={styles.quickButtonText}>+5</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.quickButton}
              onPress={() => handleQuickUpdate(item, 10)}
            >
              <Text style={styles.quickButtonText}>+10</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.quickButton, styles.historyButton]}
              onPress={() => {
                setMovementProduct(item);
                setShowMovementsModal(true);
              }}
            >
              <MaterialCommunityIcons name="clock-outline" size={20} color={COLORS.secondary} />
            </TouchableOpacity>
          </View>
        </Card>
      </TouchableOpacity>
    );
  };

  const lowStockCount = getLowStockProducts().length;

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={[COMPONENTS.header.base, styles.header]}>
        <Text style={[COMPONENTS.header.title, styles.title]}>Inventario</Text>
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => navigation.navigate('StockMovementHistory')}
          >
            <MaterialCommunityIcons name="history" size={24} color={COLORS.gray[700]} />
          </TouchableOpacity>
          <TouchableOpacity style={styles.headerButton} onPress={() => setShowShoppingList(true)}>
            <MaterialCommunityIcons name="cart-outline" size={24} color={COLORS.secondary} />
            {lowStockCount > 0 && (
              <View style={styles.badge}>
                <Text style={styles.badgeText}>{lowStockCount}</Text>
              </View>
            )}
          </TouchableOpacity>
        </View>
      </View>

      {/* Búsqueda */}
      <View style={[COMPONENTS.input.base, styles.searchContainer]}>
        <MaterialCommunityIcons name="magnify" size={20} color={COLORS.gray[400]} />
        <TextInput
          style={styles.searchInput}
          placeholder="Buscar productos..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholderTextColor={COLORS.gray[400]}
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <MaterialCommunityIcons name="close-circle" size={20} color={COLORS.gray[400]} />
          </TouchableOpacity>
        )}
      </View>

      {/* Categorías */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.categoriesContainer}
      >
        {CATEGORIES.map(category => (
          <TouchableOpacity
            key={category.id}
            style={[
              styles.categoryChip,
              selectedCategory === category.id && styles.categoryChipSelected,
            ]}
            onPress={() => setSelectedCategory(category.id)}
          >
            <MaterialCommunityIcons
              name={category.icon as any}
              size={20}
              color={selectedCategory === category.id ? COLORS.white : COLORS.gray[600]}
            />
            <Text
              style={[
                styles.categoryText,
                selectedCategory === category.id && styles.categoryTextSelected,
              ]}
            >
              {category.name}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Lista de productos */}
      <FlatList
        data={filteredProducts}
        renderItem={renderProduct}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.productsList}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyState}>
            <MaterialCommunityIcons name="package-variant" size={64} color={COLORS.gray[300]} />
            <Text style={styles.emptyText}>No se encontraron productos</Text>
          </View>
        }
      />

      {/* FAB para añadir producto */}
      <TouchableOpacity
        style={[COMPONENTS.button.primary, styles.fab]}
        onPress={() => {
          setSelectedProduct(null);
          setShowProductModal(true);
        }}
      >
        <MaterialCommunityIcons name="plus" size={28} color={COLORS.white} />
      </TouchableOpacity>

      {/* Modal de producto */}
      <ProductDetailModal
        visible={showProductModal}
        onClose={() => {
          setShowProductModal(false);
          setSelectedProduct(null);
        }}
        product={selectedProduct}
        onSave={handleSaveProduct}
        onDelete={handleDeleteProduct}
      />

      {/* Modal de lista de compra */}
      <ShoppingListModal
        visible={showShoppingList}
        onClose={() => setShowShoppingList(false)}
        products={products}
        onMarkAsOrdered={productIds => {
          // TODO: Implementar lógica para marcar productos como pedidos
          console.log('Productos marcados como pedidos:', productIds);
        }}
      />

      {/* Modal de movimientos de stock */}
      <StockMovementModal
        visible={showMovementsModal}
        onClose={() => {
          setShowMovementsModal(false);
          setMovementProduct(null);
        }}
        product={movementProduct}
        onMovementAdded={() => {
          loadProducts(); // Recargar productos para actualizar stock
        }}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    marginBottom: SPACING.sm,
  },
  title: {
    letterSpacing: TYPOGRAPHY.letterSpacing.tight,
  },
  headerActions: {
    flexDirection: 'row',
    gap: SPACING.sm,
  },
  headerButton: {
    padding: SPACING.sm,
    position: 'relative',
  },
  badge: {
    position: 'absolute',
    top: 0,
    right: 0,
    backgroundColor: COLORS.error,
    borderRadius: 10,
    width: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  badgeText: {
    color: COLORS.white,
    fontSize: FONT_SIZES.xs,
    fontWeight: 'bold',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: SPACING.md,
    marginBottom: SPACING.sm,
    borderWidth: 0,
  },
  searchInput: {
    flex: 1,
    paddingHorizontal: SPACING.sm,
    fontSize: TYPOGRAPHY.size.base,
    color: COLORS.text,
    paddingVertical: 0,
  },
  categoriesContainer: {
    paddingHorizontal: SPACING.md,
    marginBottom: SPACING.md,
  },
  categoryChip: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.xs,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.full,
    backgroundColor: COLORS.surface,
    marginRight: SPACING.sm,
  },
  categoryChipSelected: {
    backgroundColor: COLORS.secondary,
  },
  categoryText: {
    fontSize: TYPOGRAPHY.size.sm,
    color: COLORS.textSecondary,
    fontWeight: '500' as const,
  },
  categoryTextSelected: {
    color: COLORS.white,
  },
  productsList: {
    paddingHorizontal: SPACING.md,
    paddingBottom: SPACING.xl * 3,
  },
  productCard: {
    marginBottom: SPACING.sm,
  },
  productHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: SPACING.sm,
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    fontSize: TYPOGRAPHY.size.base,
    fontWeight: '600' as const,
    color: COLORS.text,
    letterSpacing: TYPOGRAPHY.letterSpacing.tight,
  },
  productBrand: {
    fontSize: TYPOGRAPHY.size.sm,
    color: COLORS.textSecondary,
    marginTop: 2,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.xs,
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.full,
  },
  statusText: {
    fontSize: TYPOGRAPHY.size.xs,
    fontWeight: '600' as const,
  },
  stockInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SPACING.sm,
    paddingVertical: SPACING.sm,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: COLORS.border,
  },
  stockNumbers: {
    alignItems: 'center',
  },
  stockLabel: {
    fontSize: TYPOGRAPHY.size.xs,
    color: COLORS.textTertiary,
    textTransform: 'uppercase',
    letterSpacing: TYPOGRAPHY.letterSpacing.wide,
  },
  stockValue: {
    fontSize: TYPOGRAPHY.size.lg,
    fontWeight: '700' as const,
    color: COLORS.text,
    marginTop: 2,
  },
  stockMin: {
    fontSize: TYPOGRAPHY.size.lg,
    color: COLORS.textSecondary,
    marginTop: 2,
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: SPACING.xs,
    flexWrap: 'wrap',
  },
  quickButton: {
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.full,
    borderWidth: 1,
    borderColor: COLORS.gray[200],
    backgroundColor: COLORS.white,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 36,
  },
  quickButtonText: {
    fontSize: TYPOGRAPHY.size.sm,
    color: COLORS.secondary,
    fontWeight: '600' as const,
  },
  historyButton: {
    // No extra padding needed
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.xl * 3,
  },
  emptyText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.textLight,
    marginTop: SPACING.md,
  },
  fab: {
    position: 'absolute',
    bottom: SPACING.xl,
    right: SPACING.md,
    width: 56,
    height: 56,
    paddingVertical: 0,
    paddingHorizontal: 0,
    alignItems: 'center',
    justifyContent: 'center',
  },
});
