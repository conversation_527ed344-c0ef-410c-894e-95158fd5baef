import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  FlatList,
} from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { COLORS, SPACING, FONT_SIZES, BORDER_RADIUS, SHADOWS } from '../constants';
import { TYPOGRAPHY, COMPONENTS } from '../constants/design-system';
import Card from '../components/common/Card';
// import { dataService } from '../services/dataService';
import { CreateAppointmentModal } from '../components/appointments/CreateAppointmentModal';

interface Appointment {
  id: string;
  client_id: string;
  client_name: string;
  service_type: string;
  date: string;
  time: string;
  duration: number;
  status: 'scheduled' | 'confirmed' | 'completed' | 'cancelled';
  notes?: string;
  color: string;
}

const DAYS = ['Dom', 'Lun', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>ie', '<PERSON>áb'];
const MONTHS = [
  '<PERSON><PERSON>', 'Febrero', '<PERSON><PERSON>', '<PERSON>bril', '<PERSON>', '<PERSON><PERSON>',
  '<PERSON>', '<PERSON><PERSON><PERSON>', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'
];

const SERVICE_COLORS = {
  'Coloración': '#7C3AED',
  'Corte': '#3B82F6',
  'Tratamiento': '#10B981',
  'Mechas': '#F59E0B',
  'Peinado': '#EC4899',
  'Otros': '#6B7280',
};

export default function AppointmentsScreen({ navigation, route }: any) {
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [viewMode, setViewMode] = useState<'month' | 'week' | 'day'>('month');
  const [prefilledData, setPrefilledData] = useState<any>(null);

  useEffect(() => {
    loadAppointments();
  }, [currentMonth]);

  useEffect(() => {
    // Verificar si venimos desde CompletionStep con datos pre-poblados
    if (route.params?.openCreateModal) {
      setShowCreateModal(true);
      if (route.params?.prefilledData) {
        setPrefilledData(route.params.prefilledData);
        setSelectedDate(new Date(route.params.prefilledData.date));
        setCurrentMonth(new Date(route.params.prefilledData.date));
      }
      // Limpiar los parámetros después de usarlos
      navigation.setParams({ openCreateModal: false, prefilledData: null });
    }
  }, [route.params]);

  const loadAppointments = async () => {
    // Mock data por ahora
    const mockAppointments: Appointment[] = [
      {
        id: '1',
        client_id: '1',
        client_name: 'María García',
        service_type: 'Coloración',
        date: new Date().toISOString().split('T')[0],
        time: '10:00',
        duration: 120,
        status: 'confirmed',
        color: SERVICE_COLORS['Coloración'],
      },
      {
        id: '2',
        client_id: '2',
        client_name: 'Ana López',
        service_type: 'Corte',
        date: new Date().toISOString().split('T')[0],
        time: '12:00',
        duration: 60,
        status: 'scheduled',
        color: SERVICE_COLORS['Corte'],
      },
    ];
    setAppointments(mockAppointments);
  };

  const handleAppointmentCreated = (newAppointment: Appointment) => {
    setAppointments([...appointments, newAppointment]);
    setShowCreateModal(false);
  };

  const getDaysInMonth = (date: Date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();

    const days = [];
    
    // Días del mes anterior
    const prevMonthLastDay = new Date(year, month, 0).getDate();
    for (let i = startingDayOfWeek - 1; i >= 0; i--) {
      days.push({
        day: prevMonthLastDay - i,
        isCurrentMonth: false,
        date: new Date(year, month - 1, prevMonthLastDay - i),
      });
    }

    // Días del mes actual
    for (let i = 1; i <= daysInMonth; i++) {
      days.push({
        day: i,
        isCurrentMonth: true,
        date: new Date(year, month, i),
      });
    }

    // Días del mes siguiente
    const remainingDays = 42 - days.length;
    for (let i = 1; i <= remainingDays; i++) {
      days.push({
        day: i,
        isCurrentMonth: false,
        date: new Date(year, month + 1, i),
      });
    }

    return days;
  };

  const getAppointmentsForDate = (date: Date) => {
    const dateStr = date.toISOString().split('T')[0];
    return appointments.filter(apt => apt.date === dateStr);
  };

  const isToday = (date: Date) => {
    const today = new Date();
    return date.toDateString() === today.toDateString();
  };

  const changeMonth = (direction: number) => {
    const newMonth = new Date(currentMonth);
    newMonth.setMonth(newMonth.getMonth() + direction);
    setCurrentMonth(newMonth);
  };

  const renderCalendarDay = ({ item }: any) => {
    const dayAppointments = getAppointmentsForDate(item.date);
    const isSelected = item.date.toDateString() === selectedDate.toDateString();
    const hasAppointments = dayAppointments.length > 0;

    return (
      <TouchableOpacity
        style={[
          styles.calendarDay,
          !item.isCurrentMonth && styles.otherMonthDay,
          isSelected && styles.selectedDay,
          isToday(item.date) && styles.todayDay,
        ]}
        onPress={() => {
          setSelectedDate(item.date);
          if (hasAppointments) {
            setViewMode('day');
          }
        }}
      >
        <Text style={[
          styles.calendarDayText,
          !item.isCurrentMonth && styles.otherMonthDayText,
          isSelected && styles.selectedDayText,
          isToday(item.date) && styles.todayDayText,
        ]}>
          {item.day}
        </Text>
        {hasAppointments && (
          <View style={styles.appointmentDots}>
            {dayAppointments.slice(0, 3).map((apt, index) => (
              <View
                key={index}
                style={[styles.appointmentDot, { backgroundColor: apt.color }]}
              />
            ))}
          </View>
        )}
      </TouchableOpacity>
    );
  };

  const renderDayView = () => {
    const dayAppointments = getAppointmentsForDate(selectedDate);
    const hours = Array.from({ length: 13 }, (_, i) => i + 8); // 8 AM to 8 PM

    return (
      <ScrollView style={styles.dayViewContainer}>
        <Text style={styles.dayViewTitle}>
          {selectedDate.toLocaleDateString('es-ES', { 
            weekday: 'long', 
            day: 'numeric', 
            month: 'long' 
          })}
        </Text>
        
        {hours.map(hour => (
          <View key={hour} style={styles.hourRow}>
            <Text style={styles.hourText}>{`${hour}:00`}</Text>
            <View style={styles.hourContent}>
              {dayAppointments
                .filter(apt => parseInt(apt.time.split(':')[0]) === hour)
                .map(apt => (
                  <TouchableOpacity
                    key={apt.id}
                    style={[
                      styles.appointmentBlock,
                      { 
                        backgroundColor: apt.color,
                        height: (apt.duration / 60) * 60, // 60px per hour
                      }
                    ]}
                    onPress={() => {}}
                  >
                    <Text style={styles.appointmentTime}>{apt.time}</Text>
                    <Text style={styles.appointmentClient}>{apt.client_name}</Text>
                    <Text style={styles.appointmentService}>{apt.service_type}</Text>
                  </TouchableOpacity>
                ))}
            </View>
          </View>
        ))}
      </ScrollView>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Agenda</Text>
        <View style={styles.headerButtons}>
          <TouchableOpacity
            style={styles.viewModeButton}
            onPress={() => setViewMode(viewMode === 'month' ? 'day' : 'month')}
          >
            <MaterialCommunityIcons 
              name={viewMode === 'month' ? 'calendar-month' : 'calendar-today'} 
              size={24} 
              color={COLORS.primary} 
            />
          </TouchableOpacity>
          <TouchableOpacity 
            style={styles.addButton}
            onPress={() => setShowCreateModal(true)}
          >
            <MaterialCommunityIcons name="plus" size={24} color={COLORS.white} />
          </TouchableOpacity>
        </View>
      </View>

      {viewMode === 'month' ? (
        <>
          {/* Month Navigation */}
          <View style={styles.monthNavigation}>
            <TouchableOpacity onPress={() => changeMonth(-1)}>
              <MaterialCommunityIcons name="chevron-left" size={24} color={COLORS.gray[700]} />
            </TouchableOpacity>
            <Text style={styles.monthText}>
              {MONTHS[currentMonth.getMonth()]} {currentMonth.getFullYear()}
            </Text>
            <TouchableOpacity onPress={() => changeMonth(1)}>
              <MaterialCommunityIcons name="chevron-right" size={24} color={COLORS.gray[700]} />
            </TouchableOpacity>
          </View>

          {/* Days of week */}
          <View style={styles.weekDays}>
            {DAYS.map(day => (
              <Text key={day} style={styles.weekDayText}>{day}</Text>
            ))}
          </View>

          {/* Calendar Grid */}
          <FlatList
            data={getDaysInMonth(currentMonth)}
            renderItem={renderCalendarDay}
            keyExtractor={(_, index) => index.toString()}
            numColumns={7}
            scrollEnabled={false}
            contentContainerStyle={styles.calendarGrid}
          />

          {/* Today's Appointments Summary */}
          <View style={styles.todaySummary}>
            <Text style={styles.todayTitle}>Citas de Hoy</Text>
            {getAppointmentsForDate(new Date()).length === 0 ? (
              <Text style={styles.noAppointmentsText}>No hay citas programadas</Text>
            ) : (
              getAppointmentsForDate(new Date()).map(apt => (
                <Card key={apt.id} style={styles.appointmentCard} elevation="low">
                  <View style={[styles.appointmentIndicator, { backgroundColor: apt.color }]} />
                  <View style={styles.appointmentInfo}>
                    <Text style={styles.appointmentTimeText}>{apt.time}</Text>
                    <Text style={styles.appointmentClientText}>{apt.client_name}</Text>
                    <Text style={styles.appointmentServiceText}>{apt.service_type}</Text>
                  </View>
                  <MaterialCommunityIcons 
                    name="chevron-right" 
                    size={24} 
                    color={COLORS.gray[400]} 
                  />
                </Card>
              ))
            )}
          </View>
        </>
      ) : (
        renderDayView()
      )}

      {/* Modal de creación de citas */}
      <CreateAppointmentModal
        visible={showCreateModal}
        onClose={() => {
          setShowCreateModal(false);
          setPrefilledData(null);
        }}
        onAppointmentCreated={handleAppointmentCreated}
        selectedDate={selectedDate}
        prefilledData={prefilledData}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    ...COMPONENTS.header.base,
    backgroundColor: COLORS.background,
  },
  title: {
    ...COMPONENTS.header.title,
    fontSize: TYPOGRAPHY.size['3xl'],
  },
  headerButtons: {
    flexDirection: 'row',
    gap: SPACING.sm,
  },
  viewModeButton: {
    padding: SPACING.sm,
  },
  addButton: {
    backgroundColor: COLORS.secondary,
    width: 44,
    height: 44,
    borderRadius: BORDER_RADIUS.full,
    justifyContent: 'center',
    alignItems: 'center',
    ...SHADOWS.md,
  },
  monthNavigation: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
  },
  monthText: {
    fontSize: TYPOGRAPHY.size.lg,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.text,
    textTransform: 'capitalize',
  },
  weekDays: {
    flexDirection: 'row',
    paddingHorizontal: SPACING.lg,
    paddingBottom: SPACING.sm,
  },
  weekDayText: {
    flex: 1,
    textAlign: 'center',
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
    fontWeight: '500',
  },
  calendarGrid: {
    paddingHorizontal: SPACING.lg,
  },
  calendarDay: {
    flex: 1,
    aspectRatio: 1,
    justifyContent: 'center',
    alignItems: 'center',
    margin: 2,
    borderRadius: BORDER_RADIUS.lg,
  },
  otherMonthDay: {
    opacity: 0.3,
  },
  selectedDay: {
    backgroundColor: COLORS.secondary,
    borderRadius: BORDER_RADIUS.lg,
  },
  todayDay: {
    backgroundColor: COLORS.surface,
    borderWidth: 2,
    borderColor: COLORS.secondary,
    borderRadius: BORDER_RADIUS.lg,
  },
  calendarDayText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[800],
  },
  otherMonthDayText: {
    color: COLORS.gray[400],
  },
  selectedDayText: {
    color: COLORS.white,
    fontWeight: '600',
  },
  todayDayText: {
    fontWeight: '600',
  },
  appointmentDots: {
    flexDirection: 'row',
    position: 'absolute',
    bottom: 4,
    gap: 2,
  },
  appointmentDot: {
    width: 4,
    height: 4,
    borderRadius: 2,
  },
  todaySummary: {
    flex: 1,
    paddingHorizontal: SPACING.lg,
    paddingTop: SPACING.lg,
  },
  todayTitle: {
    fontSize: TYPOGRAPHY.size.lg,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: SPACING.md,
  },
  noAppointmentsText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[500],
    textAlign: 'center',
    paddingVertical: SPACING.xl,
  },
  appointmentCard: {
    ...COMPONENTS.card.base,
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  appointmentIndicator: {
    width: 4,
    height: '100%',
    borderRadius: 2,
    marginRight: SPACING.md,
  },
  appointmentInfo: {
    flex: 1,
  },
  appointmentTimeText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
    marginBottom: 2,
  },
  appointmentClientText: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 2,
  },
  appointmentServiceText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
  },
  dayViewContainer: {
    flex: 1,
    paddingHorizontal: SPACING.lg,
  },
  dayViewTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: '600',
    color: COLORS.gray[900],
    marginVertical: SPACING.md,
    textTransform: 'capitalize',
  },
  hourRow: {
    flexDirection: 'row',
    height: 60,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray[100],
  },
  hourText: {
    width: 50,
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[500],
    paddingTop: SPACING.xs,
  },
  hourContent: {
    flex: 1,
    position: 'relative',
  },
  appointmentBlock: {
    position: 'absolute',
    left: 0,
    right: 0,
    padding: SPACING.xs,
    margin: 2,
    borderRadius: BORDER_RADIUS.sm,
  },
  appointmentTime: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.white,
    fontWeight: '500',
  },
  appointmentClient: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.white,
    fontWeight: '600',
  },
  appointmentService: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.white,
    opacity: 0.9,
  },
});