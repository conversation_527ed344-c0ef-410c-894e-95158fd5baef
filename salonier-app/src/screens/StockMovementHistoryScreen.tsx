import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  TextInput,
  ScrollView,
  RefreshControl,
} from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { COLORS, SPACING, FONT_SIZES, BORDER_RADIUS, SHADOWS } from '../constants';
import { dataService } from '../services/dataService';
import { StockMovement, InventoryProduct } from '../types/improved-types';
import Card from '../components/common/Card';
import { format, isToday, isYesterday, isThisWeek, isThisMonth, parseISO } from 'date-fns';
import { es } from 'date-fns/locale';

type MovementFilter = 'all' | 'today' | 'week' | 'month';
type MovementTypeFilter =
  | 'all'
  | 'purchase'
  | 'sale'
  | 'consumption'
  | 'adjustment'
  | 'return'
  | 'damage'
  | 'expiry';

interface StockMovementWithProduct extends StockMovement {
  product?: InventoryProduct;
  unit?: string;
  new_stock?: number;
}

export default function StockMovementHistoryScreen({ navigation }: any) {
  const [movements, setMovements] = useState<StockMovementWithProduct[]>([]);
  const [filteredMovements, setFilteredMovements] = useState<StockMovementWithProduct[]>([]);
  const [products, setProducts] = useState<InventoryProduct[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [dateFilter, setDateFilter] = useState<MovementFilter>('all');
  const [typeFilter, setTypeFilter] = useState<MovementTypeFilter>('all');
  const [showFilters, setShowFilters] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [movements, searchQuery, dateFilter, typeFilter]);

  const loadData = async () => {
    try {
      setLoading(true);
      // Load all movements and products
      const [movementsData, productsData] = await Promise.all([
        dataService.inventory.getStockMovements(),
        dataService.inventory.getProducts('1'),
      ]);

      setProducts(productsData);

      // Enhance movements with product data
      const enhancedMovements = movementsData.map((movement: StockMovement) => {
        const product = productsData.find(
          (p: InventoryProduct) => p.id === movement.productId || p.id === movement.product_id
        );
        return {
          ...movement,
          product,
          unit: product?.unitType || 'units',
          new_stock: movement.newStock,
        };
      });

      // Sort by date, newest first
      enhancedMovements.sort((a: StockMovementWithProduct, b: StockMovementWithProduct) => {
        const dateA = a.createdAt || a.created_at || '';
        const dateB = b.createdAt || b.created_at || '';
        return new Date(dateB).getTime() - new Date(dateA).getTime();
      });

      setMovements(enhancedMovements);
    } catch (error) {
      console.error('Error loading stock movements:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const applyFilters = () => {
    let filtered = [...movements];

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        movement =>
          movement.product?.name.toLowerCase().includes(query) ||
          movement.reason?.toLowerCase().includes(query) ||
          movement.product?.code?.toLowerCase().includes(query)
      );
    }

    // Apply date filter
    filtered = filtered.filter(movement => {
      const dateString = movement.createdAt || movement.created_at || '';
      const movementDate = parseISO(dateString);
      switch (dateFilter) {
        case 'today':
          return isToday(movementDate);
        case 'week':
          return isThisWeek(movementDate, { locale: es });
        case 'month':
          return isThisMonth(movementDate);
        default:
          return true;
      }
    });

    // Apply type filter
    if (typeFilter !== 'all') {
      filtered = filtered.filter(movement => movement.type === typeFilter);
    }

    setFilteredMovements(filtered);
  };

  const getMovementIcon = (type: string) => {
    switch (type) {
      case 'purchase':
        return 'package-plus';
      case 'sale':
        return 'cash-register';
      case 'consumption':
        return 'hair-dryer';
      case 'adjustment':
        return 'pencil';
      case 'return':
        return 'undo-variant';
      case 'damage':
        return 'alert-circle';
      case 'expiry':
        return 'clock-alert';
      default:
        return 'swap-horizontal';
    }
  };

  const getMovementColor = (type: string) => {
    switch (type) {
      case 'purchase':
        return COLORS.success;
      case 'sale':
        return COLORS.info;
      case 'consumption':
        return COLORS.warning;
      case 'adjustment':
        return COLORS.gray[600];
      case 'return':
        return COLORS.secondary;
      case 'damage':
        return COLORS.error;
      case 'expiry':
        return COLORS.error;
      default:
        return COLORS.gray[500];
    }
  };

  const getMovementLabel = (type: string) => {
    switch (type) {
      case 'purchase':
        return 'Compra';
      case 'sale':
        return 'Venta';
      case 'consumption':
        return 'Consumo';
      case 'adjustment':
        return 'Ajuste';
      case 'return':
        return 'Devolución';
      case 'damage':
        return 'Daño';
      case 'expiry':
        return 'Caducidad';
      default:
        return type;
    }
  };

  const formatDate = (dateString: string) => {
    const date = parseISO(dateString);
    if (isToday(date)) return 'Hoy';
    if (isYesterday(date)) return 'Ayer';
    return format(date, "d 'de' MMMM", { locale: es });
  };

  const formatTime = (dateString: string) => {
    return format(parseISO(dateString), 'HH:mm');
  };

  const renderMovementItem = ({ item }: { item: StockMovementWithProduct }) => {
    const isPositive = item.quantity > 0;

    return (
      <TouchableOpacity
        onPress={() => {
          if (item.product) {
            navigation.navigate('Inventory', {
              selectedProductId: item.product.id,
            });
          }
        }}
      >
        <Card style={styles.movementCard} elevation="low">
          <View style={styles.movementHeader}>
            <View
              style={[
                styles.iconContainer,
                { backgroundColor: getMovementColor(item.type) + '20' },
              ]}
            >
              <MaterialCommunityIcons
                name={getMovementIcon(item.type)}
                size={24}
                color={getMovementColor(item.type)}
              />
            </View>

            <View style={styles.movementInfo}>
              <Text style={styles.productName}>{item.product?.name || 'Producto eliminado'}</Text>
              <View style={styles.movementDetails}>
                <Text style={styles.movementType}>{getMovementLabel(item.type)}</Text>
                {item.product?.code && (
                  <Text style={styles.productCode}>• {item.product.code}</Text>
                )}
              </View>
            </View>

            <View style={styles.quantityContainer}>
              <Text
                style={[styles.quantity, { color: isPositive ? COLORS.success : COLORS.error }]}
              >
                {isPositive ? '+' : ''}
                {item.quantity} {item.unit || item.product?.unitType || 'units'}
              </Text>
              <Text style={styles.newStock}>Stock: {item.new_stock || item.newStock || 0}</Text>
            </View>
          </View>

          {item.reason && <Text style={styles.reason}>{item.reason}</Text>}

          <View style={styles.movementFooter}>
            <Text style={styles.date}>{formatDate(item.createdAt || item.created_at || '')}</Text>
            <Text style={styles.time}>{formatTime(item.createdAt || item.created_at || '')}</Text>
          </View>
        </Card>
      </TouchableOpacity>
    );
  };

  const renderFilters = () => (
    <View style={styles.filtersContainer}>
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterRow}>
        <TouchableOpacity
          style={[styles.filterChip, dateFilter === 'all' && styles.filterChipActive]}
          onPress={() => setDateFilter('all')}
        >
          <Text
            style={[styles.filterChipText, dateFilter === 'all' && styles.filterChipTextActive]}
          >
            Todos
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.filterChip, dateFilter === 'today' && styles.filterChipActive]}
          onPress={() => setDateFilter('today')}
        >
          <Text
            style={[styles.filterChipText, dateFilter === 'today' && styles.filterChipTextActive]}
          >
            Hoy
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.filterChip, dateFilter === 'week' && styles.filterChipActive]}
          onPress={() => setDateFilter('week')}
        >
          <Text
            style={[styles.filterChipText, dateFilter === 'week' && styles.filterChipTextActive]}
          >
            Esta semana
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.filterChip, dateFilter === 'month' && styles.filterChipActive]}
          onPress={() => setDateFilter('month')}
        >
          <Text
            style={[styles.filterChipText, dateFilter === 'month' && styles.filterChipTextActive]}
          >
            Este mes
          </Text>
        </TouchableOpacity>
      </ScrollView>

      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterRow}>
        <TouchableOpacity
          style={[styles.filterChip, typeFilter === 'all' && styles.filterChipActive]}
          onPress={() => setTypeFilter('all')}
        >
          <Text
            style={[styles.filterChipText, typeFilter === 'all' && styles.filterChipTextActive]}
          >
            Todos los tipos
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.filterChip, typeFilter === 'purchase' && styles.filterChipActive]}
          onPress={() => setTypeFilter('purchase')}
        >
          <MaterialCommunityIcons
            name="package-plus"
            size={16}
            color={typeFilter === 'purchase' ? COLORS.white : COLORS.gray[600]}
          />
          <Text
            style={[
              styles.filterChipText,
              typeFilter === 'purchase' && styles.filterChipTextActive,
            ]}
          >
            Compras
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.filterChip, typeFilter === 'consumption' && styles.filterChipActive]}
          onPress={() => setTypeFilter('consumption')}
        >
          <MaterialCommunityIcons
            name="hair-dryer"
            size={16}
            color={typeFilter === 'consumption' ? COLORS.white : COLORS.gray[600]}
          />
          <Text
            style={[
              styles.filterChipText,
              typeFilter === 'consumption' && styles.filterChipTextActive,
            ]}
          >
            Consumos
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.filterChip, typeFilter === 'adjustment' && styles.filterChipActive]}
          onPress={() => setTypeFilter('adjustment')}
        >
          <MaterialCommunityIcons
            name="pencil"
            size={16}
            color={typeFilter === 'adjustment' ? COLORS.white : COLORS.gray[600]}
          />
          <Text
            style={[
              styles.filterChipText,
              typeFilter === 'adjustment' && styles.filterChipTextActive,
            ]}
          >
            Ajustes
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={COLORS.primary} />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Historial de Movimientos</Text>
        <View style={styles.headerStats}>
          <Text style={styles.statsText}>{filteredMovements.length} movimientos</Text>
        </View>
      </View>

      <View style={styles.searchContainer}>
        <MaterialCommunityIcons
          name="magnify"
          size={20}
          color={COLORS.gray[400]}
          style={styles.searchIcon}
        />
        <TextInput
          style={styles.searchInput}
          placeholder="Buscar por producto o nota..."
          placeholderTextColor={COLORS.gray[400]}
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
        <TouchableOpacity onPress={() => setShowFilters(!showFilters)} style={styles.filterButton}>
          <MaterialCommunityIcons
            name="filter-variant"
            size={20}
            color={showFilters ? COLORS.primary : COLORS.gray[600]}
          />
        </TouchableOpacity>
      </View>

      {showFilters && renderFilters()}

      <FlatList
        data={filteredMovements}
        renderItem={renderMovementItem}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.listContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={() => {
              setRefreshing(true);
              loadData();
            }}
          />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <MaterialCommunityIcons name="history" size={64} color={COLORS.gray[300]} />
            <Text style={styles.emptyText}>No hay movimientos que mostrar</Text>
            <Text style={styles.emptySubtext}>Los movimientos de stock aparecerán aquí</Text>
          </View>
        }
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
  },
  title: {
    fontSize: FONT_SIZES.xxl,
    fontWeight: 'bold',
    color: COLORS.gray[900],
  },
  headerStats: {
    backgroundColor: COLORS.primary + '20',
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.md,
  },
  statsText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.primary,
    fontWeight: '600',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.md,
    backgroundColor: COLORS.white,
    borderRadius: BORDER_RADIUS.lg,
    paddingHorizontal: SPACING.md,
    ...SHADOWS.sm,
  },
  searchIcon: {
    marginRight: SPACING.sm,
  },
  searchInput: {
    flex: 1,
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[900],
    paddingVertical: SPACING.md,
  },
  filterButton: {
    padding: SPACING.xs,
  },
  filtersContainer: {
    paddingHorizontal: SPACING.lg,
    paddingBottom: SPACING.md,
  },
  filterRow: {
    marginBottom: SPACING.sm,
  },
  filterChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    backgroundColor: COLORS.white,
    borderRadius: BORDER_RADIUS.full,
    marginRight: SPACING.sm,
    borderWidth: 1,
    borderColor: COLORS.gray[300],
    gap: SPACING.xs,
  },
  filterChipActive: {
    backgroundColor: COLORS.primary,
    borderColor: COLORS.primary,
  },
  filterChipText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
  },
  filterChipTextActive: {
    color: COLORS.white,
    fontWeight: '600',
  },
  listContent: {
    paddingHorizontal: SPACING.lg,
    paddingBottom: SPACING.xxl,
  },
  movementCard: {
    marginBottom: SPACING.md,
    padding: SPACING.md,
  },
  movementHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: BORDER_RADIUS.lg,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  movementInfo: {
    flex: 1,
  },
  productName: {
    fontSize: FONT_SIZES.md,
    fontWeight: '600',
    color: COLORS.gray[900],
    marginBottom: 2,
  },
  movementDetails: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  movementType: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
  },
  productCode: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[500],
    marginLeft: SPACING.xs,
  },
  quantityContainer: {
    alignItems: 'flex-end',
  },
  quantity: {
    fontSize: FONT_SIZES.lg,
    fontWeight: 'bold',
  },
  newStock: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.gray[600],
    marginTop: 2,
  },
  reason: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[700],
    marginTop: SPACING.sm,
    fontStyle: 'italic',
  },
  movementFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: SPACING.sm,
    paddingTop: SPACING.sm,
    borderTopWidth: 1,
    borderTopColor: COLORS.gray[200],
  },
  date: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.gray[500],
  },
  time: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.gray[500],
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: SPACING.xxl * 2,
  },
  emptyText: {
    fontSize: FONT_SIZES.lg,
    color: COLORS.gray[600],
    marginTop: SPACING.lg,
    fontWeight: '600',
  },
  emptySubtext: {
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[400],
    marginTop: SPACING.xs,
  },
});
