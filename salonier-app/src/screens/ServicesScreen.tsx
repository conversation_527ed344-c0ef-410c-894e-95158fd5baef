import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  TextInput,
  Switch,
} from 'react-native';
import Toast from 'react-native-toast-message';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { COLORS, SPACING, FONT_SIZES, BORDER_RADIUS } from '../constants';
import { TYPOGRAPHY, COMPONENTS, SHADOWS } from '../constants/design-system';
import Card from '../components/common/Card';
import { dataService } from '../services/dataService';
import { Service } from '../types';

const SERVICE_CATEGORIES = [
  { value: 'color', label: 'Coloración', icon: 'palette', color: '#7C3AED' },
  { value: 'cut', label: 'Corte', icon: 'scissors-cutting', color: '#3B82F6' },
  { value: 'treatment', label: 'Tratamiento', icon: 'spa', color: '#10B981' },
  { value: 'styling', label: 'Peinado', icon: 'hair-dryer', color: '#F59E0B' },
];

export default function ServicesScreen({ navigation }: any) {
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [showActiveOnly, setShowActiveOnly] = useState(true);

  useEffect(() => {
    loadServices();
  }, []);

  const loadServices = async () => {
    try {
      setLoading(true);
      // Mock data for now
      const mockServices: Service[] = [
        {
          id: '1',
          user_id: '1',
          name: 'Coloración Global',
          category: 'color',
          duration_minutes: 120,
          base_price: 60,
          is_active: true,
          color: '#7C3AED',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
        {
          id: '2',
          user_id: '1',
          name: 'Mechas',
          category: 'color',
          duration_minutes: 180,
          base_price: 80,
          is_active: true,
          color: '#7C3AED',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
        {
          id: '3',
          user_id: '1',
          name: 'Corte y Peinado',
          category: 'cut',
          duration_minutes: 90,
          base_price: 45,
          is_active: true,
          color: '#3B82F6',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
        {
          id: '4',
          user_id: '1',
          name: 'Tratamiento Keratina',
          category: 'treatment',
          duration_minutes: 120,
          base_price: 80,
          is_active: false,
          color: '#10B981',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
      ];
      setServices(mockServices);
    } catch (error) {
      console.error('Error loading services:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'No se pudieron cargar los servicios',
      });
    } finally {
      setLoading(false);
    }
  };

  const filteredServices = services.filter(service => {
    const matchesSearch = service.name.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = !selectedCategory || service.category === selectedCategory;
    const matchesActive = !showActiveOnly || service.is_active;
    return matchesSearch && matchesCategory && matchesActive;
  });

  const getCategoryInfo = (category: string) => {
    return SERVICE_CATEGORIES.find(cat => cat.value === category);
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours === 0) return `${mins} min`;
    if (mins === 0) return `${hours} h`;
    return `${hours} h ${mins} min`;
  };

  const handleToggleService = async (serviceId: string) => {
    const service = services.find(s => s.id === serviceId);
    if (!service) return;

    try {
      const updatedServices = services.map(s =>
        s.id === serviceId ? { ...s, is_active: !s.is_active } : s
      );
      setServices(updatedServices);
      // TODO: Update in backend
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'No se pudo actualizar el servicio',
      });
    }
  };

  const handleEditService = (service: Service) => {
    navigation.navigate('EditService', { service });
  };

  const renderServiceCard = (service: Service) => {
    const categoryInfo = getCategoryInfo(service.category);

    return (
      <TouchableOpacity
        key={service.id}
        onPress={() => handleEditService(service)}
        activeOpacity={0.7}
      >
        <Card
          style={[styles.serviceCard, !service.is_active && styles.inactiveCard]}
          elevation="low"
        >
          <View style={styles.serviceHeader}>
            <View style={styles.serviceHeaderLeft}>
              <View style={[styles.categoryIcon, { backgroundColor: categoryInfo?.color + '20' }]}>
                <MaterialCommunityIcons
                  name={categoryInfo?.icon as any}
                  size={24}
                  color={categoryInfo?.color}
                />
              </View>
              <View style={styles.serviceInfo}>
                <Text style={[styles.serviceName, !service.is_active && styles.inactiveText]}>
                  {service.name}
                </Text>
                <View style={styles.serviceDetails}>
                  <View style={styles.detailItem}>
                    <MaterialCommunityIcons
                      name="clock-outline"
                      size={14}
                      color={COLORS.textSecondary}
                    />
                    <Text style={styles.detailText}>
                      {formatDuration(service.duration_minutes)}
                    </Text>
                  </View>
                  <View style={styles.detailItem}>
                    <MaterialCommunityIcons
                      name="currency-eur"
                      size={14}
                      color={COLORS.textSecondary}
                    />
                    <Text style={styles.detailText}>{service.base_price}</Text>
                  </View>
                </View>
              </View>
            </View>
            <Switch
              value={service.is_active}
              onValueChange={() => handleToggleService(service.id)}
              trackColor={{ false: COLORS.gray[300], true: COLORS.secondary }}
              thumbColor={COLORS.white}
            />
          </View>
        </Card>
      </TouchableOpacity>
    );
  };

  const renderCategoryFilter = () => (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      style={styles.categoryFilter}
      contentContainerStyle={styles.categoryFilterContent}
    >
      <TouchableOpacity
        style={[styles.categoryChip, !selectedCategory && styles.categoryChipSelected]}
        onPress={() => setSelectedCategory(null)}
      >
        <Text
          style={[styles.categoryChipText, !selectedCategory && styles.categoryChipTextSelected]}
        >
          Todos
        </Text>
      </TouchableOpacity>
      {SERVICE_CATEGORIES.map(category => (
        <TouchableOpacity
          key={category.value}
          style={[
            styles.categoryChip,
            selectedCategory === category.value && styles.categoryChipSelected,
          ]}
          onPress={() => setSelectedCategory(category.value)}
        >
          <MaterialCommunityIcons
            name={category.icon as any}
            size={16}
            color={selectedCategory === category.value ? COLORS.white : COLORS.textSecondary}
          />
          <Text
            style={[
              styles.categoryChipText,
              selectedCategory === category.value && styles.categoryChipTextSelected,
            ]}
          >
            {category.label}
          </Text>
        </TouchableOpacity>
      ))}
    </ScrollView>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Servicios</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => navigation.navigate('CreateService')}
        >
          <MaterialCommunityIcons name="plus" size={24} color={COLORS.white} />
        </TouchableOpacity>
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <MaterialCommunityIcons name="magnify" size={20} color={COLORS.textSecondary} />
        <TextInput
          style={styles.searchInput}
          placeholder="Buscar servicios..."
          placeholderTextColor={COLORS.textSecondary}
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      {/* Category Filter */}
      {renderCategoryFilter()}

      {/* Active Filter */}
      <View style={styles.activeFilter}>
        <Text style={styles.activeFilterText}>Mostrar solo activos</Text>
        <Switch
          value={showActiveOnly}
          onValueChange={setShowActiveOnly}
          trackColor={{ false: COLORS.gray[300], true: COLORS.secondary }}
          thumbColor={COLORS.white}
        />
      </View>

      {/* Services List */}
      <ScrollView
        style={styles.servicesList}
        contentContainerStyle={styles.servicesListContent}
        showsVerticalScrollIndicator={false}
      >
        {filteredServices.length === 0 ? (
          <View style={styles.emptyState}>
            <MaterialCommunityIcons name="scissors-cutting" size={48} color={COLORS.gray[400]} />
            <Text style={styles.emptyText}>No se encontraron servicios</Text>
            <Text style={styles.emptySubtext}>
              {searchQuery ? 'Intenta con otra búsqueda' : 'Añade tus primeros servicios'}
            </Text>
          </View>
        ) : (
          <>
            <Text style={styles.resultsCount}>
              {filteredServices.length} {filteredServices.length === 1 ? 'servicio' : 'servicios'}
            </Text>
            {filteredServices.map(renderServiceCard)}
          </>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    ...COMPONENTS.header.base,
    backgroundColor: COLORS.background,
  },
  title: {
    ...COMPONENTS.header.title,
    fontSize: TYPOGRAPHY.size['3xl'],
  },
  addButton: {
    backgroundColor: COLORS.secondary,
    width: 44,
    height: 44,
    borderRadius: BORDER_RADIUS.full,
    justifyContent: 'center',
    alignItems: 'center',
    ...SHADOWS.md,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.surface,
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.md,
    paddingHorizontal: SPACING.md,
    borderRadius: BORDER_RADIUS.lg,
    height: 48,
  },
  searchInput: {
    flex: 1,
    marginLeft: SPACING.sm,
    fontSize: TYPOGRAPHY.size.base,
    color: COLORS.text,
  },
  categoryFilter: {
    marginBottom: SPACING.md,
  },
  categoryFilterContent: {
    paddingHorizontal: SPACING.lg,
    gap: SPACING.sm,
  },
  categoryChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    backgroundColor: COLORS.surface,
    borderRadius: BORDER_RADIUS.full,
    gap: SPACING.xs,
    borderWidth: 1,
    borderColor: COLORS.gray[200],
  },
  categoryChipSelected: {
    backgroundColor: COLORS.secondary,
    borderColor: COLORS.secondary,
  },
  categoryChipText: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.medium,
    color: COLORS.textSecondary,
  },
  categoryChipTextSelected: {
    color: COLORS.white,
  },
  activeFilter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.lg,
    paddingBottom: SPACING.md,
  },
  activeFilterText: {
    fontSize: TYPOGRAPHY.size.sm,
    color: COLORS.textSecondary,
  },
  servicesList: {
    flex: 1,
  },
  servicesListContent: {
    paddingHorizontal: SPACING.lg,
    paddingBottom: SPACING.xl,
  },
  resultsCount: {
    fontSize: TYPOGRAPHY.size.sm,
    color: COLORS.textSecondary,
    marginBottom: SPACING.sm,
  },
  serviceCard: {
    marginBottom: SPACING.md,
    padding: 0,
  },
  inactiveCard: {
    opacity: 0.7,
  },
  serviceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: SPACING.md,
  },
  serviceHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryIcon: {
    width: 48,
    height: 48,
    borderRadius: BORDER_RADIUS.lg,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: SPACING.md,
  },
  serviceInfo: {
    flex: 1,
  },
  serviceName: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: SPACING.xs,
  },
  inactiveText: {
    color: COLORS.textSecondary,
  },
  serviceDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.md,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.xs,
  },
  detailText: {
    fontSize: TYPOGRAPHY.size.sm,
    color: COLORS.textSecondary,
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.xl * 4,
  },
  emptyText: {
    fontSize: TYPOGRAPHY.size.lg,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.text,
    marginTop: SPACING.md,
  },
  emptySubtext: {
    fontSize: TYPOGRAPHY.size.sm,
    color: COLORS.textSecondary,
    marginTop: SPACING.xs,
  },
});
