import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  Switch,
} from 'react-native';
import Toast from 'react-native-toast-message';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { COLORS, SPACING, FONT_SIZES, BORDER_RADIUS, SHADOWS } from '../constants';
import { TYPOGRAPHY, COMPONENTS } from '../constants/design-system';
import Card from '../components/common/Card';
import { convertColorWithAI } from '../services/colorConversionService';
import { BrandSelector } from '../components/converter/BrandSelector';
import { BrandSelectorModal } from '../components/converter/BrandSelectorModal';
import { dataService } from '../services/dataService';
// import { User } from '../types';

// Marcas principales con sus líneas
const BRANDS_DATA: Record<string, { lines: string[]; description: string }> = {
  "L'Oréal": {
    lines: ['Majirel', 'INOA', 'Dia Light', 'Dia Richesse', 'Cover 5'],
    description: 'Sistema decimal X.YZ',
  },
  Wella: {
    lines: ['Koleston Perfect', 'Illumina Color', 'Color Touch', 'Blondor'],
    description: 'Sistema X/YZ con barra',
  },
  Schwarzkopf: {
    lines: ['Igora Royal', 'Igora Vibrance', 'BlondMe', 'Essensity'],
    description: 'Sistema X-YZ con guión',
  },
  Revlon: {
    lines: ['Revlonissimo', 'Young Color', 'Nutri Color'],
    description: 'Sistema decimal',
  },
  Alfaparf: {
    lines: ['Evolution', 'Color Wear', 'Precious Nature'],
    description: 'Sistema decimal',
  },
  Salerm: {
    lines: ['Salermvison', 'Salerm Color', 'HD Colors'],
    description: 'Sistema decimal',
  },
};

// const QUICK_BRANDS = ["L'Oréal", "Wella", "Schwarzkopf", "Revlon"];

export default function BrandConverterScreen() {
  const [originBrand, setOriginBrand] = useState('');
  const [originLine, setOriginLine] = useState('');
  const [originCode, setOriginCode] = useState('');
  const [targetBrand, setTargetBrand] = useState('');
  const [targetLine, setTargetLine] = useState('');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [recentConversions, setRecentConversions] = useState<any[]>([]);
  const [freeMode, setFreeMode] = useState(false);
  const [freeText, setFreeText] = useState('');
  const [user, setUser] = useState<any>(null);

  // Estados para los modales
  const [showOriginModal, setShowOriginModal] = useState(false);
  const [showOriginLineModal, setShowOriginLineModal] = useState(false);
  const [showTargetModal, setShowTargetModal] = useState(false);
  const [showTargetLineModal, setShowTargetLineModal] = useState(false);

  useEffect(() => {
    loadUserData();
  }, []);

  const loadUserData = async () => {
    try {
      const userData = await dataService.auth.getCurrentUser();
      setUser(userData as any);

      // Pre-seleccionar la primera marca preferida si existe
      if ((userData as any)?.preferredBrandIds && (userData as any).preferredBrandIds.length > 0) {
        const firstPreferredBrandId = (userData as any).preferredBrandIds[0];
        // TODO: Get brand name from ID
        setOriginBrand(firstPreferredBrandId);
        // if (firstPreferredBrandId.preferred_lines && firstPreferredBrandId.preferred_lines.length > 0) {
        //   setOriginLine(firstPreferredBrandId.preferred_lines[0]);
        // }
      }
    } catch (error) {
      console.error('Error loading user data:', error);
    }
  };

  // Función para organizar las marcas con las preferidas primero
  // const getOrganizedBrands = () => {
  //   const allBrands = Object.keys(BRANDS_DATA);
  //   const preferredBrandNames = user?.preferredBrandIds || [];
  //
  //   const preferredBrands = preferredBrandNames.filter(brand => allBrands.includes(brand));
  //   const otherBrands = allBrands.filter(brand => !preferredBrandNames.includes(brand));
  //
  //   return { preferredBrands, otherBrands, allBrands };
  // };

  // Verificar si una marca es preferida
  const isPreferredBrand = (brandName: string) => {
    return user?.preferredBrandIds?.includes(brandName) || false;
  };

  const handleConvertFreeMode = async () => {
    if (!freeText.trim()) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Por favor escribe qué quieres convertir',
        position: 'top',
      });
      return;
    }

    setLoading(true);
    try {
      // Aquí usarías un prompt diferente para modo libre
      const result = await convertColorWithAI({
        originBrand: 'Auto',
        originCode: freeText,
        targetBrand: 'Auto',
      });

      setResult(result);
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'No se pudo procesar tu solicitud',
        position: 'top',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleConvert = async () => {
    if (!originBrand || !originCode || !targetBrand) {
      Toast.show({
        type: 'warning',
        text1: 'Campos Requeridos',
        text2: 'Por favor completa marca origen, código y marca destino',
        position: 'top',
      });
      return;
    }

    setLoading(true);
    try {
      const conversionResult = await convertColorWithAI({
        originBrand,
        originLine,
        originCode,
        targetBrand,
        targetLine: targetLine === 'auto' ? '' : targetLine,
      });

      setResult(conversionResult);

      // Guardar en recientes
      const newRecent = {
        from: `${originBrand} ${originLine} ${originCode}`,
        to: `${targetBrand} ${conversionResult.targetLine}`,
        result: conversionResult,
        timestamp: new Date().toISOString(),
      };
      setRecentConversions([newRecent, ...recentConversions.slice(0, 4)]);
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'No se pudo realizar la conversión. Intenta de nuevo.',
        position: 'top',
      });
      console.error('Conversion error:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadRecentConversion = (recent: any) => {
    // Parsear la conversión reciente y cargar los campos
    const parts = recent.from.split(' ');
    if (parts.length >= 2) {
      setOriginBrand(parts[0]);
      if (parts.length === 3) {
        setOriginLine(parts[1]);
        setOriginCode(parts[2]);
      } else {
        setOriginCode(parts[1]);
      }
    }
    setTargetBrand(recent.to.split(' ')[0]);
    setResult(recent.result);
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView showsVerticalScrollIndicator={false}>
          <View style={styles.header}>
            <Text style={styles.title}>🎨 Conversor de Color</Text>
            <Text style={styles.subtitle}>Convierte entre cualquier marca profesional</Text>

            {/* Switch para modo libre */}
            <View style={styles.modeSwitch}>
              <Text style={styles.modeSwitchText}>Modo Guiado</Text>
              <Switch
                value={freeMode}
                onValueChange={setFreeMode}
                trackColor={{ false: COLORS.gray[300], true: COLORS.secondary }}
                thumbColor={COLORS.white}
              />
              <Text style={styles.modeSwitchText}>Entrada Libre</Text>
            </View>
          </View>

          {freeMode ? (
            // Modo libre
            <Card style={styles.section} elevation="low">
              <Text style={styles.sectionTitle}>Escribe qué necesitas convertir</Text>
              <TextInput
                style={styles.freeInput}
                placeholder="Ej: L'Oréal Majirel 7.31 a Wella"
                placeholderTextColor={COLORS.gray[400]}
                value={freeText}
                onChangeText={setFreeText}
                multiline
                numberOfLines={3}
              />
              <Text style={styles.freeHint}>
                Puedes escribir de forma natural, la IA entenderá tu solicitud
              </Text>
            </Card>
          ) : (
            <>
              {/* SECCIÓN ORIGEN */}
              <Card style={styles.section} elevation="low">
                <Text style={styles.sectionTitle}>¿Qué color tienes?</Text>

                <BrandSelector
                  label="Marca"
                  value={originBrand}
                  line={originLine}
                  placeholder="Seleccionar marca..."
                  onPress={() => setShowOriginModal(true)}
                />

                {originBrand && BRANDS_DATA[originBrand]?.lines?.length > 0 && (
                  <BrandSelector
                    label="Línea (opcional)"
                    value={originLine}
                    placeholder="Seleccionar línea..."
                    onPress={() => setShowOriginLineModal(true)}
                  />
                )}

                <View style={styles.inputGroup}>
                  <Text style={styles.label}>Código del color</Text>
                  <TextInput
                    style={styles.input}
                    placeholder="Ej: 7.31, 6/73, 8-4"
                    value={originCode}
                    onChangeText={setOriginCode}
                    autoCapitalize="characters"
                    autoCorrect={false}
                  />
                </View>
              </Card>

              {/* SECCIÓN DESTINO */}
              <Card style={styles.section} elevation="low">
                <Text style={styles.sectionTitle}>¿A qué marca quieres convertir?</Text>

                <BrandSelector
                  label="Marca destino"
                  value={targetBrand}
                  line={targetLine !== 'auto' ? targetLine : ''}
                  placeholder="Seleccionar marca..."
                  onPress={() => setShowTargetModal(true)}
                  isPreferred={isPreferredBrand(targetBrand)}
                />

                {targetBrand && BRANDS_DATA[targetBrand]?.lines?.length > 0 && (
                  <BrandSelector
                    label="Línea preferida"
                    value={targetLine === 'auto' ? 'Sugerir mejor opción' : targetLine}
                    placeholder="Automático"
                    onPress={() => setShowTargetLineModal(true)}
                  />
                )}
              </Card>
            </>
          )}

          {/* BOTÓN CONVERTIR */}
          <TouchableOpacity
            style={[styles.convertButton, loading && styles.convertButtonDisabled]}
            onPress={freeMode ? handleConvertFreeMode : handleConvert}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator color="#fff" />
            ) : (
              <>
                <MaterialCommunityIcons name="sync" size={24} color="#fff" />
                <Text style={styles.convertButtonText}>CONVERTIR</Text>
              </>
            )}
          </TouchableOpacity>

          {/* RESULTADO */}
          {result && !loading && (
            <Card style={styles.resultCard} elevation="medium">
              <View style={styles.resultHeader}>
                <Text style={styles.resultTitle}>✨ Conversión Lista</Text>
                <View style={styles.confidenceBadge}>
                  <Text style={styles.confidenceText}>{result.confidence}%</Text>
                </View>
              </View>

              <View style={styles.conversionDisplay}>
                <View style={styles.colorBox}>
                  <Text style={styles.brandName}>{originBrand}</Text>
                  {originLine && <Text style={styles.lineName}>{originLine}</Text>}
                  <Text style={styles.colorCode}>{originCode}</Text>
                </View>

                <MaterialCommunityIcons name="arrow-right" size={24} color={COLORS.primary} />

                <View style={styles.colorBox}>
                  <Text style={styles.brandName}>{targetBrand}</Text>
                  <Text style={styles.lineName}>{result.targetLine}</Text>
                  <Text style={styles.colorCode}>{result.conversion}</Text>
                </View>
              </View>

              {result.alternatives && result.alternatives.length > 0 && (
                <View style={styles.alternatives}>
                  <Text style={styles.alternativesTitle}>Otras opciones:</Text>
                  {result.alternatives.map((alt: any, index: number) => (
                    <View key={index} style={styles.alternativeItem}>
                      <Text style={styles.alternativeText}>
                        • {alt.line} {alt.code}
                      </Text>
                      <Text style={styles.alternativeReason}>{alt.reason}</Text>
                    </View>
                  ))}
                </View>
              )}

              {result.adjustments && result.adjustments.length > 0 && (
                <View style={styles.adjustments}>
                  <Text style={styles.adjustmentsTitle}>💡 Recomendaciones:</Text>
                  {result.adjustments.map((adj: string, index: number) => (
                    <Text key={index} style={styles.adjustment}>
                      • {adj}
                    </Text>
                  ))}
                </View>
              )}

              <View style={styles.resultActions}>
                <TouchableOpacity style={styles.actionButton}>
                  <MaterialCommunityIcons name="content-save" size={20} color={COLORS.primary} />
                  <Text style={styles.actionButtonText}>Guardar</Text>
                </TouchableOpacity>
                <TouchableOpacity style={styles.actionButton}>
                  <MaterialCommunityIcons name="share-variant" size={20} color={COLORS.primary} />
                  <Text style={styles.actionButtonText}>Compartir</Text>
                </TouchableOpacity>
              </View>
            </Card>
          )}

          {/* CONVERSIONES RECIENTES */}
          {recentConversions.length > 0 && (
            <View style={styles.recentSection}>
              <Text style={styles.recentTitle}>Conversiones Recientes</Text>
              {recentConversions.map((recent, index) => (
                <TouchableOpacity
                  key={index}
                  style={styles.recentItem}
                  onPress={() => loadRecentConversion(recent)}
                >
                  <Text style={styles.recentText}>{recent.from}</Text>
                  <MaterialCommunityIcons name="arrow-right" size={16} color={COLORS.gray[500]} />
                  <Text style={styles.recentText}>{recent.to}</Text>
                </TouchableOpacity>
              ))}
            </View>
          )}
        </ScrollView>
      </KeyboardAvoidingView>

      {/* Modales de selección */}
      <BrandSelectorModal
        visible={showOriginModal}
        onClose={() => setShowOriginModal(false)}
        onSelect={(selectedBrand, line) => {
          setOriginBrand(selectedBrand);
          if (line) setOriginLine(line);
          setShowOriginModal(false);
        }}
        title="Seleccionar Marca Origen"
        brands={BRANDS_DATA}
        showLines={false}
        allowManual={true}
        currentBrand={originBrand}
        preferredBrands={user?.preferredBrandIds || []}
      />

      <BrandSelectorModal
        visible={showOriginLineModal}
        onClose={() => setShowOriginLineModal(false)}
        onSelect={(_, line) => {
          if (line) setOriginLine(line);
          setShowOriginLineModal(false);
        }}
        title={`Líneas de ${originBrand}`}
        brands={{ [originBrand]: BRANDS_DATA[originBrand] || { lines: [] } }}
        showLines={true}
        allowManual={false}
        currentBrand={originBrand}
        currentLine={originLine}
      />

      <BrandSelectorModal
        visible={showTargetModal}
        onClose={() => setShowTargetModal(false)}
        onSelect={brand => {
          setTargetBrand(brand);
          setTargetLine('auto');
          setShowTargetModal(false);
        }}
        title="Seleccionar Marca Destino"
        brands={BRANDS_DATA}
        showLines={false}
        allowManual={true}
        currentBrand={targetBrand}
        preferredBrands={user?.preferredBrandIds || []}
      />

      <BrandSelectorModal
        visible={showTargetLineModal}
        onClose={() => setShowTargetLineModal(false)}
        onSelect={(_, line) => {
          setTargetLine(line || 'auto');
          setShowTargetLineModal(false);
        }}
        title={`Líneas de ${targetBrand}`}
        brands={{ [targetBrand]: BRANDS_DATA[targetBrand] || { lines: [] } }}
        showLines={true}
        allowManual={false}
        currentBrand={targetBrand}
        currentLine={targetLine}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  keyboardView: {
    flex: 1,
  },
  header: {
    alignItems: 'center',
    paddingVertical: SPACING.lg,
  },
  title: {
    fontSize: TYPOGRAPHY.size['3xl'],
    fontFamily: TYPOGRAPHY.fontFamily.bold,
    fontWeight: '700',
    color: COLORS.text,
    marginBottom: SPACING.xs,
    letterSpacing: TYPOGRAPHY.letterSpacing.tight,
  },
  subtitle: {
    fontSize: TYPOGRAPHY.size.base,
    color: COLORS.textSecondary,
  },
  section: {
    ...COMPONENTS.card.base,
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
  },
  sectionTitle: {
    fontSize: TYPOGRAPHY.size.lg,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: SPACING.md,
  },
  quickBrands: {
    marginBottom: SPACING.md,
    paddingVertical: SPACING.xs,
  },
  brandChip: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.full,
    backgroundColor: COLORS.gray[100],
    marginRight: SPACING.sm,
    borderWidth: 1,
    borderColor: COLORS.gray[200],
  },
  brandChipSelected: {
    backgroundColor: COLORS.primary,
    borderColor: COLORS.primary,
  },
  brandChipText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[700],
    fontWeight: '500',
  },
  brandChipTextSelected: {
    color: COLORS.white,
  },
  inputGroup: {
    marginBottom: SPACING.md,
  },
  label: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.medium,
    fontWeight: '500',
    color: COLORS.text,
    marginBottom: SPACING.xs,
  },
  input: {
    ...COMPONENTS.input.base,
    borderWidth: 1,
    borderColor: COLORS.gray[200],
  },
  pickerContainer: {
    backgroundColor: COLORS.white,
    borderWidth: 1,
    borderColor: COLORS.gray[300],
    borderRadius: BORDER_RADIUS.md,
    overflow: 'hidden',
  },
  picker: {
    height: 50,
  },
  hint: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.gray[500],
    marginTop: SPACING.xs,
  },
  convertButton: {
    ...COMPONENTS.button.primary,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
    gap: SPACING.sm,
    ...SHADOWS.md,
  },
  convertButtonDisabled: {
    opacity: 0.7,
  },
  convertButtonText: {
    fontSize: TYPOGRAPHY.size.lg,
    fontFamily: TYPOGRAPHY.fontFamily.bold,
    fontWeight: '700',
    color: COLORS.white,
    letterSpacing: TYPOGRAPHY.letterSpacing.wide,
  },
  resultCard: {
    ...COMPONENTS.card.elevated,
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
  },
  resultHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  resultTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: '600',
    color: COLORS.gray[900],
  },
  confidenceBadge: {
    backgroundColor: COLORS.success + '20',
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.full,
  },
  confidenceText: {
    fontSize: FONT_SIZES.sm,
    fontWeight: '600',
    color: COLORS.success,
  },
  conversionDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
    marginBottom: SPACING.lg,
    paddingVertical: SPACING.md,
  },
  colorBox: {
    alignItems: 'center',
    flex: 1,
  },
  brandName: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
    marginBottom: SPACING.xs,
  },
  lineName: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.gray[500],
    marginBottom: SPACING.xs,
  },
  colorCode: {
    fontSize: FONT_SIZES.xl,
    fontWeight: 'bold',
    color: COLORS.gray[900],
  },
  alternatives: {
    backgroundColor: COLORS.gray[50],
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    marginBottom: SPACING.md,
  },
  alternativesTitle: {
    fontSize: FONT_SIZES.sm,
    fontWeight: '600',
    color: COLORS.gray[700],
    marginBottom: SPACING.sm,
  },
  alternativeItem: {
    marginBottom: SPACING.sm,
  },
  alternativeText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[800],
  },
  alternativeReason: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.gray[600],
    marginLeft: SPACING.md,
  },
  adjustments: {
    backgroundColor: COLORS.info + '10',
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    marginBottom: SPACING.md,
  },
  adjustmentsTitle: {
    fontSize: FONT_SIZES.sm,
    fontWeight: '600',
    color: COLORS.gray[700],
    marginBottom: SPACING.sm,
  },
  adjustment: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[700],
    marginBottom: SPACING.xs,
  },
  resultActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: SPACING.md,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.xs,
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    backgroundColor: COLORS.primary + '10',
  },
  actionButtonText: {
    fontSize: FONT_SIZES.sm,
    fontWeight: '500',
    color: COLORS.primary,
  },
  recentSection: {
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.xl,
  },
  recentTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: '600',
    color: COLORS.gray[700],
    marginBottom: SPACING.sm,
  },
  recentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.white,
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    marginBottom: SPACING.sm,
    borderWidth: 1,
    borderColor: COLORS.gray[200],
    gap: SPACING.sm,
  },
  recentText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[700],
    flex: 1,
  },
  modeSwitch: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: SPACING.sm,
    marginTop: SPACING.lg,
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.lg,
    backgroundColor: COLORS.surface,
    borderRadius: BORDER_RADIUS.full,
    ...SHADOWS.sm,
  },
  modeSwitchText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
    fontWeight: '500',
  },
  freeInput: {
    ...COMPONENTS.input.base,
    borderWidth: 1,
    borderColor: COLORS.gray[200],
    minHeight: 80,
    textAlignVertical: 'top',
  },
  freeHint: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[500],
    marginTop: SPACING.sm,
    fontStyle: 'italic',
  },
});
