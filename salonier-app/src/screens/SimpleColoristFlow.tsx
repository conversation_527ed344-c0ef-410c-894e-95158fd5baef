import React, { useEffect } from 'react';
import {
  View,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  Text,
  ScrollView,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { SimpleConsultationProvider, useSimpleConsultation, SimpleConsultationStep } from '../hooks/useSimpleConsultation';
import { COLORS, SPACING } from '../constants';
import { TYPOGRAPHY } from '../constants/design-system';
import Icon from '../components/common/Icon';
import ProgressDots from '../components/common/ProgressDots';
import BigActionButton from '../components/common/BigActionButton';

// Import step components
import ClientQuickSelect from '../components/colorist/steps/ClientQuickSelect';
import SimplePhotoAnalysis from '../components/colorist/SimplePhotoAnalysis';
import SmartFormulation from '../components/colorist/steps/SmartFormulation';
import ApplyAndSave from '../components/colorist/steps/ApplyAndSave';
import CorrectionGoalSimple from '../components/colorist/CorrectionGoalSimple';

function SimpleColoristFlowContent() {
  const navigation = useNavigation<NativeStackNavigationProp<any>>();
  const route = useRoute<any>();
  const { state, nextStep, previousStep, canProceed, setCorrection } = useSimpleConsultation();
  
  const isCorrection = route.params?.isCorrection || false;

  useEffect(() => {
    if (isCorrection) {
      setCorrection(true);
    }
  }, [isCorrection]);

  const handleBack = () => {
    if (state.currentStep === SimpleConsultationStep.CLIENT) {
      navigation.goBack();
    } else {
      previousStep();
    }
  };

  const handleNext = () => {
    if (canProceed()) {
      nextStep();
    }
  };

  const getStepNumber = () => {
    const steps = Object.values(SimpleConsultationStep);
    return steps.indexOf(state.currentStep) + 1;
  };

  const renderStepContent = () => {
    switch (state.currentStep) {
      case SimpleConsultationStep.CLIENT:
        return <ClientQuickSelect />;
      
      case SimpleConsultationStep.ANALYSIS:
        return (
          <SimplePhotoAnalysis
            mode="current"
            title={isCorrection ? '🔧 Análisis del Problema' : '📸 Análisis Actual'}
            subtitle={isCorrection ? 'Captura el problema actual' : 'Captura el estado actual del cabello'}
            onComplete={(data) => {
              // Handle analysis complete
              handleNext();
            }}
          />
        );
      
      case SimpleConsultationStep.OBJECTIVE:
        if (isCorrection) {
          return (
            <CorrectionGoalSimple
              onProblemSelect={(problemId, timeframe) => {
                setCorrection(true, problemId, timeframe);
                handleNext();
              }}
            />
          );
        }
        return (
          <SimplePhotoAnalysis
            mode="desired"
            title="🎯 Color Objetivo"
            subtitle="Muestra el color deseado"
            onComplete={(data) => {
              // Handle objective complete
              handleNext();
            }}
            showComparison={true}
            comparisonData={state.hairAnalysis}
          />
        );
      
      case SimpleConsultationStep.FORMULA:
        return <SmartFormulation />;
      
      case SimpleConsultationStep.APPLY:
        return <ApplyAndSave />;
      
      default:
        return null;
    }
  };

  const getStepTitle = () => {
    switch (state.currentStep) {
      case SimpleConsultationStep.CLIENT:
        return 'Cliente';
      case SimpleConsultationStep.ANALYSIS:
        return 'Análisis';
      case SimpleConsultationStep.OBJECTIVE:
        return isCorrection ? 'Problema' : 'Objetivo';
      case SimpleConsultationStep.FORMULA:
        return 'Fórmula';
      case SimpleConsultationStep.APPLY:
        return 'Aplicar';
      default:
        return '';
    }
  };

  const showNextButton = state.currentStep !== SimpleConsultationStep.APPLY;

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={handleBack}>
          <Icon name="arrow-back" size={24} color={COLORS.text} />
        </TouchableOpacity>
        
        <View style={styles.headerCenter}>
          <Text style={styles.stepTitle}>{getStepTitle()}</Text>
          <ProgressDots 
            total={5} 
            current={getStepNumber()} 
            color={isCorrection ? COLORS.warning : COLORS.primary}
          />
        </View>
        
        <View style={styles.headerRight} />
      </View>

      {/* Content */}
      <ScrollView 
        style={styles.content}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
        {renderStepContent()}
      </ScrollView>

      {/* Bottom Action */}
      {showNextButton && (
        <View style={styles.bottomAction}>
          <BigActionButton
            title="Siguiente"
            onPress={handleNext}
            disabled={!canProceed()}
            variant={isCorrection ? 'warning' : 'primary'}
            style={styles.nextButton}
          />
        </View>
      )}
    </SafeAreaView>
  );
}

export default function SimpleColoristFlow() {
  return (
    <SimpleConsultationProvider>
      <SimpleColoristFlowContent />
    </SimpleConsultationProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    backgroundColor: COLORS.white,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray[200],
  },
  backButton: {
    padding: SPACING.sm,
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  stepTitle: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: SPACING.xs,
  },
  headerRight: {
    width: 40, // Balance header
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    paddingVertical: SPACING.lg,
  },
  bottomAction: {
    padding: SPACING.lg,
    backgroundColor: COLORS.white,
    borderTopWidth: 1,
    borderTopColor: COLORS.gray[200],
  },
  nextButton: {
    marginHorizontal: SPACING.lg,
  },
});