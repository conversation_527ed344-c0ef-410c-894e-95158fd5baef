import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
} from 'react-native';
import { COLORS } from '../constants';

export default function SimpleColoristFlow() {
  return (
    <SafeAreaView style={styles.container}>
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <Text style={{ fontSize: 18, color: COLORS.text }}>
          Flujo Simple de Colorista
        </Text>
        <Text style={{ fontSize: 14, color: COLORS.textSecondary, marginTop: 10 }}>
          En desarrollo...
        </Text>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    backgroundColor: COLORS.white,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray[200],
  },
  backButton: {
    padding: SPACING.sm,
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  stepTitle: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: SPACING.xs,
  },
  headerRight: {
    width: 40, // Balance header
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    paddingVertical: SPACING.lg,
  },
  bottomAction: {
    padding: SPACING.lg,
    backgroundColor: COLORS.white,
    borderTopWidth: 1,
    borderTopColor: COLORS.gray[200],
  },
  nextButton: {
    marginHorizontal: SPACING.lg,
  },
});