import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
} from 'react-native';
import { COLORS, SPACING } from '../constants';

export default function SimpleColoristFlow() {
  return (
    <SafeAreaView style={styles.container}>
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <Text style={{ fontSize: 18, color: COLORS.text }}>
          Flujo Simple de Colorista
        </Text>
        <Text style={{ fontSize: 14, color: COLORS.textSecondary, marginTop: 10 }}>
          En desarrollo...
        </Text>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
});