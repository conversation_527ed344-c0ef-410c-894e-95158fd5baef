import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Switch,
} from 'react-native';
import Toast from 'react-native-toast-message';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { COLORS, SPACING, FONT_SIZES, BORDER_RADIUS } from '../constants';
import Card from '../components/common/Card';
import { dataService } from '../services/dataService';
import { User, Service, Brand } from '../types';
import { ServicesSettingsModal } from '../components/settings/ServicesSettingsModal';
import { BrandPreferencesModal } from '../components/settings/BrandPreferencesModal';
import { ProfileSettingsModal } from '../components/settings/ProfileSettingsModal';
import { BusinessHoursModal } from '../components/settings/BusinessHoursModal';
import { PricingSettingsModal } from '../components/settings/PricingSettingsModal';
import { ReminderTimeModal } from '../components/settings/ReminderTimeModal';
import { LanguageModal } from '../components/settings/LanguageModal';
import { PrivacyModal } from '../components/settings/PrivacyModal';
import { HelpModal } from '../components/settings/HelpModal';
import { AboutModal } from '../components/settings/AboutModal';
import InventoryLevelModal from '../components/settings/InventoryLevelModal';
import { NotificationSettingsModal } from '../components/settings/NotificationSettingsModal';

interface SettingItem {
  id: string;
  title: string;
  subtitle?: string;
  icon: string;
  type: 'navigate' | 'toggle' | 'modal';
  value?: boolean;
  onPress?: () => void;
}

interface SettingSection {
  title: string;
  items: SettingItem[];
}

export default function SettingsScreen() {
  const [user, setUser] = useState<User | null>(null);
  const [darkMode, setDarkMode] = useState(false);
  const [showServicesModal, setShowServicesModal] = useState(false);
  const [showBrandsModal, setShowBrandsModal] = useState(false);
  const [showProfileModal, setShowProfileModal] = useState(false);
  const [showBusinessHoursModal, setShowBusinessHoursModal] = useState(false);
  const [showPricingModal, setShowPricingModal] = useState(false);
  const [showReminderModal, setShowReminderModal] = useState(false);
  const [showLanguageModal, setShowLanguageModal] = useState(false);
  const [showPrivacyModal, setShowPrivacyModal] = useState(false);
  const [showHelpModal, setShowHelpModal] = useState(false);
  const [showAboutModal, setShowAboutModal] = useState(false);
  const [showInventoryLevelModal, setShowInventoryLevelModal] = useState(false);
  const [showNotificationModal, setShowNotificationModal] = useState(false);

  useEffect(() => {
    loadUserData();
  }, []);

  const loadUserData = async () => {
    try {
      const userData = await dataService.auth.getCurrentUser();
      if (userData) {
        setUser(userData);
      }
      // Cargar preferencias guardadas
      // Load notification preferences if needed
    } catch (error) {
      console.error('Error loading user data:', error);
    }
  };

  const getInventoryLevelText = (level?: string) => {
    switch (level) {
      case 'smart_cost':
        return 'Smart Cost - Rentabilidad';
      case 'full_control':
        return 'Control Total';
      case 'none':
      default:
        return 'Solo Fórmulas';
    }
  };

  const handleSignOut = () => {
    Toast.show({
      type: 'warning',
      text1: 'Cerrar Sesión',
      text2: 'Mantenga presionado para confirmar',
      position: 'top',
      visibilityTime: 3000,
      onPress: async () => {
        Toast.hide();
        try {
          await dataService.auth.signOut();
          Toast.show({
            type: 'success',
            text1: 'Sesión cerrada',
            text2: 'Has cerrado sesión correctamente',
            position: 'top',
          });
        } catch (error) {
          Toast.show({
            type: 'error',
            text1: 'Error',
            text2: 'No se pudo cerrar sesión',
            position: 'top',
          });
        }
      },
    });
  };

  const handleSaveServices = async (services: Service[]) => {
    if (user) {
      const updatedUser = { ...user, custom_services: services };
      setUser(updatedUser);
      // TODO: Guardar en backend
      try {
        // await dataService.users.update(user.id, { custom_services: services });
        Toast.show({
          type: 'success',
          text1: 'Éxito',
          text2: 'Servicios actualizados correctamente',
          position: 'top',
        });
      } catch (error) {
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: 'No se pudieron guardar los servicios',
          position: 'top',
        });
      }
    }
  };

  const handleSaveBrands = async (brands: Brand[]) => {
    if (user) {
      const updatedUser = { ...user, preferred_brands: brands };
      setUser(updatedUser);
      // TODO: Guardar en backend
      try {
        // await dataService.users.update(user.id, { preferred_brands: brands });
        Toast.show({
          type: 'success',
          text1: 'Éxito',
          text2: 'Marcas preferidas actualizadas',
          position: 'top',
        });
      } catch (error) {
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: 'No se pudieron guardar las marcas',
          position: 'top',
        });
      }
    }
  };

  const handleSaveProfile = async (profileData: Partial<User>) => {
    if (user) {
      const updatedUser = { ...user, ...profileData };
      setUser(updatedUser);
      // TODO: Guardar en backend
      try {
        // await dataService.users.update(user.id, profileData);
        Toast.show({
          type: 'success',
          text1: 'Éxito',
          text2: 'Perfil actualizado correctamente',
          position: 'top',
        });
      } catch (error) {
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: 'No se pudo actualizar el perfil',
          position: 'top',
        });
      }
    }
  };

  const handleSaveBusinessHours = async (hours: any) => {
    if (user) {
      const updatedUser = { ...user, business_hours: hours };
      setUser(updatedUser);
      // TODO: Guardar en backend
      try {
        // await dataService.users.update(user.id, { business_hours: hours });
        Toast.show({
          type: 'success',
          text1: 'Éxito',
          text2: 'Horario actualizado correctamente',
          position: 'top',
        });
      } catch (error) {
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: 'No se pudo actualizar el horario',
          position: 'top',
        });
      }
    }
  };

  const sections: SettingSection[] = [
    {
      title: 'Mi Cuenta',
      items: [
        {
          id: 'profile',
          title: 'Perfil',
          subtitle: user?.full_name || 'Configurar perfil',
          icon: 'account-circle',
          type: 'navigate',
          onPress: () => setShowProfileModal(true),
        },
        {
          id: 'services',
          title: 'Mis Servicios',
          subtitle: `${user?.custom_services?.length || 0} servicios configurados`,
          icon: 'scissors-cutting',
          type: 'navigate',
          onPress: () => setShowServicesModal(true),
        },
        {
          id: 'brands',
          title: 'Marcas Preferidas',
          subtitle: `${user?.preferred_brands?.length || 0} marcas seleccionadas`,
          icon: 'palette',
          type: 'navigate',
          onPress: () => setShowBrandsModal(true),
        },
      ],
    },
    {
      title: 'Configuración del Negocio',
      items: [
        {
          id: 'hours',
          title: 'Horario de Trabajo',
          subtitle: user?.business_hours ? 'Configurado' : 'Lun-Vie 9:00-19:00',
          icon: 'clock-outline',
          type: 'navigate',
          onPress: () => setShowBusinessHoursModal(true),
        },
        {
          id: 'pricing',
          title: 'Precios y Márgenes',
          subtitle: 'Configurar márgenes de ganancia',
          icon: 'currency-eur',
          type: 'navigate',
          onPress: () => setShowPricingModal(true),
        },
        {
          id: 'inventory_level',
          title: 'Control de Inventario',
          subtitle: getInventoryLevelText(user?.inventory_level),
          icon: 'package-variant',
          type: 'navigate',
          onPress: () => setShowInventoryLevelModal(true),
        },
      ],
    },
    {
      title: 'Notificaciones',
      items: [
        {
          id: 'notifications',
          title: 'Notificaciones',
          subtitle: 'Configurar recordatorios y alertas',
          icon: 'bell-outline',
          type: 'navigate',
          onPress: () => setShowNotificationModal(true),
        },
      ],
    },
    {
      title: 'Preferencias',
      items: [
        {
          id: 'language',
          title: 'Idioma',
          subtitle: 'Español',
          icon: 'translate',
          type: 'navigate',
          onPress: () => setShowLanguageModal(true),
        },
        {
          id: 'theme',
          title: 'Modo Oscuro',
          subtitle: 'Próximamente',
          icon: 'theme-light-dark',
          type: 'toggle',
          value: darkMode,
          onPress: () =>
            Toast.show({
              type: 'info',
              text1: 'Próximamente',
              text2: 'El modo oscuro estará disponible pronto',
              position: 'top',
            }),
        },
      ],
    },
    {
      title: 'Más',
      items: [
        {
          id: 'privacy',
          title: 'Privacidad y Seguridad',
          icon: 'shield-lock-outline',
          type: 'navigate',
          onPress: () => setShowPrivacyModal(true),
        },
        {
          id: 'help',
          title: 'Ayuda y Soporte',
          icon: 'help-circle-outline',
          type: 'navigate',
          onPress: () => setShowHelpModal(true),
        },
        {
          id: 'about',
          title: 'Acerca de Salonier',
          subtitle: 'Versión 1.0.0',
          icon: 'information-outline',
          type: 'navigate',
          onPress: () => setShowAboutModal(true),
        },
      ],
    },
  ];

  const renderSettingItem = (item: SettingItem) => {
    return (
      <TouchableOpacity
        key={item.id}
        style={styles.settingItem}
        onPress={item.onPress}
        activeOpacity={0.7}
      >
        <View style={styles.settingItemLeft}>
          <View style={styles.iconContainer}>
            <MaterialCommunityIcons name={item.icon as any} size={24} color={COLORS.gray[700]} />
          </View>
          <View style={styles.settingItemText}>
            <Text style={styles.settingItemTitle}>{item.title}</Text>
            {item.subtitle && <Text style={styles.settingItemSubtitle}>{item.subtitle}</Text>}
          </View>
        </View>
        {item.type === 'navigate' && (
          <MaterialCommunityIcons name="chevron-right" size={24} color={COLORS.gray[400]} />
        )}
        {item.type === 'toggle' && (
          <Switch
            value={item.value}
            onValueChange={item.onPress}
            trackColor={{ false: COLORS.gray[300], true: COLORS.secondary }}
            thumbColor={COLORS.white}
          />
        )}
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <Text style={styles.title}>Ajustes</Text>
        </View>

        {sections.map((section, index) => (
          <View key={index} style={styles.section}>
            <Text style={styles.sectionTitle}>{section.title}</Text>
            <Card elevation="low" style={styles.sectionCard}>
              {section.items.map((item, itemIndex) => (
                <React.Fragment key={item.id}>
                  {renderSettingItem(item)}
                  {itemIndex < section.items.length - 1 && <View style={styles.divider} />}
                </React.Fragment>
              ))}
            </Card>
          </View>
        ))}

        <TouchableOpacity style={styles.signOutButton} onPress={handleSignOut}>
          <Text style={styles.signOutText}>Cerrar Sesión</Text>
        </TouchableOpacity>

        <View style={styles.footer}>
          <Text style={styles.footerText}>Hecho con ❤️ para profesionales del color</Text>
        </View>
      </ScrollView>

      {/* Modales */}
      <ServicesSettingsModal
        visible={showServicesModal}
        onClose={() => setShowServicesModal(false)}
        services={user?.custom_services || []}
        onSave={handleSaveServices}
      />

      <BrandPreferencesModal
        visible={showBrandsModal}
        onClose={() => setShowBrandsModal(false)}
        preferredBrands={user?.preferred_brands || []}
        onSave={handleSaveBrands}
      />

      {user && (
        <>
          <ProfileSettingsModal
            visible={showProfileModal}
            onClose={() => setShowProfileModal(false)}
            user={user}
            onSave={handleSaveProfile}
          />

          <BusinessHoursModal
            visible={showBusinessHoursModal}
            onClose={() => setShowBusinessHoursModal(false)}
            businessHours={user.business_hours}
            onSave={handleSaveBusinessHours}
          />
        </>
      )}

      <PricingSettingsModal
        visible={showPricingModal}
        onClose={() => setShowPricingModal(false)}
        onSave={settings => {
          console.log('Pricing settings saved:', settings);
          // TODO: Guardar en backend
        }}
      />

      <ReminderTimeModal
        visible={showReminderModal}
        onClose={() => setShowReminderModal(false)}
        onSave={settings => {
          console.log('Reminder settings saved:', settings);
          // TODO: Guardar en backend
        }}
      />

      <LanguageModal
        visible={showLanguageModal}
        onClose={() => setShowLanguageModal(false)}
        currentLanguage="es"
        onSelectLanguage={code => {
          console.log('Language selected:', code);
          // TODO: Guardar idioma y actualizar app
        }}
      />

      <PrivacyModal visible={showPrivacyModal} onClose={() => setShowPrivacyModal(false)} />

      <HelpModal visible={showHelpModal} onClose={() => setShowHelpModal(false)} />

      <AboutModal visible={showAboutModal} onClose={() => setShowAboutModal(false)} />

      {user && (
        <InventoryLevelModal
          visible={showInventoryLevelModal}
          onClose={() => setShowInventoryLevelModal(false)}
          user={user}
          onUpdate={updatedUser => {
            setUser(updatedUser);
            // TODO: Guardar en backend
          }}
        />
      )}

      <NotificationSettingsModal
        visible={showNotificationModal}
        onClose={() => setShowNotificationModal(false)}
        currentUser={user}
        onUpdate={updates => {
          if (user) {
            setUser({ ...user, ...updates });
          }
        }}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    paddingHorizontal: SPACING.lg,
    paddingTop: SPACING.lg,
    paddingBottom: SPACING.md,
  },
  title: {
    fontSize: FONT_SIZES.xxl,
    fontWeight: 'bold',
    color: COLORS.gray[900],
  },
  section: {
    marginBottom: SPACING.xl,
    paddingHorizontal: SPACING.lg,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.sm,
    fontWeight: '600',
    color: COLORS.gray[600],
    marginBottom: SPACING.sm,
    marginLeft: SPACING.xs,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  sectionCard: {
    padding: 0,
    overflow: 'hidden',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
  },
  settingItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: BORDER_RADIUS.md,
    backgroundColor: COLORS.gray[100],
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: SPACING.md,
  },
  settingItemText: {
    flex: 1,
  },
  settingItemTitle: {
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[900],
    fontWeight: '500',
  },
  settingItemSubtitle: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
    marginTop: 2,
  },
  divider: {
    height: 1,
    backgroundColor: COLORS.gray[100],
    marginLeft: SPACING.lg + 40 + SPACING.md,
  },
  signOutButton: {
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.xl,
    paddingVertical: SPACING.md,
    backgroundColor: COLORS.error + '10',
    borderRadius: BORDER_RADIUS.md,
    alignItems: 'center',
  },
  signOutText: {
    fontSize: FONT_SIZES.md,
    fontWeight: '600',
    color: COLORS.error,
  },
  footer: {
    alignItems: 'center',
    paddingBottom: SPACING.xl,
  },
  footerText: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.gray[500],
  },
});
