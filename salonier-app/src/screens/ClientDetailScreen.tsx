import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  ActivityIndicator,
} from 'react-native';
import Toast from 'react-native-toast-message';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { COLORS, SPACING, FONT_SIZES, BORDER_RADIUS } from '../constants';
import { COMPONENTS, TYPOGRAPHY, SHADOWS } from '../constants/design-system';
// import { Client } from '../types';
import Card from '../components/common/Card';

export default function ClientDetailScreen({ route, navigation }: any) {
  const { client } = route.params;
  const [consultations] = useState<any[]>([]);
  const [loading] = useState(false);

  useEffect(() => {
    navigation.setOptions({
      title: client.full_name,
      headerRight: () => (
        <TouchableOpacity style={styles.headerButton} onPress={handleEdit}>
          <MaterialCommunityIcons name="pencil" size={24} color={COLORS.primary} />
        </TouchableOpacity>
      ),
    });
  }, [client]);

  const handleEdit = () => {
    Toast.show({
      type: 'info',
      text1: 'Próximamente',
      text2: 'La edición de clientes estará disponible pronto',
      position: 'top',
    });
  };

  const handleDelete = () => {
    Toast.show({
      type: 'warning',
      text1: 'Eliminar Cliente',
      text2: 'Mantén presionado para confirmar eliminación',
      position: 'top',
      visibilityTime: 3000,
      onPress: () => {
        Toast.hide();
        Toast.show({
          type: 'info',
          text1: 'Próximamente',
          text2: 'La eliminación estará disponible pronto',
          position: 'top',
        });
      },
    });
  };

  const InfoRow = ({ icon, label, value }: any) => (
    <View style={styles.infoRow}>
      <MaterialCommunityIcons name={icon} size={20} color={COLORS.gray[500]} />
      <View style={styles.infoContent}>
        <Text style={styles.infoLabel}>{label}</Text>
        <Text style={styles.infoValue}>{value || 'No especificado'}</Text>
      </View>
    </View>
  );

  // Generate avatar initials
  const initials = client.full_name
    .split(' ')
    .map((n: string) => n[0])
    .join('')
    .toUpperCase()
    .slice(0, 2);

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Profile Header */}
        <View style={styles.profileHeader}>
          <View style={[COMPONENTS.avatar.xlarge, styles.profileAvatar]}>
            <Text style={styles.profileAvatarText}>{initials}</Text>
          </View>
          <Text style={styles.profileName}>{client.full_name}</Text>
          <Text style={styles.profileDate}>
            Cliente desde {new Date(client.created_at).toLocaleDateString('es-ES')}
          </Text>
        </View>

        {/* Contact Info */}
        <Card style={[COMPONENTS.card.base, styles.infoCard]}>
          <Text style={styles.sectionTitle}>Información de Contacto</Text>

          <InfoRow icon="phone" label="Teléfono" value={client.phone} />

          <InfoRow icon="email" label="Email" value={client.email} />
        </Card>

        {client.allergies && client.allergies.length > 0 && (
          <Card style={[COMPONENTS.card.base, styles.infoCard, styles.allergyCard]}>
            <View style={styles.allergyHeader}>
              <MaterialCommunityIcons name="alert" size={24} color={COLORS.warning} />
              <Text style={styles.allergyTitle}>Alergias</Text>
            </View>
            <View style={styles.allergyList}>
              {client.allergies.map((allergy: string, index: number) => (
                <View key={index} style={styles.allergyItem}>
                  <Text style={styles.allergyText}>• {allergy}</Text>
                </View>
              ))}
            </View>
          </Card>
        )}

        {client.preferences && (
          <Card style={[COMPONENTS.card.base, styles.infoCard]}>
            <Text style={styles.sectionTitle}>Preferencias</Text>
            <Text style={styles.preferencesText}>{client.preferences}</Text>
          </Card>
        )}

        <Card style={[COMPONENTS.card.base, styles.infoCard]}>
          <Text style={styles.sectionTitle}>Historial de Coloración</Text>

          {loading ? (
            <ActivityIndicator size="large" color={COLORS.primary} style={styles.loading} />
          ) : consultations.length === 0 ? (
            <View style={styles.emptyHistory}>
              <MaterialCommunityIcons
                name="hair-dryer-outline"
                size={48}
                color={COLORS.gray[300]}
              />
              <Text style={styles.emptyText}>Sin consultas registradas</Text>
              <TouchableOpacity
                style={styles.newConsultationButton}
                onPress={() => navigation.navigate('ConsultationFlow', { client })}
              >
                <Text style={styles.newConsultationText}>Iniciar primera consulta</Text>
              </TouchableOpacity>
            </View>
          ) : (
            <View style={styles.consultationsList}>
              {consultations.map((consultation, index) => (
                <TouchableOpacity
                  key={consultation.id}
                  style={[
                    styles.consultationItem,
                    index === consultations.length - 1 && styles.lastItem,
                  ]}
                  onPress={() =>
                    Toast.show({
                      type: 'info',
                      text1: 'Próximamente',
                      text2: 'Vista detallada de consultas disponible pronto',
                      position: 'top',
                    })
                  }
                >
                  <View style={styles.consultationHeader}>
                    <MaterialCommunityIcons
                      name={consultation.is_correction ? 'wrench' : 'palette'}
                      size={20}
                      color={consultation.is_correction ? COLORS.warning : COLORS.primary}
                    />
                    <Text style={styles.consultationDate}>
                      {new Date(consultation.created_at).toLocaleDateString('es-ES')}
                    </Text>
                    {consultation.status === 'completed' && (
                      <View style={styles.completedBadge}>
                        <Text style={styles.completedText}>Completado</Text>
                      </View>
                    )}
                  </View>

                  {consultation.formulation && (
                    <View style={styles.formulationInfo}>
                      <Text style={styles.formulationText}>
                        {consultation.formulation.formula?.products[0]?.name ||
                          'Fórmula personalizada'}
                      </Text>
                      {consultation.actual_result && (
                        <Text style={styles.resultText}>
                          Resultado: Nivel {consultation.actual_result.achieved_level} -{' '}
                          {consultation.actual_result.achieved_tone}
                        </Text>
                      )}
                    </View>
                  )}

                  {consultation.follow_up_notes && (
                    <Text style={styles.notesPreview} numberOfLines={2}>
                      📝 {consultation.follow_up_notes}
                    </Text>
                  )}

                  <MaterialCommunityIcons
                    name="chevron-right"
                    size={20}
                    color={COLORS.gray[400]}
                    style={styles.chevron}
                  />
                </TouchableOpacity>
              ))}
            </View>
          )}
        </Card>

        <View style={styles.statsContainer}>
          <Card style={[COMPONENTS.card.base, styles.statCard]}>
            <Text style={styles.statValue}>{client.last_visit ? '1' : '0'}</Text>
            <Text style={styles.statLabel}>Visitas</Text>
          </Card>
          <Card style={[COMPONENTS.card.base, styles.statCard]}>
            <Text style={styles.statValue}>€0</Text>
            <Text style={styles.statLabel}>Gastado</Text>
          </Card>
          <Card style={[COMPONENTS.card.base, styles.statCard]}>
            <Text style={styles.statValue}>-</Text>
            <Text style={styles.statLabel}>Próxima cita</Text>
          </Card>
        </View>

        <TouchableOpacity style={styles.deleteButton} onPress={handleDelete}>
          <MaterialCommunityIcons name="delete-outline" size={20} color={COLORS.error} />
          <Text style={styles.deleteButtonText}>Eliminar Cliente</Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  headerButton: {
    marginRight: SPACING.md,
    padding: SPACING.xs,
  },
  profileHeader: {
    alignItems: 'center',
    paddingVertical: SPACING.xl,
    backgroundColor: COLORS.white,
    marginBottom: SPACING.md,
  },
  profileAvatar: {
    backgroundColor: COLORS.accent,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  profileAvatarText: {
    fontSize: TYPOGRAPHY.size['4xl'],
    fontWeight: '700' as const,
    color: COLORS.primary,
    letterSpacing: TYPOGRAPHY.letterSpacing.tight,
  },
  profileName: {
    fontSize: TYPOGRAPHY.size['2xl'],
    fontWeight: '600' as const,
    color: COLORS.text,
    marginBottom: SPACING.xs,
    letterSpacing: TYPOGRAPHY.letterSpacing.tight,
  },
  profileDate: {
    fontSize: TYPOGRAPHY.size.sm,
    color: COLORS.textSecondary,
  },
  infoCard: {
    marginHorizontal: SPACING.md,
    marginBottom: SPACING.md,
  },
  sectionTitle: {
    fontSize: TYPOGRAPHY.size.lg,
    fontWeight: '600' as const,
    color: COLORS.text,
    marginBottom: SPACING.md,
    letterSpacing: TYPOGRAPHY.letterSpacing.tight,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingVertical: SPACING.sm,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray[100],
  },
  infoContent: {
    flex: 1,
    marginLeft: SPACING.md,
  },
  infoLabel: {
    fontSize: TYPOGRAPHY.size.xs,
    color: COLORS.textTertiary,
    marginBottom: 2,
    textTransform: 'uppercase',
    letterSpacing: TYPOGRAPHY.letterSpacing.wide,
  },
  infoValue: {
    fontSize: TYPOGRAPHY.size.base,
    color: COLORS.text,
    fontWeight: '500' as const,
  },
  allergyCard: {
    backgroundColor: COLORS.warning + '10',
    borderWidth: 1,
    borderColor: COLORS.warning + '30',
  },
  allergyHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  allergyTitle: {
    fontSize: TYPOGRAPHY.size.lg,
    fontWeight: '600' as const,
    color: COLORS.text,
    marginLeft: SPACING.sm,
    letterSpacing: TYPOGRAPHY.letterSpacing.tight,
  },
  allergyList: {
    gap: SPACING.xs,
  },
  allergyItem: {
    paddingLeft: SPACING.md,
  },
  allergyText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[700],
  },
  preferencesText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[700],
    lineHeight: 22,
  },
  emptyHistory: {
    alignItems: 'center',
    paddingVertical: SPACING.xl,
  },
  emptyText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[500],
    marginTop: SPACING.sm,
  },
  loading: {
    paddingVertical: SPACING.xl,
  },
  newConsultationButton: {
    marginTop: SPACING.lg,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.sm,
    backgroundColor: COLORS.primary,
    borderRadius: BORDER_RADIUS.full,
  },
  newConsultationText: {
    color: COLORS.white,
    fontSize: FONT_SIZES.sm,
    fontWeight: '600',
  },
  consultationsList: {
    marginTop: SPACING.xs,
  },
  consultationItem: {
    paddingVertical: SPACING.md,
    borderTopWidth: 1,
    borderTopColor: COLORS.gray[100],
    position: 'relative',
  },
  lastItem: {
    borderBottomWidth: 0,
  },
  consultationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.sm,
    marginBottom: SPACING.xs,
  },
  consultationDate: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
    flex: 1,
  },
  completedBadge: {
    backgroundColor: COLORS.success + '20',
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs / 2,
    borderRadius: BORDER_RADIUS.full,
  },
  completedText: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.success,
    fontWeight: '600',
  },
  formulationInfo: {
    marginLeft: SPACING.lg + SPACING.sm,
    marginBottom: SPACING.xs,
  },
  formulationText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[800],
    fontWeight: '500',
  },
  resultText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
    marginTop: SPACING.xs / 2,
  },
  notesPreview: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
    fontStyle: 'italic',
    marginLeft: SPACING.lg + SPACING.sm,
    marginRight: SPACING.lg,
  },
  chevron: {
    position: 'absolute',
    right: 0,
    top: '50%',
    marginTop: -10,
  },
  consultationService: {
    flex: 1,
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[800],
  },
  statsContainer: {
    flexDirection: 'row',
    marginHorizontal: SPACING.md,
    marginBottom: SPACING.lg,
    gap: SPACING.sm,
  },
  statCard: {
    flex: 1,
    padding: SPACING.md,
    alignItems: 'center',
  },
  statValue: {
    fontSize: TYPOGRAPHY.size['2xl'],
    fontWeight: '700' as const,
    color: COLORS.secondary,
    marginBottom: SPACING.xs,
  },
  statLabel: {
    fontSize: TYPOGRAPHY.size.sm,
    color: COLORS.textSecondary,
  },
  deleteButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: SPACING.lg,
    marginVertical: SPACING.xl,
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    borderWidth: 1,
    borderColor: COLORS.error,
  },
  deleteButtonText: {
    color: COLORS.error,
    fontSize: FONT_SIZES.md,
    fontWeight: '500',
    marginLeft: SPACING.sm,
  },
});
