import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  // SafeAreaView,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import Toast from 'react-native-toast-message';
import { COLORS, SPACING, FONT_SIZES, BORDER_RADIUS, SHADOWS } from '../constants';
import { COMPONENTS, TYPOGRAPHY } from '../constants/design-system';
import { dataService } from '../services/dataService';
import { User, Product } from '../types';
import { smartSuggestionService, SmartSuggestion } from '../services/smartSuggestionService';
import SmartSuggestionCard from '../components/dashboard/SmartSuggestionCard';
import { LowStockAlert } from '../components/inventory/LowStockAlert';
import { useBehaviorTracking } from '../hooks/useBehaviorTracking';
import InventoryLevelModal from '../components/inventory/InventoryLevelModal';
import PricingSetupModal from '../components/inventory/PricingSetupModal';
import { useNotifications } from '../hooks/useNotifications';

interface DashboardMetrics {
  todayAppointments: number;
  todayRevenue: number;
  monthlyRevenue: number;
  pendingClients: number;
  lowStockItems: number;
}

export default function DashboardScreen({ navigation }: any) {
  const [user, setUser] = useState<User | null>(null);
  const [metrics, setMetrics] = useState<DashboardMetrics>({
    todayAppointments: 0,
    todayRevenue: 0,
    monthlyRevenue: 0,
    pendingClients: 0,
    lowStockItems: 0,
  });
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [suggestion, setSuggestion] = useState<SmartSuggestion | null>(null);
  const [showInventoryModal, setShowInventoryModal] = useState(false);
  const [showPricingModal, setShowPricingModal] = useState(false);
  const [products, setProducts] = useState<Product[]>([]);

  const { updateLastActiveDate } = useBehaviorTracking({
    user,
    onUserUpdate: setUser,
  });

  const { sendDailySummary, checkAndScheduleNotifications } = useNotifications();

  useEffect(() => {
    loadDashboardData();
    loadUserData();
    loadProducts();
    updateLastActiveDate();

    // Check and schedule notifications for the day
    checkAndScheduleNotifications();
  }, []);

  useEffect(() => {
    if (user) {
      checkForSuggestions();
    }
  }, [user]);

  const loadUserData = async () => {
    try {
      const userData = await dataService.auth.getCurrentUser();
      if (userData) {
        setUser(userData as any);
      }
    } catch (error) {
      console.error('Error loading user:', error);
    }
  };

  const checkForSuggestions = () => {
    if (!user) return;

    try {
      const newSuggestion = smartSuggestionService.getSuggestion(user);
      if (newSuggestion) {
        setSuggestion(newSuggestion);
        // Mark that a suggestion was shown
        const updatedUser = smartSuggestionService.recordSuggestionShown(user);
        setUser(updatedUser);
      }
    } catch (error) {
      console.error('Error checking suggestions:', error);
    }
  };

  const handleSuggestionAction = () => {
    if (!suggestion || !user) return;

    switch (suggestion.action.type) {
      case 'change_level':
        setShowInventoryModal(true);
        break;
      case 'open_modal':
        if (suggestion.action.payload?.modal === 'pricing_setup') {
          setShowPricingModal(true);
        }
        break;
      case 'navigate':
        navigation.navigate(suggestion.action.payload?.screen);
        break;
    }
  };

  const handleDismissSuggestion = () => {
    if (!suggestion || !user) return;

    const updatedUser = smartSuggestionService.dismissSuggestion(user, suggestion.id);
    setUser(updatedUser);
    setSuggestion(null);
  };

  const loadDashboardData = async () => {
    try {
      const metricsData = await dataService.metrics.getDashboard('1');
      if (!metricsData) {
        console.error('Error loading metrics');
        // Set default values if error
        setMetrics({
          todayAppointments: 2,
          todayRevenue: 120,
          monthlyRevenue: 2850,
          pendingClients: 3,
          lowStockItems: 0,
        });
      } else if (metricsData) {
        setMetrics({
          todayAppointments: metricsData.todayAppointments || 0,
          todayRevenue: metricsData.todayRevenue || 0,
          monthlyRevenue: metricsData.monthlyRevenue || 0,
          pendingClients: metricsData.pendingClients || 0,
          lowStockItems: metricsData.lowStockItems || 0,
        });

        // Send daily summary notification if it's a new day
        const now = new Date();
        if (now.getHours() === 8 && now.getMinutes() < 5) {
          sendDailySummary(metricsData.todayAppointments || 0, metricsData.todayRevenue || 0);
        }
      }
    } catch (error) {
      console.error('Error loading dashboard:', error);
      // Set default values if error
      setMetrics({
        todayAppointments: 2,
        todayRevenue: 120,
        monthlyRevenue: 2850,
        pendingClients: 3,
        lowStockItems: 0,
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadDashboardData();
    loadProducts();
  };

  const loadProducts = async () => {
    try {
      // TODO: Cargar desde dataService real
      const mockProducts: Product[] = [
        {
          id: '1',
          name: 'Majirel 7.31',
          brand: "L'Oréal",
          line: 'Majirel',
          code: '7.31',
          category: 'dye',
          current_stock: 3,
          min_stock: 5,
          unit: 'units',
          purchase_price: 8.5,
        },
        {
          id: '2',
          name: 'Oxidante 20 Vol',
          brand: "L'Oréal",
          code: '20VOL',
          category: 'developer',
          current_stock: 500,
          min_stock: 1000,
          unit: 'ml',
          purchase_price: 3.2,
        },
        {
          id: '3',
          name: 'Smartbond Paso 1',
          brand: "L'Oréal",
          line: 'Smartbond',
          code: 'SB1',
          category: 'treatment',
          current_stock: 250,
          min_stock: 500,
          unit: 'ml',
          purchase_price: 25.0,
        },
      ];
      setProducts(mockProducts);

      // Actualizar métricas de stock bajo
      const lowStock = mockProducts.filter(p => p.current_stock <= p.min_stock).length;
      setMetrics(prev => ({ ...prev, lowStockItems: lowStock }));
    } catch (error) {
      console.error('Error loading products:', error);
    }
  };

  const QuickActionButton = ({ title, icon, onPress, color = COLORS.primary }: any) => (
    <TouchableOpacity style={[COMPONENTS.card.base, styles.quickAction]} onPress={onPress}>
      <View style={[styles.quickActionIconContainer, { backgroundColor: color }]}>
        <Text style={styles.quickActionIcon}>{icon}</Text>
      </View>
      <Text style={styles.quickActionText}>{title}</Text>
    </TouchableOpacity>
  );

  const MetricCard = ({ title, value, subtitle, color = COLORS.primary }: any) => (
    <View style={[COMPONENTS.card.base, styles.metricCard]}>
      <Text style={styles.metricTitle}>{title}</Text>
      <Text style={[styles.metricValue, { color }]}>{value}</Text>
      {subtitle && <Text style={styles.metricSubtitle}>{subtitle}</Text>}
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={COLORS.primary} />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
      >
        {/* Header */}
        <View style={[COMPONENTS.header.base, styles.header]}>
          <View>
            <Text style={styles.greeting}>Hola, {user?.full_name || 'Estilista'}</Text>
            <Text style={styles.date}>
              {new Date().toLocaleDateString('es-ES', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
              })}
            </Text>
          </View>
        </View>

        {/* Smart Suggestion */}
        {suggestion && (
          <SmartSuggestionCard
            suggestion={suggestion}
            onAction={handleSuggestionAction}
            onDismiss={handleDismissSuggestion}
          />
        )}

        {/* Métricas principales */}
        <View style={styles.metricsGrid}>
          <MetricCard
            title="Citas Hoy"
            value={metrics.todayAppointments}
            subtitle="2 completadas"
            color={COLORS.info}
          />
          <MetricCard
            title="Ingresos Hoy"
            value={`€${metrics.todayRevenue}`}
            subtitle="Objetivo: €350"
            color={COLORS.success}
          />
        </View>

        {/* Botones principales */}
        <View style={styles.mainButtonsContainer}>
          <TouchableOpacity
            style={[COMPONENTS.card.elevated, styles.mainButton, styles.mainButtonPrimary]}
            onPress={() => navigation.navigate('SimpleColoristFlow')}
          >
            <Text style={styles.mainButtonIcon}>🎨</Text>
            <Text style={styles.mainButtonText}>Nueva Coloración</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[COMPONENTS.card.elevated, styles.mainButton, styles.mainButtonCorrection]}
            onPress={() => navigation.navigate('SimpleColoristFlow', { isCorrection: true })}
          >
            <Text style={styles.mainButtonIcon}>🔧</Text>
            <Text style={styles.mainButtonText}>Corrección de Color</Text>
          </TouchableOpacity>
        </View>

        {/* Acciones rápidas */}
        <View style={styles.quickActionsContainer}>
          <Text style={styles.sectionTitle}>Acceso Rápido</Text>
          <View style={styles.quickActionsGrid}>
            <QuickActionButton
              title="Agenda"
              icon="📅"
              onPress={() => navigation.navigate('Appointments')}
              color={COLORS.info}
            />
            <QuickActionButton
              title="Clientes"
              icon="👥"
              onPress={() => navigation.navigate('Clients')}
              color={COLORS.secondary}
            />
            <QuickActionButton
              title="Inventario"
              icon="📦"
              onPress={() => navigation.navigate('Inventory')}
              color={COLORS.warning}
            />
            <QuickActionButton
              title="Convertir"
              icon="🔄"
              onPress={() => navigation.navigate('BrandConverter')}
              color={COLORS.accent}
            />
          </View>
        </View>

        {/* Low Stock Alert */}
        {products.filter(p => p.current_stock <= p.min_stock).length > 0 && (
          <LowStockAlert
            products={products.filter(p => p.current_stock <= p.min_stock)}
            onPress={() => navigation.navigate('Inventory')}
          />
        )}

        {/* Alertas */}
        {metrics.pendingClients > 0 && (
          <View style={styles.alertsContainer}>
            <Text style={styles.sectionTitle}>Alertas</Text>
            <View style={styles.alertCard}>
              <Text style={styles.alertIcon}>⚠️</Text>
              <Text style={styles.alertText}>
                {metrics.pendingClients} clientes necesitan retoque
              </Text>
            </View>
          </View>
        )}

        {/* Resumen mensual */}
        <View style={styles.monthlyContainer}>
          <Text style={styles.sectionTitle}>Resumen Mensual</Text>
          <View style={[COMPONENTS.card.elevated, styles.monthlyCard]}>
            <Text style={styles.monthlyRevenue}>€{metrics.monthlyRevenue}</Text>
            <Text style={styles.monthlyLabel}>Ingresos del mes</Text>
            <View style={styles.monthlyProgress}>
              <View style={[styles.monthlyProgressBar, { width: '75%' }]} />
            </View>
            <Text style={styles.monthlySubtext}>75% del objetivo mensual</Text>
          </View>
        </View>
      </ScrollView>

      {/* Modals */}
      <InventoryLevelModal
        visible={showInventoryModal}
        onClose={() => setShowInventoryModal(false)}
        currentLevel={user?.inventory_level || 'none'}
        onSelectLevel={level => {
          const updatedUser = { ...user!, inventory_level: level };
          setUser(updatedUser);
          setShowInventoryModal(false);
          setSuggestion(null);

          // If selecting smart_cost, show pricing modal
          if (level === 'smart_cost') {
            setTimeout(() => setShowPricingModal(true), 500);
          }
        }}
      />

      <PricingSetupModal
        visible={showPricingModal}
        onClose={() => setShowPricingModal(false)}
        onComplete={pricing => {
          const updatedUser = {
            ...user!,
            product_pricing: pricing,
          };
          setUser(updatedUser);
          setShowPricingModal(false);
          Toast.show({
            type: 'success',
            text1: 'Configuración Completa',
            text2: 'Ya puedes ver los costos reales de tus servicios',
            position: 'top',
          });
        }}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    paddingTop: SPACING.md,
    paddingBottom: SPACING.sm,
    backgroundColor: COLORS.white,
    marginBottom: SPACING.md,
  },
  greeting: {
    fontSize: TYPOGRAPHY.size['2xl'],
    fontWeight: '700' as const,
    color: COLORS.text,
    marginBottom: 2,
    letterSpacing: TYPOGRAPHY.letterSpacing.tight,
  },
  date: {
    fontSize: TYPOGRAPHY.size.sm,
    color: COLORS.textSecondary,
    textTransform: 'capitalize',
  },
  metricsGrid: {
    flexDirection: 'row',
    paddingHorizontal: SPACING.md,
    gap: SPACING.md,
    marginBottom: SPACING.lg,
  },
  metricCard: {
    flex: 1,
  },
  metricTitle: {
    fontSize: TYPOGRAPHY.size.sm,
    color: COLORS.textSecondary,
    marginBottom: SPACING.xs,
    textTransform: 'uppercase',
    letterSpacing: TYPOGRAPHY.letterSpacing.wide,
  },
  metricValue: {
    fontSize: TYPOGRAPHY.size['3xl'],
    fontWeight: '700' as const,
    marginBottom: SPACING.xs,
  },
  metricSubtitle: {
    fontSize: TYPOGRAPHY.size.xs,
    color: COLORS.textTertiary,
  },
  mainButtonsContainer: {
    flexDirection: 'row',
    paddingHorizontal: SPACING.md,
    marginBottom: SPACING.lg,
    gap: SPACING.md,
  },
  mainButton: {
    flex: 1,
    paddingVertical: SPACING.xl,
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
  },
  mainButtonPrimary: {
    backgroundColor: COLORS.secondary,
  },
  mainButtonCorrection: {
    backgroundColor: COLORS.warning,
  },
  mainButtonIcon: {
    fontSize: 40,
    marginBottom: SPACING.sm,
  },
  mainButtonText: {
    color: COLORS.white,
    fontSize: TYPOGRAPHY.size.sm,
    fontWeight: '700' as const,
    textTransform: 'uppercase',
    letterSpacing: TYPOGRAPHY.letterSpacing.wide,
  },
  quickActionsContainer: {
    paddingHorizontal: SPACING.md,
    marginBottom: SPACING.lg,
  },
  sectionTitle: {
    fontSize: TYPOGRAPHY.size.lg,
    fontWeight: '600' as const,
    color: COLORS.text,
    marginBottom: SPACING.md,
    letterSpacing: TYPOGRAPHY.letterSpacing.tight,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.md,
  },
  quickAction: {
    flex: 1,
    minWidth: '45%',
    alignItems: 'center',
    paddingVertical: SPACING.lg,
  },
  quickActionIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  quickActionIcon: {
    fontSize: 24,
    color: COLORS.white,
  },
  quickActionText: {
    color: COLORS.text,
    fontSize: TYPOGRAPHY.size.sm,
    fontWeight: '500' as const,
  },
  alertsContainer: {
    paddingHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
  },
  alertCard: {
    backgroundColor: COLORS.white,
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
    ...SHADOWS.sm,
  },
  alertIcon: {
    fontSize: 20,
    marginRight: SPACING.md,
  },
  alertText: {
    flex: 1,
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[700],
  },
  monthlyContainer: {
    paddingHorizontal: SPACING.md,
    marginBottom: SPACING.xl,
  },
  monthlyCard: {
    alignItems: 'center',
  },
  monthlyRevenue: {
    fontSize: TYPOGRAPHY.size['4xl'],
    fontWeight: '700' as const,
    color: COLORS.secondary,
    marginBottom: SPACING.xs,
  },
  monthlyLabel: {
    fontSize: TYPOGRAPHY.size.base,
    color: COLORS.textSecondary,
    marginBottom: SPACING.md,
  },
  monthlyProgress: {
    width: '100%',
    height: 8,
    backgroundColor: COLORS.gray[200],
    borderRadius: BORDER_RADIUS.full,
    overflow: 'hidden',
    marginBottom: SPACING.sm,
  },
  monthlyProgressBar: {
    height: '100%',
    backgroundColor: COLORS.primary,
  },
  monthlySubtext: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[500],
  },
});
