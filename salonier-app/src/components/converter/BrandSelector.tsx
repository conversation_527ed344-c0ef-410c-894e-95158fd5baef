import React from 'react';
import {
  TouchableOpacity,
  Text,
  View,
  StyleSheet,
} from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { COLORS, SPACING, FONT_SIZES, BORDER_RADIUS } from '../../constants';

interface BrandSelectorProps {
  label: string;
  value: string;
  line?: string;
  placeholder?: string;
  onPress: () => void;
  isPreferred?: boolean;
}

export const BrandSelector: React.FC<BrandSelectorProps> = ({
  label,
  value,
  line,
  placeholder = "Seleccionar...",
  onPress,
  isPreferred = false,
}) => {
  const hasValue = value && value.trim() !== '';

  return (
    <View style={styles.container}>
      <Text style={styles.label}>{label}</Text>
      <TouchableOpacity 
        style={styles.selector}
        onPress={onPress}
        activeOpacity={0.7}
      >
        <View style={styles.content}>
          {hasValue ? (
            <View style={styles.valueContainer}>
              {isPreferred && (
                <MaterialCommunityIcons name="star" size={16} color={COLORS.warning} />
              )}
              <Text style={styles.brandText}>{value}</Text>
              {line && <Text style={styles.lineText}>{line}</Text>}
            </View>
          ) : (
            <Text style={styles.placeholder}>{placeholder}</Text>
          )}
        </View>
        <MaterialCommunityIcons 
          name="chevron-down" 
          size={24} 
          color={COLORS.gray[400]} 
        />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: SPACING.md,
  },
  label: {
    fontSize: FONT_SIZES.sm,
    fontWeight: '500',
    color: COLORS.gray[700],
    marginBottom: SPACING.xs,
  },
  selector: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.white,
    borderWidth: 1,
    borderColor: COLORS.gray[300],
    borderRadius: BORDER_RADIUS.md,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.md,
    minHeight: 50,
  },
  content: {
    flex: 1,
  },
  valueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.xs,
  },
  brandText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[900],
    fontWeight: '500',
  },
  lineText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
    fontStyle: 'italic',
  },
  placeholder: {
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[400],
  },
});