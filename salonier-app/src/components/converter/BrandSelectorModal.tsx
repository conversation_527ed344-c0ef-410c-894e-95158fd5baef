import React, { useState, useEffect, useMemo } from 'react';
import {
  Modal,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  Dimensions,
} from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { COLORS, SPACING, FONT_SIZES, BORDER_RADIUS, SHADOWS } from '../../constants';

const { height: SCREEN_HEIGHT } = Dimensions.get('window');

// interface BrandOption {
//   brand: string;
//   lines?: string[];
// }

interface BrandSelectorModalProps {
  visible: boolean;
  onClose: () => void;
  onSelect: (brand: string, line?: string) => void;
  title: string;
  brands: { [key: string]: { lines: string[] } };
  showLines?: boolean;
  allowManual?: boolean;
  currentBrand?: string;
  currentLine?: string;
  preferredBrands?: string[];
}

const POPULAR_BRANDS = ["L'Oréal", 'Wella', '<PERSON><PERSON><PERSON><PERSON>pf', 'Revlon'];

export const BrandSelectorModal: React.FC<BrandSelectorModalProps> = ({
  visible,
  onClose,
  onSelect,
  title,
  brands,
  showLines = false,
  allowManual = true,
  currentBrand,
  currentLine,
  preferredBrands = [],
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredBrands, setFilteredBrands] = useState<string[]>([]);
  const [selectedBrand, setSelectedBrand] = useState(currentBrand || '');
  const [, setSelectedLine] = useState(currentLine || '');
  const [showManualInput, setShowManualInput] = useState(false);
  const [manualBrand, setManualBrand] = useState('');
  const [manualLine, setManualLine] = useState('');

  const allBrands = useMemo(() => Object.keys(brands), [brands]);

  useEffect(() => {
    // Filtrar marcas según búsqueda
    let filtered = allBrands;

    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = allBrands.filter(brand => brand.toLowerCase().includes(query));
    }

    // Ordenar con marcas preferidas primero
    const preferredFiltered = filtered.filter(brand => preferredBrands.includes(brand));
    const otherFiltered = filtered.filter(brand => !preferredBrands.includes(brand));

    setFilteredBrands([...preferredFiltered, ...otherFiltered]);
  }, [searchQuery, allBrands, preferredBrands.join(',')]); // Fix infinite loop by converting array to string

  const handleSelectBrand = (brand: string) => {
    setSelectedBrand(brand);
    if (!showLines || !brands[brand]?.lines?.length) {
      onSelect(brand);
      handleClose();
    }
  };

  const handleSelectLine = (line: string) => {
    onSelect(selectedBrand, line);
    handleClose();
  };

  const handleManualSubmit = () => {
    if (manualBrand.trim()) {
      onSelect(manualBrand.trim(), manualLine.trim() || undefined);
      handleClose();
    }
  };

  const handleClose = () => {
    setSearchQuery('');
    setSelectedBrand('');
    setSelectedLine('');
    setShowManualInput(false);
    setManualBrand('');
    setManualLine('');
    onClose();
  };

  if (showManualInput) {
    return (
      <Modal
        visible={visible}
        animationType="slide"
        transparent={true}
        onRequestClose={handleClose}
      >
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.container}
        >
          <TouchableOpacity style={styles.backdrop} activeOpacity={1} onPress={handleClose} />
          <View style={styles.modalContent}>
            <View style={styles.header}>
              <TouchableOpacity onPress={() => setShowManualInput(false)}>
                <MaterialCommunityIcons name="arrow-left" size={24} color={COLORS.gray[700]} />
              </TouchableOpacity>
              <Text style={styles.title}>Añadir Marca Manual</Text>
              <TouchableOpacity onPress={handleClose}>
                <MaterialCommunityIcons name="close" size={24} color={COLORS.gray[700]} />
              </TouchableOpacity>
            </View>

            <View style={styles.manualForm}>
              <Text style={styles.inputLabel}>Nombre de la marca *</Text>
              <TextInput
                style={styles.input}
                value={manualBrand}
                onChangeText={setManualBrand}
                placeholder="Ej: Redken, Joico, etc."
                placeholderTextColor={COLORS.gray[400]}
                autoFocus
              />

              <Text style={styles.inputLabel}>Línea (opcional)</Text>
              <TextInput
                style={styles.input}
                value={manualLine}
                onChangeText={setManualLine}
                placeholder="Ej: Shades EQ, Color Fusion"
                placeholderTextColor={COLORS.gray[400]}
              />

              <TouchableOpacity
                style={[styles.submitButton, !manualBrand.trim() && styles.submitButtonDisabled]}
                onPress={handleManualSubmit}
                disabled={!manualBrand.trim()}
              >
                <Text style={styles.submitButtonText}>Añadir y Usar</Text>
              </TouchableOpacity>
            </View>
          </View>
        </KeyboardAvoidingView>
      </Modal>
    );
  }

  return (
    <Modal visible={visible} animationType="slide" transparent={true} onRequestClose={handleClose}>
      <View style={styles.container}>
        <TouchableOpacity style={styles.backdrop} activeOpacity={1} onPress={handleClose} />
        <View
          style={[styles.modalContent, showLines && selectedBrand && styles.modalContentExpanded]}
        >
          <View style={styles.header}>
            {showLines && selectedBrand ? (
              <TouchableOpacity onPress={() => setSelectedBrand('')}>
                <MaterialCommunityIcons name="arrow-left" size={24} color={COLORS.gray[700]} />
              </TouchableOpacity>
            ) : (
              <View style={{ width: 24 }} />
            )}
            <Text style={styles.title}>
              {showLines && selectedBrand ? `Líneas de ${selectedBrand}` : title}
            </Text>
            <TouchableOpacity onPress={handleClose}>
              <MaterialCommunityIcons name="close" size={24} color={COLORS.gray[700]} />
            </TouchableOpacity>
          </View>

          {/* Búsqueda */}
          {!selectedBrand && (
            <View style={styles.searchContainer}>
              <MaterialCommunityIcons name="magnify" size={20} color={COLORS.gray[400]} />
              <TextInput
                style={styles.searchInput}
                placeholder="Buscar marca..."
                placeholderTextColor={COLORS.gray[400]}
                value={searchQuery}
                onChangeText={setSearchQuery}
                autoCorrect={false}
              />
              {searchQuery.length > 0 && (
                <TouchableOpacity onPress={() => setSearchQuery('')}>
                  <MaterialCommunityIcons name="close-circle" size={20} color={COLORS.gray[400]} />
                </TouchableOpacity>
              )}
            </View>
          )}

          {/* Marcas populares */}
          {!selectedBrand && !searchQuery && (
            <View style={styles.popularSection}>
              <Text style={styles.sectionTitle}>Populares</Text>
              <View style={styles.popularBrands}>
                {POPULAR_BRANDS.map(brand => (
                  <TouchableOpacity
                    key={brand}
                    style={[
                      styles.popularChip,
                      currentBrand === brand && styles.popularChipSelected,
                    ]}
                    onPress={() => handleSelectBrand(brand)}
                  >
                    <Text
                      style={[
                        styles.popularChipText,
                        currentBrand === brand && styles.popularChipTextSelected,
                      ]}
                    >
                      {brand}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          )}

          {/* Lista de marcas o líneas */}
          <ScrollView style={styles.list} showsVerticalScrollIndicator={false}>
            {showLines && selectedBrand ? (
              // Mostrar líneas de la marca seleccionada
              <>
                {!brands[selectedBrand]?.lines?.length ? (
                  <TouchableOpacity
                    style={styles.listItem}
                    onPress={() => {
                      onSelect(selectedBrand);
                      handleClose();
                    }}
                  >
                    <Text style={styles.listItemText}>Sin línea específica</Text>
                  </TouchableOpacity>
                ) : (
                  <>
                    <TouchableOpacity
                      style={styles.listItem}
                      onPress={() => {
                        onSelect(selectedBrand);
                        handleClose();
                      }}
                    >
                      <Text style={styles.listItemText}>Cualquier línea</Text>
                      <Text style={styles.listItemSubtext}>Dejar que IA sugiera</Text>
                    </TouchableOpacity>
                    {brands[selectedBrand].lines.map(line => (
                      <TouchableOpacity
                        key={line}
                        style={[styles.listItem, currentLine === line && styles.listItemSelected]}
                        onPress={() => handleSelectLine(line)}
                      >
                        <Text style={styles.listItemText}>{line}</Text>
                        {currentLine === line && (
                          <MaterialCommunityIcons name="check" size={20} color={COLORS.primary} />
                        )}
                      </TouchableOpacity>
                    ))}
                  </>
                )}
              </>
            ) : (
              // Mostrar marcas con separación entre preferidas y otras
              <>
                {preferredBrands.length > 0 &&
                  filteredBrands.some(brand => preferredBrands.includes(brand)) && (
                    <>
                      <Text style={styles.listSectionTitle}>Tus marcas preferidas</Text>
                      {filteredBrands
                        .filter(brand => preferredBrands.includes(brand))
                        .map(brand => (
                          <TouchableOpacity
                            key={brand}
                            style={[
                              styles.listItem,
                              currentBrand === brand && !showLines && styles.listItemSelected,
                            ]}
                            onPress={() => handleSelectBrand(brand)}
                          >
                            <View style={styles.listItemLeft}>
                              <MaterialCommunityIcons
                                name="star"
                                size={18}
                                color={COLORS.warning}
                              />
                              <Text style={styles.listItemText}>{brand}</Text>
                            </View>
                            {showLines && brands[brand]?.lines?.length > 0 && (
                              <View style={styles.listItemRight}>
                                <Text style={styles.lineCount}>
                                  {brands[brand].lines.length} líneas
                                </Text>
                                <MaterialCommunityIcons
                                  name="chevron-right"
                                  size={20}
                                  color={COLORS.gray[400]}
                                />
                              </View>
                            )}
                            {currentBrand === brand && !showLines && (
                              <MaterialCommunityIcons
                                name="check"
                                size={20}
                                color={COLORS.primary}
                              />
                            )}
                          </TouchableOpacity>
                        ))}
                    </>
                  )}

                {filteredBrands.some(brand => !preferredBrands.includes(brand)) && (
                  <>
                    <Text style={styles.listSectionTitle}>
                      {preferredBrands.length > 0 ? 'Otras marcas' : 'Todas las marcas'}
                    </Text>
                    {filteredBrands
                      .filter(brand => !preferredBrands.includes(brand))
                      .map(brand => (
                        <TouchableOpacity
                          key={brand}
                          style={[
                            styles.listItem,
                            currentBrand === brand && !showLines && styles.listItemSelected,
                          ]}
                          onPress={() => handleSelectBrand(brand)}
                        >
                          <Text style={styles.listItemText}>{brand}</Text>
                          {showLines && brands[brand]?.lines?.length > 0 && (
                            <View style={styles.listItemRight}>
                              <Text style={styles.lineCount}>
                                {brands[brand].lines.length} líneas
                              </Text>
                              <MaterialCommunityIcons
                                name="chevron-right"
                                size={20}
                                color={COLORS.gray[400]}
                              />
                            </View>
                          )}
                          {currentBrand === brand && !showLines && (
                            <MaterialCommunityIcons name="check" size={20} color={COLORS.primary} />
                          )}
                        </TouchableOpacity>
                      ))}
                  </>
                )}
              </>
            )}
          </ScrollView>

          {/* Opción manual */}
          {allowManual && !selectedBrand && (
            <TouchableOpacity style={styles.manualButton} onPress={() => setShowManualInput(true)}>
              <MaterialCommunityIcons name="plus-circle-outline" size={20} color={COLORS.primary} />
              <Text style={styles.manualButtonText}>Añadir marca manualmente</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  backdrop: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: SCREEN_HEIGHT * 0.7,
    ...SHADOWS.lg,
  },
  modalContentExpanded: {
    maxHeight: SCREEN_HEIGHT * 0.8,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: SPACING.lg,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray[100],
  },
  title: {
    fontSize: FONT_SIZES.lg,
    fontWeight: '600',
    color: COLORS.gray[900],
    flex: 1,
    textAlign: 'center',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.gray[50],
    marginHorizontal: SPACING.lg,
    marginTop: SPACING.md,
    paddingHorizontal: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
  },
  searchInput: {
    flex: 1,
    paddingVertical: SPACING.sm,
    marginLeft: SPACING.sm,
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[900],
  },
  popularSection: {
    marginTop: SPACING.lg,
    paddingHorizontal: SPACING.lg,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.sm,
    fontWeight: '500',
    color: COLORS.gray[600],
    marginBottom: SPACING.sm,
  },
  popularBrands: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.sm,
  },
  popularChip: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.full,
    backgroundColor: COLORS.gray[100],
    borderWidth: 1,
    borderColor: COLORS.gray[200],
  },
  popularChipSelected: {
    backgroundColor: COLORS.primary + '20',
    borderColor: COLORS.primary,
  },
  popularChipText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[700],
    fontWeight: '500',
  },
  popularChipTextSelected: {
    color: COLORS.primary,
  },
  list: {
    marginTop: SPACING.md,
  },
  listItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray[50],
  },
  listItemSelected: {
    backgroundColor: COLORS.primary + '10',
  },
  listItemText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[900],
    flex: 1,
  },
  listItemSubtext: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[500],
    marginTop: 2,
  },
  listItemRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.xs,
  },
  lineCount: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[500],
  },
  listSectionTitle: {
    fontSize: FONT_SIZES.sm,
    fontWeight: '600',
    color: COLORS.gray[600],
    paddingHorizontal: SPACING.lg,
    paddingTop: SPACING.md,
    paddingBottom: SPACING.sm,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  listItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.sm,
    flex: 1,
  },
  manualButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: SPACING.sm,
    paddingVertical: SPACING.lg,
    marginHorizontal: SPACING.lg,
    marginVertical: SPACING.md,
    borderWidth: 1,
    borderColor: COLORS.primary,
    borderRadius: BORDER_RADIUS.md,
    borderStyle: 'dashed',
  },
  manualButtonText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.primary,
    fontWeight: '500',
  },
  manualForm: {
    padding: SPACING.lg,
  },
  inputLabel: {
    fontSize: FONT_SIZES.sm,
    fontWeight: '500',
    color: COLORS.gray[700],
    marginBottom: SPACING.xs,
  },
  input: {
    backgroundColor: COLORS.gray[50],
    borderWidth: 1,
    borderColor: COLORS.gray[200],
    borderRadius: BORDER_RADIUS.md,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.md,
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[900],
    marginBottom: SPACING.lg,
  },
  submitButton: {
    backgroundColor: COLORS.primary,
    paddingVertical: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    alignItems: 'center',
    marginTop: SPACING.md,
  },
  submitButtonDisabled: {
    opacity: 0.5,
  },
  submitButtonText: {
    fontSize: FONT_SIZES.md,
    fontWeight: '600',
    color: COLORS.white,
  },
});
