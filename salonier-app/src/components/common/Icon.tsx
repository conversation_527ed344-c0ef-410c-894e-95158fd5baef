import React from 'react';
import { StyleProp, ViewStyle } from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { ICONS } from '../../constants/design-system';

interface IconProps {
  name: 'home' | 'users' | 'calendar' | 'box' | 'settings' | 'arrow-left' | 'plus' | 'search' | 'filter' | 'check' | 'x' | 'pencil' | 'chevron-left';
  size?: keyof typeof ICONS.size;
  color?: string;
  style?: StyleProp<ViewStyle>;
}

const iconMap = {
  home: 'home-outline',
  users: 'account-group-outline',
  calendar: 'calendar-blank-outline',
  box: 'package-variant-closed',
  settings: 'cog-outline',
  'arrow-left': 'arrow-left',
  'chevron-left': 'chevron-left',
  plus: 'plus',
  search: 'magnify',
  filter: 'filter-outline',
  check: 'check',
  x: 'close',
  pencil: 'pencil',
} as const;

export const Icon: React.FC<IconProps> = ({
  name,
  size = 'md',
  color = '#000',
  style,
}) => {
  const iconName = iconMap[name] || 'help-circle-outline';
  
  return (
    <MaterialCommunityIcons
      name={iconName as any}
      size={ICONS.size[size]}
      color={color}
      style={style}
    />
  );
};