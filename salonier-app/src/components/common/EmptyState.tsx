import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { COMPONENTS, TYPOGRAPHY, ICONS } from '../../constants/design-system';
import { COLORS, SPACING, BORDER_RADIUS } from '../../constants';

interface EmptyStateProps {
  icon?: string;
  iconComponent?: React.ReactNode;
  title: string;
  subtitle?: string;
  actionLabel?: string;
  onAction?: () => void;
  image?: any;
  style?: any;
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  icon,
  iconComponent,
  title,
  subtitle,
  actionLabel,
  onAction,
  image,
  style,
}) => {
  return (
    <View style={[styles.container, style]}>
      {image ? (
        <Image source={image} style={styles.image} resizeMode="contain" />
      ) : iconComponent ? (
        <View style={styles.iconContainer}>{iconComponent}</View>
      ) : icon ? (
        <View style={styles.iconContainer}>
          <MaterialIcons
            name={icon as any}
            size={ICONS.size.xl * 1.6}
            color={COLORS.textTertiary}
          />
        </View>
      ) : null}

      <Text style={styles.title}>{title}</Text>
      
      {subtitle && (
        <Text style={styles.subtitle}>{subtitle}</Text>
      )}

      {actionLabel && onAction && (
        <TouchableOpacity
          style={styles.button}
          onPress={onAction}
          activeOpacity={0.8}
        >
          <Text style={styles.buttonText}>{actionLabel}</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

// Variaciones predefinidas
export const EmptyClients: React.FC<{
  onAddClient: () => void;
}> = ({ onAddClient }) => (
  <EmptyState
    icon="people-outline"
    title="Sin clientes aún"
    subtitle="Empieza añadiendo tu primer cliente para comenzar a gestionar sus servicios"
    actionLabel="Añadir Cliente"
    onAction={onAddClient}
  />
);

export const EmptyInventory: React.FC<{
  onAddProduct: () => void;
}> = ({ onAddProduct }) => (
  <EmptyState
    icon="inventory-2"
    title="Inventario vacío"
    subtitle="Añade productos para mantener un control de tu stock"
    actionLabel="Añadir Producto"
    onAction={onAddProduct}
  />
);

export const EmptyAppointments: React.FC<{
  onAddAppointment: () => void;
}> = ({ onAddAppointment }) => (
  <EmptyState
    icon="event-available"
    title="Sin citas programadas"
    subtitle="Programa tu primera cita para organizar tu agenda"
    actionLabel="Nueva Cita"
    onAction={onAddAppointment}
  />
);

export const EmptySearchResults: React.FC<{
  searchQuery: string;
}> = ({ searchQuery }) => (
  <EmptyState
    icon="search-off"
    title="Sin resultados"
    subtitle={`No se encontraron resultados para "${searchQuery}"`}
  />
);

export const NoConnection: React.FC<{
  onRetry: () => void;
}> = ({ onRetry }) => (
  <EmptyState
    icon="wifi-off"
    title="Sin conexión"
    subtitle="Verifica tu conexión a internet e intenta de nuevo"
    actionLabel="Reintentar"
    onAction={onRetry}
  />
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.xl,
    minHeight: 300,
  },
  iconContainer: {
    marginBottom: SPACING.lg,
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.full,
    backgroundColor: COLORS.surface,
  },
  image: {
    width: 200,
    height: 200,
    marginBottom: SPACING.lg,
  },
  title: {
    fontSize: TYPOGRAPHY.size.xl,
    fontWeight: '600',
    color: COLORS.text,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    textAlign: 'center',
    marginBottom: SPACING.sm,
  },
  subtitle: {
    fontSize: TYPOGRAPHY.size.base,
    color: COLORS.textSecondary,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    textAlign: 'center',
    marginBottom: SPACING.lg,
    paddingHorizontal: SPACING.lg,
    lineHeight: TYPOGRAPHY.size.base * TYPOGRAPHY.lineHeight.normal,
  },
  button: {
    ...COMPONENTS.button.primary,
    marginTop: SPACING.sm,
  },
  buttonText: {
    color: COMPONENTS.button.primary.color,
    fontSize: COMPONENTS.button.primary.fontSize,
    fontFamily: TYPOGRAPHY.fontFamily.bold,
    fontWeight: COMPONENTS.button.primary.fontWeight,
    letterSpacing: COMPONENTS.button.primary.letterSpacing,
  },
});