import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TextInputProps,
  TouchableOpacity,
  Animated,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { COMPONENTS, TYPOGRAPHY, ICONS } from '../../constants/design-system';
import { COLORS, SPACING } from '../../constants';

interface FormInputProps extends TextInputProps {
  label?: string;
  error?: string;
  required?: boolean;
  hint?: string;
  icon?: string;
  rightIcon?: string;
  onRightIconPress?: () => void;
  containerStyle?: any;
}

export const FormInput: React.FC<FormInputProps> = ({
  label,
  error,
  required,
  hint,
  icon,
  rightIcon,
  onRightIconPress,
  containerStyle,
  style,
  onFocus,
  onBlur,
  ...props
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const animatedValue = React.useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    Animated.timing(animatedValue, {
      toValue: isFocused ? 1 : 0,
      duration: 200,
      useNativeDriver: false,
    }).start();
  }, [isFocused]);

  const handleFocus = (e: any) => {
    setIsFocused(true);
    onFocus?.(e);
  };

  const handleBlur = (e: any) => {
    setIsFocused(false);
    onBlur?.(e);
  };

  const borderColor = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [
      error ? COLORS.error : 'transparent',
      error ? COLORS.error : COLORS.secondary,
    ],
  });

  return (
    <View style={[styles.container, containerStyle]}>
      {label && (
        <View style={styles.labelContainer}>
          <Text style={styles.label}>{label}</Text>
          {required && <Text style={styles.required}>*</Text>}
        </View>
      )}

      <Animated.View
        style={[
          styles.inputContainer,
          COMPONENTS.input.base,
          isFocused && COMPONENTS.input.focused,
          error && COMPONENTS.input.error,
          { borderColor },
        ]}
      >
        {icon && (
          <MaterialIcons
            name={icon as any}
            size={ICONS.size.sm}
            color={isFocused ? COLORS.secondary : COLORS.textTertiary}
            style={styles.leftIcon}
          />
        )}

        <TextInput
          style={[
            styles.input,
            icon && styles.inputWithLeftIcon,
            rightIcon && styles.inputWithRightIcon,
            style,
          ]}
          placeholderTextColor={COLORS.textTertiary}
          onFocus={handleFocus}
          onBlur={handleBlur}
          {...props}
        />

        {rightIcon && (
          <TouchableOpacity
            onPress={onRightIconPress}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <MaterialIcons
              name={rightIcon as any}
              size={ICONS.size.sm}
              color={COLORS.textTertiary}
            />
          </TouchableOpacity>
        )}
      </Animated.View>

      {error && (
        <View style={styles.errorContainer}>
          <MaterialIcons
            name="error-outline"
            size={ICONS.size.xs}
            color={COLORS.error}
            style={styles.errorIcon}
          />
          <Text style={styles.errorText}>{error}</Text>
        </View>
      )}

      {hint && !error && (
        <Text style={styles.hintText}>{hint}</Text>
      )}
    </View>
  );
};

// Variaciones especializadas
export const EmailInput: React.FC<Omit<FormInputProps, 'keyboardType' | 'autoCapitalize'>> = (props) => (
  <FormInput
    {...props}
    keyboardType="email-address"
    autoCapitalize="none"
    autoCorrect={false}
    icon="email"
  />
);

export const PhoneInput: React.FC<Omit<FormInputProps, 'keyboardType'>> = (props) => (
  <FormInput
    {...props}
    keyboardType="phone-pad"
    icon="phone"
  />
);

export const PasswordInput: React.FC<Omit<FormInputProps, 'secureTextEntry'>> = (props) => {
  const [showPassword, setShowPassword] = useState(false);

  return (
    <FormInput
      {...props}
      secureTextEntry={!showPassword}
      icon="lock"
      rightIcon={showPassword ? "visibility-off" : "visibility"}
      onRightIconPress={() => setShowPassword(!showPassword)}
    />
  );
};

export const SearchInput: React.FC<FormInputProps> = (props) => (
  <FormInput
    {...props}
    icon="search"
    rightIcon={props.value ? "close" : undefined}
    autoCorrect={false}
    autoCapitalize="none"
  />
);

const styles = StyleSheet.create({
  container: {
    marginBottom: SPACING.md,
  },
  labelContainer: {
    flexDirection: 'row',
    marginBottom: SPACING.sm,
  },
  label: {
    fontSize: TYPOGRAPHY.size.sm,
    fontWeight: '500',
    color: COLORS.text,
    fontFamily: TYPOGRAPHY.fontFamily.medium,
  },
  required: {
    color: COLORS.error,
    marginLeft: SPACING.xs,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    minHeight: 48,
  },
  input: {
    flex: 1,
    fontSize: TYPOGRAPHY.size.base,
    color: COLORS.text,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    paddingVertical: SPACING.sm,
  },
  inputWithLeftIcon: {
    paddingLeft: SPACING.sm,
  },
  inputWithRightIcon: {
    paddingRight: SPACING.sm,
  },
  leftIcon: {
    marginRight: SPACING.sm,
  },
  errorBorder: {
    // Now handled by COMPONENTS.input.error
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: SPACING.xs,
  },
  errorIcon: {
    marginRight: SPACING.xs,
  },
  errorText: {
    fontSize: TYPOGRAPHY.size.xs,
    color: COLORS.error,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
  },
  hintText: {
    fontSize: TYPOGRAPHY.size.xs,
    color: COLORS.textSecondary,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    marginTop: SPACING.xs,
  },
});