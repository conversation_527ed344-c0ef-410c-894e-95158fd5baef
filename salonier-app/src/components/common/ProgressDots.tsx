import React from 'react';
import {
  View,
  StyleSheet,
  Animated,
} from 'react-native';
import { COLORS, SPACING } from '../../constants';

interface ProgressDotsProps {
  total: number;
  current: number;
  color?: string;
}

export default function ProgressDots({ 
  total, 
  current, 
  color = COLORS.primary 
}: ProgressDotsProps) {
  return (
    <View style={styles.container}>
      {Array.from({ length: total }).map((_, index) => {
        const isActive = index < current;
        const isCurrent = index === current - 1;
        
        return (
          <View
            key={index}
            style={[
              styles.dot,
              isActive && { backgroundColor: color },
              isCurrent && styles.currentDot,
            ]}
          />
        );
      })}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: SPACING.xs,
    paddingVertical: SPACING.md,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: COLORS.gray[300],
  },
  currentDot: {
    width: 24,
    height: 8,
  },
});