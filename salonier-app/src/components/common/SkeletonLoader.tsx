import React, { useEffect, useRef } from 'react';
import { View, StyleSheet, Animated, ViewStyle, DimensionValue } from 'react-native';
import { COLORS } from '../../constants';

interface SkeletonLoaderProps {
  width?: DimensionValue;
  height?: number;
  borderRadius?: number;
  style?: ViewStyle;
  variant?: 'text' | 'rect' | 'circle';
  lines?: number;
  spacing?: number;
}

export const SkeletonLoader: React.FC<SkeletonLoaderProps> = ({
  width = '100%',
  height = 20,
  borderRadius = 4,
  style,
  variant = 'rect',
  lines = 1,
  spacing = 8,
}) => {
  const shimmerAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(shimmerAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(shimmerAnim, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    );
    animation.start();

    return () => animation.stop();
  }, []);

  const opacity = shimmerAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0.3, 0.7],
  });

  const getVariantStyles = (): ViewStyle => {
    switch (variant) {
      case 'circle':
        return {
          width: height,
          height: height,
          borderRadius: height / 2,
        };
      case 'text':
        return {
          width,
          height: height,
          borderRadius: borderRadius,
        };
      default:
        return {
          width,
          height,
          borderRadius,
        };
    }
  };

  const renderSkeleton = () => (
    <Animated.View style={[styles.skeleton, getVariantStyles(), { opacity }, style]} />
  );

  if (lines > 1) {
    return (
      <View>
        {Array.from({ length: lines }).map((_, index) => (
          <View key={index} style={{ marginBottom: index < lines - 1 ? spacing : 0 }}>
            {renderSkeleton()}
          </View>
        ))}
      </View>
    );
  }

  return renderSkeleton();
};

// Componente para listas completas
export const SkeletonList: React.FC<{
  count?: number;
  itemHeight?: number;
  spacing?: number;
}> = ({ count = 5, spacing = 12 }) => {
  return (
    <View style={styles.listContainer}>
      {Array.from({ length: count }).map((_, index) => (
        <View
          key={index}
          style={[styles.listItem, { marginBottom: index < count - 1 ? spacing : 0 }]}
        >
          <View style={styles.listItemContent}>
            <SkeletonLoader variant="circle" height={48} style={styles.avatar} />
            <View style={styles.textContent}>
              <SkeletonLoader width="60%" height={16} style={styles.title} />
              <SkeletonLoader width="40%" height={14} />
            </View>
          </View>
        </View>
      ))}
    </View>
  );
};

// Componente para tarjetas
export const SkeletonCard: React.FC<{
  width?: DimensionValue;
  height?: number;
}> = ({ width = '100%', height = 200 }) => {
  return (
    <View style={[styles.card, { width, height }]}>
      <SkeletonLoader height={120} borderRadius={8} style={styles.cardImage} />
      <View style={styles.cardContent}>
        <SkeletonLoader width="70%" height={18} style={styles.cardTitle} />
        <SkeletonLoader lines={2} height={14} spacing={4} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  skeleton: {
    backgroundColor: COLORS.gray[200],
  },
  listContainer: {
    padding: 16,
  },
  listItem: {
    backgroundColor: COLORS.white,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
  },
  listItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    marginRight: 12,
  },
  textContent: {
    flex: 1,
  },
  title: {
    marginBottom: 4,
  },
  card: {
    backgroundColor: COLORS.white,
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 3,
  },
  cardImage: {
    marginBottom: 0,
  },
  cardContent: {
    padding: 12,
  },
  cardTitle: {
    marginBottom: 8,
  },
});
