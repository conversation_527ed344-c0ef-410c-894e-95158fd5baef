import React from 'react';
import {
  View,
  ActivityIndicator,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
} from 'react-native';

// Hook para gestionar estados de carga
import { useState, useCallback } from 'react';

import { Animated } from 'react-native';
// import { BlurView } from 'expo-blur';

interface LoadingViewProps {
  loading: boolean;
  message?: string;
  fullScreen?: boolean;
  overlay?: boolean;
  size?: 'small' | 'large';
  color?: string;
  style?: ViewStyle;
  textStyle?: TextStyle;
  children?: React.ReactNode;
}

export const LoadingView: React.FC<LoadingViewProps> = ({
  loading,
  message,
  fullScreen = false,
  overlay = false,
  size = 'large',
  color = '#6366F1',
  style,
  textStyle,
  children,
}) => {
  if (!loading && children) {
    return <>{children}</>;
  }

  if (!loading) {
    return null;
  }

  const content = (
    <View style={[styles.loadingContainer, style]}>
      <ActivityIndicator size={size} color={color} />
      {message && (
        <Text style={[styles.loadingText, textStyle]}>{message}</Text>
      )}
    </View>
  );

  if (overlay && children) {
    return (
      <>
        {children}
        <View style={[StyleSheet.absoluteFillObject, { backgroundColor: 'rgba(0,0,0,0.5)' }]}>
          <View style={StyleSheet.absoluteFillObject}>
            {content}
          </View>
        </View>
      </>
    );
  }

  if (fullScreen) {
    return <View style={styles.fullScreenContainer}>{content}</View>;
  }

  return content;
};

interface LoadingState {
  isLoading: boolean;
  message?: string;
}

export function useLoading(initialMessage?: string) {
  const [state, setState] = useState<LoadingState>({
    isLoading: false,
    message: initialMessage,
  });

  const startLoading = useCallback((message?: string) => {
    setState({ isLoading: true, message });
  }, []);

  const stopLoading = useCallback(() => {
    setState({ isLoading: false, message: undefined });
  }, []);

  const updateMessage = useCallback((message: string) => {
    setState(prev => ({ ...prev, message }));
  }, []);

  const withLoading = useCallback(
    async <T,>(
      asyncFn: () => Promise<T>,
      loadingMessage?: string
    ): Promise<T> => {
      try {
        startLoading(loadingMessage);
        const result = await asyncFn();
        return result;
      } finally {
        stopLoading();
      }
    },
    [startLoading, stopLoading]
  );

  return {
    isLoading: state.isLoading,
    message: state.message,
    startLoading,
    stopLoading,
    updateMessage,
    withLoading,
  };
}

// Componente de Skeleton Loading
interface SkeletonProps {
  width?: number | string;
  height?: number | string;
  borderRadius?: number;
  style?: ViewStyle;
}

export const Skeleton: React.FC<SkeletonProps> = ({
  width = '100%',
  height = 20,
  borderRadius = 4,
  style,
}) => {
  const [opacity] = React.useState(new Animated.Value(0.3));

  React.useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(opacity, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(opacity, {
          toValue: 0.3,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    );

    animation.start();

    return () => animation.stop();
  }, [opacity]);

  return (
    <Animated.View
      style={[
        {
          width: width as any,
          height: height as any,
          borderRadius,
          backgroundColor: '#E0E0E0',
          opacity,
        },
        style,
      ]}
    />
  );
};

// Componente de lista con skeleton
interface SkeletonListProps {
  count?: number;
  itemHeight?: number;
  spacing?: number;
}

export const SkeletonList: React.FC<SkeletonListProps> = ({
  count = 5,
  // itemHeight = 80,
  spacing = 10,
}) => {
  return (
    <View style={styles.skeletonList}>
      {Array.from({ length: count }).map((_, index) => (
        <View key={index} style={{ marginBottom: spacing }}>
          <View style={styles.skeletonItem}>
            <Skeleton width={60} height={60} borderRadius={30} />
            <View style={styles.skeletonContent}>
              <Skeleton height={16} width="70%" />
              <Skeleton height={14} width="50%" style={{ marginTop: 8 }} />
            </View>
          </View>
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  fullScreenContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  skeletonList: {
    padding: 16,
  },
  skeletonItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: 'white',
    borderRadius: 8,
  },
  skeletonContent: {
    flex: 1,
    marginLeft: 16,
  },
});