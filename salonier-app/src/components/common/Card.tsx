import React from 'react';
import { View, ViewStyle, StyleSheet, StyleProp } from 'react-native';
import { COMPONENTS, SHADOWS } from '../../constants/design-system';
import { SPACING, BORDER_RADIUS } from '../../constants';

interface CardProps {
  children: React.ReactNode;
  style?: StyleProp<ViewStyle>;
  padding?: keyof typeof SPACING;
  borderRadius?: keyof typeof BORDER_RADIUS;
  elevation?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'base' | 'elevated';
}

export default function Card({ 
  children, 
  style, 
  padding = 'md',
  borderRadius = 'xl',
  elevation = 'sm',
  variant = 'base'
}: CardProps) {
  const cardStyle = variant === 'elevated' ? COMPONENTS.card.elevated : COMPONENTS.card.base;
  const shadowStyle = SHADOWS[elevation];

  return (
    <View 
      style={[
        styles.card,
        cardStyle,
        shadowStyle,
        {
          padding: SPACING[padding],
          borderRadius: BORDER_RADIUS[borderRadius],
        },
        style
      ]}
    >
      {children}
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    // Base styles are now handled by COMPONENTS.card
  },
});