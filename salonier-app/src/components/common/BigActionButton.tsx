import React, { useRef } from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  Animated,
  ViewStyle,
  TextStyle,
  Vibration,
} from 'react-native';
import { COLORS, SPACING, BORDER_RADIUS } from '../../constants';
import { TYPOGRAPHY, SHADOWS } from '../../constants/design-system';

interface BigActionButtonProps {
  onPress: () => void;
  title: string;
  subtitle?: string;
  emoji?: string;
  disabled?: boolean;
  variant?: 'primary' | 'secondary' | 'success' | 'warning';
  style?: ViewStyle;
  textStyle?: TextStyle;
}

export default function BigActionButton({
  onPress,
  title,
  subtitle,
  emoji,
  disabled = false,
  variant = 'primary',
  style,
  textStyle,
}: BigActionButtonProps) {
  const scaleAnim = useRef(new Animated.Value(1)).current;

  const handlePressIn = () => {
    Animated.spring(scaleAnim, {
      toValue: 0.95,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      friction: 3,
      tension: 40,
      useNativeDriver: true,
    }).start();
  };

  const handlePress = () => {
    if (!disabled) {
      Vibration.vibrate(10);
      onPress();
    }
  };

  const getButtonColors = () => {
    if (disabled) {
      return {
        background: COLORS.gray[200],
        text: COLORS.gray[500],
      };
    }

    switch (variant) {
      case 'secondary':
        return {
          background: COLORS.secondary,
          text: COLORS.white,
        };
      case 'success':
        return {
          background: COLORS.success,
          text: COLORS.white,
        };
      case 'warning':
        return {
          background: COLORS.warning,
          text: COLORS.white,
        };
      default:
        return {
          background: COLORS.primary,
          text: COLORS.white,
        };
    }
  };

  const colors = getButtonColors();

  return (
    <Animated.View
      style={[
        { transform: [{ scale: scaleAnim }] },
        style,
      ]}
    >
      <TouchableOpacity
        style={[
          styles.button,
          { backgroundColor: colors.background },
          disabled && styles.buttonDisabled,
        ]}
        onPress={handlePress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={0.9}
        disabled={disabled}
      >
        {emoji && <Text style={styles.emoji}>{emoji}</Text>}
        <Text style={[styles.title, { color: colors.text }, textStyle]}>
          {title}
        </Text>
        {subtitle && (
          <Text style={[styles.subtitle, { color: colors.text, opacity: 0.8 }]}>
            {subtitle}
          </Text>
        )}
      </TouchableOpacity>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  button: {
    paddingVertical: SPACING.xl,
    paddingHorizontal: SPACING.xl * 1.5,
    borderRadius: BORDER_RADIUS.full,
    alignItems: 'center',
    justifyContent: 'center',
    ...SHADOWS.lg,
    minHeight: 64,
  },
  buttonDisabled: {
    ...SHADOWS.sm,
  },
  emoji: {
    fontSize: 32,
    marginBottom: SPACING.sm,
  },
  title: {
    fontSize: TYPOGRAPHY.size.lg,
    fontFamily: TYPOGRAPHY.fontFamily.bold,
    fontWeight: '700',
    letterSpacing: TYPOGRAPHY.letterSpacing.wide,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    marginTop: SPACING.xs,
    textAlign: 'center',
  },
});