import React, { useEffect, useRef } from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  ActivityIndicator,
  Animated,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { COMPONENTS, TYPOGRAPHY, ICONS, STATES } from '../../constants/design-system';
import { COLORS, SPACING, BORDER_RADIUS } from '../../constants';

interface ProgressButtonProps {
  onPress: () => void | Promise<void>;
  title: string;
  loading?: boolean;
  disabled?: boolean;
  variant?: 'primary' | 'secondary' | 'danger' | 'success';
  size?: 'small' | 'medium' | 'large';
  icon?: string;
  iconPosition?: 'left' | 'right';
  fullWidth?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
  showSuccess?: boolean;
  successDuration?: number;
}

export const ProgressButton: React.FC<ProgressButtonProps> = ({
  onPress,
  title,
  loading = false,
  disabled = false,
  variant = 'primary',
  size = 'medium',
  icon,
  iconPosition = 'left',
  fullWidth = false,
  style,
  textStyle,
  showSuccess = false,
  successDuration = 2000,
}) => {
  const [isLoading, setIsLoading] = React.useState(loading);
  const [isSuccess, setIsSuccess] = React.useState(false);
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const successAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    setIsLoading(loading);
  }, [loading]);

  const handlePressIn = () => {
    Animated.spring(scaleAnim, {
      toValue: 0.95,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      friction: 3,
      tension: 40,
      useNativeDriver: true,
    }).start();
  };

  const handlePress = async () => {
    if (disabled || isLoading) return;

    const result = onPress();
    
    if (result instanceof Promise) {
      setIsLoading(true);
      try {
        await result;
        if (showSuccess) {
          showSuccessAnimation();
        }
      } finally {
        setIsLoading(false);
      }
    }
  };

  const showSuccessAnimation = () => {
    setIsSuccess(true);
    Animated.sequence([
      Animated.timing(successAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.delay(successDuration),
      Animated.timing(successAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setIsSuccess(false);
    });
  };

  const getButtonStyles = (): ViewStyle[] => {
    const baseStyles: ViewStyle[] = [styles.button, styles[`button_${size}`] as ViewStyle];
    
    if (fullWidth) baseStyles.push(styles.fullWidth as ViewStyle);
    
    const variantStyles = {
      primary: styles.primaryButton,
      secondary: styles.secondaryButton,
      danger: styles.dangerButton,
      success: styles.successButton,
    };
    
    baseStyles.push(variantStyles[variant] as ViewStyle);
    
    if (disabled || isLoading) {
      baseStyles.push(styles.disabledButton as ViewStyle);
    }
    
    if (isSuccess) {
      baseStyles.push(styles.successStateButton as ViewStyle);
    }
    
    return baseStyles;
  };

  const getTextStyles = (): TextStyle[] => {
    const baseStyles: TextStyle[] = [styles.text, styles[`text_${size}`] as TextStyle];
    
    const variantStyles = {
      primary: styles.primaryText,
      secondary: styles.secondaryText,
      danger: styles.dangerText,
      success: styles.successText,
    };
    
    baseStyles.push(variantStyles[variant] as TextStyle);
    
    if (disabled) {
      baseStyles.push(styles.disabledText as TextStyle);
    }
    
    return baseStyles;
  };

  const renderContent = () => {
    if (isLoading) {
      return (
        <ActivityIndicator
          size={size === 'small' ? 'small' : 'small'}
          color={variant === 'secondary' ? COLORS.primary : COLORS.white}
        />
      );
    }

    if (isSuccess) {
      return (
        <Animated.View style={{ opacity: successAnim }}>
          <MaterialIcons
            name="check"
            size={size === 'small' ? 18 : size === 'large' ? 24 : 20}
            color={COLORS.white}
          />
        </Animated.View>
      );
    }

    return (
      <>
        {icon && iconPosition === 'left' && (
          <MaterialIcons
            name={icon as any}
            size={size === 'small' ? ICONS.size.xs : size === 'large' ? ICONS.size.md : ICONS.size.sm}
            color={variant === 'secondary' ? COLORS.primary : COLORS.white}
            style={styles.iconLeft}
          />
        )}
        <Text style={[...getTextStyles(), textStyle]}>{title}</Text>
        {icon && iconPosition === 'right' && (
          <MaterialIcons
            name={icon as any}
            size={size === 'small' ? ICONS.size.xs : size === 'large' ? ICONS.size.md : ICONS.size.sm}
            color={variant === 'secondary' ? COLORS.primary : COLORS.white}
            style={styles.iconRight}
          />
        )}
      </>
    );
  };

  return (
    <Animated.View style={[{ transform: [{ scale: scaleAnim }] }, style]}>
      <TouchableOpacity
        style={getButtonStyles()}
        onPress={handlePress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        disabled={disabled || isLoading}
        activeOpacity={0.8}
      >
        {renderContent()}
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: BORDER_RADIUS.full,
    overflow: 'hidden',
  },
  button_small: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    minHeight: 32,
  },
  button_medium: {
    ...COMPONENTS.button.primary,
    minHeight: 44,
  },
  button_large: {
    paddingHorizontal: SPACING.xl,
    paddingVertical: SPACING.md,
    minHeight: 56,
  },
  fullWidth: {
    width: '100%',
  },
  primaryButton: {
    backgroundColor: COMPONENTS.button.primary.backgroundColor,
  },
  secondaryButton: {
    backgroundColor: COMPONENTS.button.secondary.backgroundColor,
  },
  dangerButton: {
    backgroundColor: COLORS.error,
  },
  successButton: {
    backgroundColor: COLORS.success,
  },
  successStateButton: {
    backgroundColor: COLORS.success,
  },
  disabledButton: {
    opacity: STATES.disabled.opacity,
  },
  text: {
    fontFamily: TYPOGRAPHY.fontFamily.bold,
    letterSpacing: TYPOGRAPHY.letterSpacing.wide,
  },
  text_small: {
    fontSize: TYPOGRAPHY.size.sm,
  },
  text_medium: {
    fontSize: TYPOGRAPHY.size.base,
  },
  text_large: {
    fontSize: TYPOGRAPHY.size.lg,
  },
  primaryText: {
    color: COMPONENTS.button.primary.color,
  },
  secondaryText: {
    color: COMPONENTS.button.secondary.color,
  },
  dangerText: {
    color: COLORS.white,
  },
  successText: {
    color: COLORS.white,
  },
  disabledText: {
    opacity: 0.7,
  },
  iconLeft: {
    marginRight: SPACING.sm,
  },
  iconRight: {
    marginLeft: SPACING.sm,
  },
});