import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
} from 'react-native';
import { COLORS, SPACING, FONT_SIZES, BORDER_RADIUS } from '../../constants';
import Card from '../common/Card';
import { ColorPatch } from '../../services/patchExtractor';

interface PatchPreviewProps {
  patches: ColorPatch[];
  showLabels?: boolean;
}

export default function PatchPreview({ patches, showLabels = true }: PatchPreviewProps) {
  const groupedPatches = patches.reduce((acc, patch) => {
    if (!acc[patch.zone]) {
      acc[patch.zone] = [];
    }
    acc[patch.zone].push(patch);
    return acc;
  }, {} as Record<string, ColorPatch[]>);

  const zoneLabels = {
    roots: '🌱 Raíces',
    mid: '〰️ Medios',
    ends: '✨ Puntas'
  };

  return (
    <Card style={styles.container} elevation="low">
      <View style={styles.header}>
        <Text style={styles.title}>Parches de Color Extraídos</Text>
        <Text style={styles.subtitle}>
          Estas pequeñas muestras serán analizadas
        </Text>
      </View>

      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.patchesContainer}
      >
        {Object.entries(groupedPatches).map(([zone, zonePatches]) => (
          <View key={zone} style={styles.zoneGroup}>
            {showLabels && (
              <Text style={styles.zoneLabel}>
                {zoneLabels[zone as keyof typeof zoneLabels] || zone}
              </Text>
            )}
            <View style={styles.zonePatches}>
              {zonePatches.map((patch, index) => (
                <View key={`${zone}-${index}`} style={styles.patchWrapper}>
                  <Image 
                    source={{ uri: `data:image/jpeg;base64,${patch.base64}` }}
                    style={styles.patch}
                  />
                  <Text style={styles.patchSize}>75x75px</Text>
                </View>
              ))}
            </View>
          </View>
        ))}
      </ScrollView>

      <View style={styles.infoBox}>
        <Text style={styles.infoIcon}>🔒</Text>
        <Text style={styles.infoText}>
          Los parches son tan pequeños que es imposible identificar a una persona
        </Text>
      </View>
    </Card>
  );
}

const styles = StyleSheet.create({
  container: {
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
  },
  header: {
    marginBottom: SPACING.md,
  },
  title: {
    fontSize: FONT_SIZES.md,
    fontWeight: '600',
    color: COLORS.gray[900],
    marginBottom: SPACING.xs,
  },
  subtitle: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
  },
  patchesContainer: {
    paddingVertical: SPACING.sm,
    gap: SPACING.lg,
  },
  zoneGroup: {
    marginRight: SPACING.lg,
  },
  zoneLabel: {
    fontSize: FONT_SIZES.sm,
    fontWeight: '600',
    color: COLORS.gray[700],
    marginBottom: SPACING.sm,
    textAlign: 'center',
  },
  zonePatches: {
    flexDirection: 'row',
    gap: SPACING.sm,
  },
  patchWrapper: {
    alignItems: 'center',
  },
  patch: {
    width: 75,
    height: 75,
    borderRadius: BORDER_RADIUS.sm,
    borderWidth: 2,
    borderColor: COLORS.gray[300],
    backgroundColor: COLORS.gray[100],
  },
  patchSize: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.gray[500],
    marginTop: SPACING.xs,
  },
  infoBox: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.success + '10',
    padding: SPACING.sm,
    borderRadius: BORDER_RADIUS.sm,
    marginTop: SPACING.md,
  },
  infoIcon: {
    fontSize: 16,
    marginRight: SPACING.sm,
  },
  infoText: {
    flex: 1,
    fontSize: FONT_SIZES.xs,
    color: COLORS.gray[700],
  },
});