import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { COLORS, SPACING, FONT_SIZES, BORDER_RADIUS, HAIR_LEVELS } from '../../../constants';
import Card from '../../common/Card';
import Slider from '@react-native-community/slider';

interface ZoneData {
  level: number;
  porosity: 'low' | 'medium' | 'high';
  condition: 'damaged' | 'healthy' | 'very_healthy';
  grayPercentage?: number;
  tone?: string;
}

interface ZoneControlsProps {
  zones: {
    roots: ZoneData;
    mids: ZoneData;
    ends: ZoneData;
  };
  onChange: (zones: any) => void;
  mode: 'current' | 'desired';
}

export default function ZoneControls({ zones, onChange, mode }: ZoneControlsProps) {
  const updateZone = (zone: 'roots' | 'mids' | 'ends', field: string, value: any) => {
    onChange({
      ...zones,
      [zone]: {
        ...zones[zone],
        [field]: value,
      },
    });
  };

  const renderZoneSection = (zone: 'roots' | 'mids' | 'ends', zoneData: ZoneData) => {
    const zoneLabels = {
      roots: { icon: '🌱', name: 'Raíces' },
      mids: { icon: '〰️', name: 'Medios' },
      ends: { icon: '✨', name: 'Puntas' },
    };

    return (
      <View key={zone} style={styles.zoneSection}>
        <Text style={styles.zoneName}>
          {zoneLabels[zone].icon} {zoneLabels[zone].name}
        </Text>
        
        {/* Level */}
        <View style={styles.parameter}>
          <Text style={styles.paramLabel}>Nivel</Text>
          <View style={styles.levelDisplay}>
            <View 
              style={[
                styles.levelSample, 
                { backgroundColor: HAIR_LEVELS[zoneData.level - 1]?.color || '#000' }
              ]} 
            />
            <Text style={styles.levelNumber}>{zoneData.level.toFixed(1)}</Text>
          </View>
          <Slider
            style={styles.slider}
            minimumValue={1}
            maximumValue={10}
            step={0.1}
            value={zoneData.level}
            onValueChange={(value) => updateZone(zone, 'level', Math.round(value * 10) / 10)}
            minimumTrackTintColor={COLORS.primary}
            maximumTrackTintColor={COLORS.gray[300]}
            thumbTintColor={COLORS.primary}
          />
        </View>

        {/* Gray percentage (only for current mode) */}
        {mode === 'current' && (
          <View style={styles.parameter}>
            <Text style={styles.paramLabel}>Canas</Text>
            <Text style={styles.paramValue}>{zoneData.grayPercentage || 0}%</Text>
            <Slider
              style={styles.slider}
              minimumValue={0}
              maximumValue={100}
              step={5}
              value={zoneData.grayPercentage || 0}
              onValueChange={(value) => updateZone(zone, 'grayPercentage', value)}
              minimumTrackTintColor={COLORS.gray[500]}
              maximumTrackTintColor={COLORS.gray[300]}
              thumbTintColor={COLORS.gray[700]}
            />
          </View>
        )}

        {/* Porosity */}
        <View style={styles.parameter}>
          <Text style={styles.paramLabel}>Porosidad</Text>
          <View style={styles.options}>
            {(['low', 'medium', 'high'] as const).map((porosity) => (
              <TouchableOpacity
                key={porosity}
                style={[
                  styles.option,
                  zoneData.porosity === porosity && styles.optionSelected
                ]}
                onPress={() => updateZone(zone, 'porosity', porosity)}
              >
                <Text style={[
                  styles.optionText,
                  zoneData.porosity === porosity && styles.optionTextSelected
                ]}>
                  {porosity === 'low' ? 'Baja' : porosity === 'medium' ? 'Media' : 'Alta'}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Condition */}
        <View style={styles.parameter}>
          <Text style={styles.paramLabel}>Estado</Text>
          <View style={styles.options}>
            {(['damaged', 'healthy', 'very_healthy'] as const).map((condition) => (
              <TouchableOpacity
                key={condition}
                style={[
                  styles.option,
                  zoneData.condition === condition && styles.optionSelected
                ]}
                onPress={() => updateZone(zone, 'condition', condition)}
              >
                <Text style={[
                  styles.optionText,
                  zoneData.condition === condition && styles.optionTextSelected
                ]}>
                  {condition === 'damaged' ? '🆘' : condition === 'healthy' ? '✅' : '✨'}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </View>
    );
  };

  return (
    <Card style={styles.container} elevation="medium">
      <Text style={styles.title}>Análisis por Zonas</Text>
      {(['roots', 'mids', 'ends'] as const).map((zone) => 
        renderZoneSection(zone, zones[zone])
      )}
    </Card>
  );
}

const styles = StyleSheet.create({
  container: {
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
  },
  title: {
    fontSize: FONT_SIZES.lg,
    fontWeight: '600',
    color: COLORS.gray[900],
    marginBottom: SPACING.md,
  },
  zoneSection: {
    marginBottom: SPACING.xl,
    paddingBottom: SPACING.lg,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray[200],
  },
  zoneName: {
    fontSize: FONT_SIZES.md,
    fontWeight: '600',
    color: COLORS.gray[900],
    marginBottom: SPACING.md,
  },
  parameter: {
    marginBottom: SPACING.md,
  },
  paramLabel: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
    marginBottom: SPACING.sm,
  },
  paramValue: {
    fontSize: FONT_SIZES.md,
    fontWeight: '600',
    color: COLORS.gray[900],
    marginBottom: SPACING.xs,
  },
  levelDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.sm,
    marginBottom: SPACING.sm,
  },
  levelSample: {
    width: 30,
    height: 30,
    borderRadius: BORDER_RADIUS.full,
    borderWidth: 1,
    borderColor: COLORS.gray[300],
  },
  levelNumber: {
    fontSize: FONT_SIZES.md,
    fontWeight: '600',
    color: COLORS.gray[900],
  },
  slider: {
    width: '100%',
    height: 30,
  },
  options: {
    flexDirection: 'row',
    gap: SPACING.sm,
  },
  option: {
    flex: 1,
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    backgroundColor: COLORS.gray[100],
    alignItems: 'center',
  },
  optionSelected: {
    backgroundColor: COLORS.primary,
  },
  optionText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[700],
  },
  optionTextSelected: {
    color: COLORS.white,
    fontWeight: '600',
  },
});