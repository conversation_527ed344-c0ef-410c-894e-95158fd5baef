import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { COLORS, SPACING, FONT_SIZES } from '../../../constants';
import Card from '../../common/Card';

interface ZoneToggleProps {
  enabled: boolean;
  onChange: (enabled: boolean) => void;
  subtitle?: string;
}

export default function ZoneToggle({ enabled, onChange, subtitle }: ZoneToggleProps) {
  return (
    <Card style={styles.container} elevation="medium">
      <TouchableOpacity
        style={styles.toggle}
        onPress={() => onChange(!enabled)}
      >
        <View style={styles.toggleContent}>
          <Text style={styles.toggleIcon}>{enabled ? '✅' : '⬜'}</Text>
          <View style={styles.toggleTextContainer}>
            <Text style={styles.toggleTitle}>Personalizar por zonas</Text>
            <Text style={styles.toggleSubtitle}>
              {subtitle || 'Analiza raíces, medios y puntas por separado'}
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    </Card>
  );
}

const styles = StyleSheet.create({
  container: {
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
  },
  toggle: {
    padding: SPACING.sm,
  },
  toggleContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.md,
  },
  toggleIcon: {
    fontSize: 24,
  },
  toggleTextContainer: {
    flex: 1,
  },
  toggleTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: '600',
    color: COLORS.gray[900],
  },
  toggleSubtitle: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
    marginTop: SPACING.xs,
  },
});