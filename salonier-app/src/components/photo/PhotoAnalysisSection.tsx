import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Animated,
} from 'react-native';
import { COLORS, SPACING, FONT_SIZES, BORDER_RADIUS } from '../../constants';
import { TYPOGRAPHY, SHADOWS, COMPONENTS } from '../../constants/design-system';
import { usePhotoAnalysis, PhotoAnalysisData, ZoneData } from '../../hooks/usePhotoAnalysis';
import Card from '../common/Card';
import PhotoModeSelector from './PhotoModeSelector';
import PhotoSourceSelector from './PhotoSourceSelector';
import PhotoPreview from './PhotoPreview';
import ZoneToggle from './ZoneAnalysis/ZoneToggle';
import ZoneControls from './ZoneAnalysis/ZoneControls';
import ManualColorSelector from './ManualSelection/ManualColorSelector';

interface PhotoAnalysisSectionProps {
  mode: 'current' | 'desired';
  title: string;
  subtitle?: string;
  onComplete: (data: PhotoAnalysisData) => void;
  initialData?: Partial<PhotoAnalysisData>;
  showComparison?: boolean;
  comparisonData?: any;
}

export default function PhotoAnalysisSection({
  mode,
  title,
  subtitle,
  onComplete,
  initialData,
  showComparison,
  comparisonData,
}: PhotoAnalysisSectionProps) {
  const photoAnalysis = usePhotoAnalysis(initialData);
  const [showSourceSelector, setShowSourceSelector] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [photoReady, setPhotoReady] = useState(false);
  const [analysisStep, setAnalysisStep] = useState(0);
  
  // Animaciones
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const progressAnim = useRef(new Animated.Value(0)).current;

  const handlePhotoCapture = async () => {
    const photoUri = await photoAnalysis.capturePhoto();
    if (photoUri) {
      await photoAnalysis.addPhoto(photoUri);
      setPhotoReady(true);
      setShowSourceSelector(false);
    }
  };

  const handleGallerySelection = async () => {
    const photoUris = await photoAnalysis.pickFromGallery(true);
    if (photoUris.length > 0) {
      for (const uri of photoUris) {
        await photoAnalysis.addPhoto(uri);
      }
      setPhotoReady(true);
      setShowSourceSelector(false);
    }
  };
  
  const handleAnalyzeWithAI = async () => {
    setIsAnalyzing(true);
    setAnalysisStep(0);
    
    // Iniciar animaciones
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 8,
        tension: 40,
        useNativeDriver: true,
      }),
    ]).start();
    
    // Simular pasos del análisis
    const steps = [
      { delay: 800, step: 1 },
      { delay: 1600, step: 2 },
      { delay: 2400, step: 3 },
    ];
    
    steps.forEach(({ delay, step }) => {
      setTimeout(() => {
        setAnalysisStep(step);
        // Animar progreso
        Animated.timing(progressAnim, {
          toValue: step / 3,
          duration: 400,
          useNativeDriver: false,
        }).start();
      }, delay);
    });
    
    // Finalizar análisis
    setTimeout(() => {
      setIsAnalyzing(false);
      setPhotoReady(false);
      // Auto-switch to manual mode to show zone controls with AI results
      photoAnalysis.setMode('manual');
      
      // Reset animaciones
      fadeAnim.setValue(0);
      scaleAnim.setValue(0.8);
      progressAnim.setValue(0);
    }, 3000);
  };

  const handleSourceSelect = (source: 'camera' | 'gallery') => {
    if (source === 'camera') {
      handlePhotoCapture();
    } else {
      handleGallerySelection();
    }
  };

  const handleComplete = () => {
    const data = photoAnalysis.getAnalysisData();
    onComplete(data);
  };

  if (isAnalyzing || photoAnalysis.isProcessing) {
    return (
      <View style={styles.loadingContainer}>
        <View style={styles.analyzingAnimation}>
          <Text style={styles.analyzingIcon}>🤖</Text>
          <ActivityIndicator size="large" color={COLORS.primary} />
        </View>
        <Text style={styles.loadingText}>
          {mode === 'current' 
            ? 'Analizando tu color con IA...' 
            : 'Analizando color objetivo con IA...'}
        </Text>
        <Animated.View 
          style={[
            styles.analyzingSteps,
            {
              opacity: fadeAnim,
              transform: [{ scale: scaleAnim }]
            }
          ]}
        >
          <View style={styles.analyzingStep}>
            <Text style={[styles.stepIcon, analysisStep >= 1 && styles.stepIconActive]}>
              {analysisStep >= 1 ? '✓' : '○'}
            </Text>
            <Text style={[styles.stepText, analysisStep >= 1 && styles.stepTextActive]}>
              Detectando zonas del cabello
            </Text>
          </View>
          <View style={styles.analyzingStep}>
            <Text style={[styles.stepIcon, analysisStep >= 2 && styles.stepIconActive]}>
              {analysisStep >= 2 ? '✓' : '○'}
            </Text>
            <Text style={[styles.stepText, analysisStep >= 2 && styles.stepTextActive]}>
              Analizando niveles y tonos
            </Text>
          </View>
          <View style={styles.analyzingStep}>
            <Text style={[styles.stepIcon, analysisStep >= 3 && styles.stepIconActive]}>
              {analysisStep >= 3 ? '✓' : '○'}
            </Text>
            <Text style={[styles.stepText, analysisStep >= 3 && styles.stepTextActive]}>
              Calculando porcentaje de canas
            </Text>
          </View>
        </Animated.View>
        
        {/* Barra de progreso */}
        <View style={styles.progressContainer}>
          <View style={styles.progressBar}>
            <Animated.View 
              style={[
                styles.progressFill,
                {
                  width: progressAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: ['0%', '100%']
                  })
                }
              ]}
            />
          </View>
        </View>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.header}>
        <Text style={styles.title}>{title}</Text>
        {subtitle && <Text style={styles.subtitle}>{subtitle}</Text>}
      </View>

      {/* Mode Selector */}
      <PhotoModeSelector
        selectedMode={photoAnalysis.mode}
        onModeChange={photoAnalysis.setMode}
      />

      {/* Photo Mode Content */}
      {photoAnalysis.mode === 'photo' ? (
        <>
          {photoAnalysis.photos.length === 0 ? (
            <Card style={styles.photoCard} elevation="medium">
              <TouchableOpacity
                style={styles.addPhotoButton}
                onPress={() => setShowSourceSelector(true)}
              >
                <Text style={styles.addPhotoIcon}>📸</Text>
                <Text style={styles.addPhotoText}>Tomar Foto</Text>
                <Text style={styles.addPhotoSubtext}>
                  Captura tu cabello para análisis automático
                </Text>
              </TouchableOpacity>
            </Card>
          ) : (
            <>
              <PhotoPreview
                photos={photoAnalysis.photos}
                onRemove={(index) => {
                  photoAnalysis.removePhoto(index);
                  setPhotoReady(false);
                }}
                onAddMore={() => setShowSourceSelector(true)}
                maxPhotos={1}
              />
              
              {/* Botón de Análisis con IA */}
              {photoReady && (
                <TouchableOpacity 
                  style={styles.analyzeButton}
                  onPress={handleAnalyzeWithAI}
                >
                  <Text style={styles.analyzeButtonIcon}>🤖</Text>
                  <View style={styles.analyzeButtonContent}>
                    <Text style={styles.analyzeButtonText}>Analizar con IA</Text>
                    <Text style={styles.analyzeButtonSubtext}>
                      Detectará automáticamente niveles y características
                    </Text>
                  </View>
                  <View style={styles.analyzeButtonArrow}>
                    <Text style={styles.arrowText}>→</Text>
                  </View>
                </TouchableOpacity>
              )}
            </>
          )}
        </>
      ) : (
        /* Manual Mode Content */
        <ManualColorSelector
          mode={mode}
          initialLevel={initialData?.zoneAnalysis?.roots.level || 6}
          initialTone={initialData?.zoneAnalysis?.roots.tone}
          onChange={(level: number, tone?: string) => {
            // Update manual selection
            photoAnalysis.setZoneAnalysis(prev => ({
              ...prev,
              roots: { 
                ...prev.roots, 
                level, 
                tone,
                grayPercentage: prev.roots.grayPercentage || 0
              },
              mids: { 
                ...prev.mids, 
                level, 
                tone,
                grayPercentage: prev.mids.grayPercentage || 0
              },
              ends: { 
                ...prev.ends, 
                level, 
                tone,
                grayPercentage: prev.ends.grayPercentage || 0
              },
            }));
          }}
        />
      )}

      {/* Show photos if available in manual mode */}
      {photoAnalysis.mode === 'manual' && photoAnalysis.photos.length > 0 && (
        <PhotoPreview
          photos={photoAnalysis.photos}
          onRemove={photoAnalysis.removePhoto}
          onAddMore={() => setShowSourceSelector(true)}
          maxPhotos={1}
        />
      )}

      {/* Zone Customization */}
      <ZoneToggle
        enabled={photoAnalysis.customizeByZones}
        onChange={photoAnalysis.setCustomizeByZones}
        subtitle={mode === 'current' 
          ? 'Analiza raíces, medios y puntas por separado'
          : 'Define diferentes colores por zona'}
      />

      {photoAnalysis.customizeByZones && (
        <ZoneControls
          zones={photoAnalysis.zoneAnalysis as { roots: ZoneData; mids: ZoneData; ends: ZoneData; }}
          onChange={(zones: { roots: ZoneData; mids: ZoneData; ends: ZoneData; }) => photoAnalysis.setZoneAnalysis(zones)}
          mode={mode}
        />
      )}

      {/* Comparison (for desired color) */}
      {showComparison && comparisonData && (
        <Card style={styles.comparisonCard} elevation="low">
          <Text style={styles.comparisonTitle}>Comparación</Text>
          <View style={styles.comparisonContent}>
            <View style={styles.comparisonItem}>
              <Text style={styles.comparisonLabel}>Actual</Text>
              <View style={[styles.colorSample, { backgroundColor: '#8B6A47' }]} />
              <Text style={styles.comparisonValue}>Nivel {comparisonData.natural_level || 6}</Text>
            </View>
            <Text style={styles.comparisonArrow}>→</Text>
            <View style={styles.comparisonItem}>
              <Text style={styles.comparisonLabel}>Deseado</Text>
              <View style={[styles.colorSample, { backgroundColor: '#D4AF37' }]} />
              <Text style={styles.comparisonValue}>Nivel {photoAnalysis.zoneAnalysis.roots.level}</Text>
            </View>
          </View>
        </Card>
      )}

      {/* Confirm Button */}
      <TouchableOpacity 
        style={styles.confirmButton}
        onPress={handleComplete}
        disabled={photoAnalysis.mode === 'photo' && photoAnalysis.photos.length === 0}
      >
        <Text style={styles.confirmButtonText}>
          Confirmar {mode === 'current' ? 'Análisis' : 'Color Objetivo'}
        </Text>
      </TouchableOpacity>

      {/* Source Selector Modal */}
      {showSourceSelector && (
        <PhotoSourceSelector
          visible={showSourceSelector}
          onClose={() => setShowSourceSelector(false)}
          onSelect={handleSourceSelect}
        />
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.surface,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.xl,
  },
  analyzingAnimation: {
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  analyzingIcon: {
    fontSize: 48,
    marginBottom: SPACING.md,
  },
  loadingText: {
    marginTop: SPACING.md,
    fontSize: TYPOGRAPHY.size.lg,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    color: COLORS.text,
    textAlign: 'center',
    marginBottom: SPACING.xl,
  },
  analyzingSteps: {
    alignItems: 'flex-start',
    width: '100%',
  },
  analyzingStep: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  stepIcon: {
    fontSize: TYPOGRAPHY.size.base,
    color: COLORS.gray[400],
    marginRight: SPACING.sm,
    width: 20,
    textAlign: 'center',
  },
  stepIconActive: {
    color: COLORS.success,
  },
  stepText: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.gray[500],
  },
  stepTextActive: {
    color: COLORS.text,
    fontFamily: TYPOGRAPHY.fontFamily.medium,
  },
  progressContainer: {
    width: '80%',
    marginTop: SPACING.lg,
  },
  progressBar: {
    height: 4,
    backgroundColor: COLORS.gray[200],
    borderRadius: BORDER_RADIUS.xs,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: COLORS.primary,
  },
  header: {
    padding: SPACING.lg,
    paddingBottom: SPACING.md,
  },
  title: {
    fontSize: TYPOGRAPHY.size['2xl'],
    fontFamily: TYPOGRAPHY.fontFamily.bold,
    fontWeight: '700',
    color: COLORS.text,
    marginBottom: SPACING.xs,
    letterSpacing: TYPOGRAPHY.letterSpacing.tight,
  },
  subtitle: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
    lineHeight: TYPOGRAPHY.size.base * TYPOGRAPHY.lineHeight.normal,
  },
  photoCard: {
    ...COMPONENTS.card.elevated,
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
  },
  addPhotoButton: {
    padding: SPACING.xl,
    alignItems: 'center',
    backgroundColor: COLORS.surface,
    borderRadius: BORDER_RADIUS.lg,
    ...SHADOWS.sm,
  },
  addPhotoIcon: {
    fontSize: 48,
    marginBottom: SPACING.md,
  },
  addPhotoText: {
    fontSize: TYPOGRAPHY.size.lg,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: SPACING.xs,
  },
  addPhotoSubtext: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
  comparisonCard: {
    ...COMPONENTS.card.base,
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
    backgroundColor: COLORS.info + '10',
  },
  comparisonTitle: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: SPACING.md,
    letterSpacing: TYPOGRAPHY.letterSpacing.wide,
    textTransform: 'uppercase',
  },
  comparisonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  comparisonItem: {
    alignItems: 'center',
  },
  comparisonLabel: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
    marginBottom: SPACING.xs,
  },
  colorSample: {
    width: 50,
    height: 50,
    borderRadius: BORDER_RADIUS.full,
    marginBottom: SPACING.xs,
    borderWidth: 2,
    borderColor: COLORS.gray[200],
    ...SHADOWS.sm,
  },
  comparisonValue: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.text,
  },
  comparisonArrow: {
    fontSize: 24,
    marginHorizontal: SPACING.lg,
    color: COLORS.gray[400],
  },
  confirmButton: {
    ...COMPONENTS.button.primary,
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.xl,
    paddingVertical: SPACING.lg,
    ...SHADOWS.lg,
  },
  confirmButtonText: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.white,
    letterSpacing: TYPOGRAPHY.letterSpacing.wide,
  },
  analyzeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.primary,
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
    padding: SPACING.lg,
    borderRadius: BORDER_RADIUS.xl,
    ...SHADOWS.lg,
  },
  analyzeButtonIcon: {
    fontSize: 32,
    marginRight: SPACING.md,
  },
  analyzeButtonContent: {
    flex: 1,
  },
  analyzeButtonText: {
    fontSize: TYPOGRAPHY.size.lg,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.white,
    marginBottom: SPACING.xs,
  },
  analyzeButtonSubtext: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.white,
    opacity: 0.9,
  },
  analyzeButtonArrow: {
    marginLeft: SPACING.md,
  },
  arrowText: {
    fontSize: 24,
    color: COLORS.white,
  },
});