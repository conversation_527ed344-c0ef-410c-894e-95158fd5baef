import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { COLORS, SPACING, FONT_SIZES, BORDER_RADIUS } from '../../constants';
import Card from '../common/Card';

interface PhotoModeSelectorProps {
  selectedMode: 'photo' | 'manual';
  onModeChange: (mode: 'photo' | 'manual') => void;
}

export default function PhotoModeSelector({ selectedMode, onModeChange }: PhotoModeSelectorProps) {
  return (
    <Card style={styles.container} elevation="low">
      <Text style={styles.title}>¿Cómo deseas proceder?</Text>
      <View style={styles.options}>
        <TouchableOpacity
          style={[
            styles.option,
            selectedMode === 'photo' && styles.optionSelected
          ]}
          onPress={() => onModeChange('photo')}
        >
          <Text style={styles.optionIcon}>📸</Text>
          <Text style={[
            styles.optionText,
            selectedMode === 'photo' && styles.optionTextSelected
          ]}>
            Con Foto
          </Text>
          <Text style={styles.optionDescription}>
            Análisis automático con IA
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.option,
            selectedMode === 'manual' && styles.optionSelected
          ]}
          onPress={() => onModeChange('manual')}
        >
          <Text style={styles.optionIcon}>✋</Text>
          <Text style={[
            styles.optionText,
            selectedMode === 'manual' && styles.optionTextSelected
          ]}>
            Manual
          </Text>
          <Text style={styles.optionDescription}>
            Selección visual directa
          </Text>
        </TouchableOpacity>
      </View>
    </Card>
  );
}

const styles = StyleSheet.create({
  container: {
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
  },
  title: {
    fontSize: FONT_SIZES.lg,
    fontWeight: '600',
    color: COLORS.gray[900],
    marginBottom: SPACING.md,
    textAlign: 'center',
  },
  options: {
    flexDirection: 'row',
    gap: SPACING.md,
  },
  option: {
    flex: 1,
    alignItems: 'center',
    padding: SPACING.lg,
    borderRadius: BORDER_RADIUS.lg,
    backgroundColor: COLORS.gray[50],
    borderWidth: 2,
    borderColor: COLORS.gray[200],
  },
  optionSelected: {
    backgroundColor: COLORS.primary + '10',
    borderColor: COLORS.primary,
  },
  optionIcon: {
    fontSize: 36,
    marginBottom: SPACING.sm,
  },
  optionText: {
    fontSize: FONT_SIZES.md,
    fontWeight: '600',
    color: COLORS.gray[700],
    marginBottom: SPACING.xs,
  },
  optionTextSelected: {
    color: COLORS.primary,
  },
  optionDescription: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.gray[500],
    textAlign: 'center',
  },
});