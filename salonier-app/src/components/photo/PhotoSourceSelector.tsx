import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  TouchableWithoutFeedback,
} from 'react-native';
import { COLORS, SPACING, FONT_SIZES, BORDER_RADIUS } from '../../constants';

interface PhotoSourceSelectorProps {
  visible: boolean;
  onClose: () => void;
  onSelect: (source: 'camera' | 'gallery') => void;
}

export default function PhotoSourceSelector({ visible, onClose, onSelect }: PhotoSourceSelectorProps) {
  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.overlay}>
          <TouchableWithoutFeedback>
            <View style={styles.content}>
              <Text style={styles.title}>Selecciona una opción</Text>
              
              <TouchableOpacity
                style={styles.option}
                onPress={() => onSelect('camera')}
              >
                <View style={styles.optionContent}>
                  <Text style={styles.optionIcon}>📷</Text>
                  <View style={styles.optionTextContainer}>
                    <Text style={styles.optionTitle}>Usar Cámara</Text>
                    <Text style={styles.optionSubtitle}>
                      Captura una nueva foto
                    </Text>
                  </View>
                </View>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={styles.option}
                onPress={() => onSelect('gallery')}
              >
                <View style={styles.optionContent}>
                  <Text style={styles.optionIcon}>🖼️</Text>
                  <View style={styles.optionTextContainer}>
                    <Text style={styles.optionTitle}>Elegir de Galería</Text>
                    <Text style={styles.optionSubtitle}>
                      Selecciona una foto existente
                    </Text>
                  </View>
                </View>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={onClose}
              >
                <Text style={styles.cancelText}>Cancelar</Text>
              </TouchableOpacity>
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  content: {
    backgroundColor: COLORS.white,
    borderTopLeftRadius: BORDER_RADIUS.xl,
    borderTopRightRadius: BORDER_RADIUS.xl,
    padding: SPACING.xl,
    paddingBottom: SPACING.xl + 20,
  },
  title: {
    fontSize: FONT_SIZES.lg,
    fontWeight: '600',
    color: COLORS.gray[900],
    marginBottom: SPACING.lg,
    textAlign: 'center',
  },
  option: {
    backgroundColor: COLORS.gray[50],
    borderRadius: BORDER_RADIUS.lg,
    marginBottom: SPACING.md,
    overflow: 'hidden',
  },
  optionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: SPACING.lg,
  },
  optionIcon: {
    fontSize: 32,
    marginRight: SPACING.md,
  },
  optionTextContainer: {
    flex: 1,
  },
  optionTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: '600',
    color: COLORS.gray[900],
    marginBottom: SPACING.xs,
  },
  optionSubtitle: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
  },
  cancelButton: {
    alignItems: 'center',
    padding: SPACING.md,
    marginTop: SPACING.md,
  },
  cancelText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[600],
  },
});