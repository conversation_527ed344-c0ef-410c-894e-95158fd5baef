import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Dimensions,
} from 'react-native';
import { COLORS, SPACING, FONT_SIZES, BORDER_RADIUS } from '../../constants';
import Card from '../common/Card';

const { width: screenWidth } = Dimensions.get('window');
const PHOTO_SIZE = (screenWidth - SPACING.lg * 2 - SPACING.md * 3) / 4;

interface PhotoPreviewProps {
  photos: string[];
  onRemove: (index: number) => void;
  onAddMore?: () => void;
  maxPhotos?: number;
}

export default function PhotoPreview({ 
  photos, 
  onRemove, 
  onAddMore,
  maxPhotos = 4 
}: PhotoPreviewProps) {
  const canAddMore = photos.length < maxPhotos;

  return (
    <Card style={styles.container} elevation="medium">
      <View style={styles.header}>
        <Text style={styles.title}>Fotos capturadas</Text>
        <Text style={styles.count}>{photos.length}/{maxPhotos}</Text>
      </View>
      
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.photosContainer}
      >
        {photos.map((photo, index) => (
          <View key={index} style={styles.photoWrapper}>
            <Image source={{ uri: photo }} style={styles.photo} />
            <TouchableOpacity
              style={styles.removeButton}
              onPress={() => onRemove(index)}
            >
              <Text style={styles.removeIcon}>✕</Text>
            </TouchableOpacity>
            <View style={styles.photoLabel}>
              <Text style={styles.photoLabelText}>Foto {index + 1}</Text>
            </View>
          </View>
        ))}
        
        {canAddMore && onAddMore && (
          <TouchableOpacity
            style={styles.addMoreButton}
            onPress={onAddMore}
          >
            <Text style={styles.addMoreIcon}>+</Text>
            <Text style={styles.addMoreText}>Agregar</Text>
          </TouchableOpacity>
        )}
      </ScrollView>
      
      {photos.length > 0 && (
        <View style={styles.infoBox}>
          <Text style={styles.infoIcon}>💡</Text>
          <Text style={styles.infoText}>
            Las fotos han sido procesadas para proteger la privacidad
          </Text>
        </View>
      )}
    </Card>
  );
}

const styles = StyleSheet.create({
  container: {
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  title: {
    fontSize: FONT_SIZES.md,
    fontWeight: '600',
    color: COLORS.gray[900],
  },
  count: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
  },
  photosContainer: {
    flexDirection: 'row',
    gap: SPACING.md,
  },
  photoWrapper: {
    position: 'relative',
  },
  photo: {
    width: PHOTO_SIZE,
    height: PHOTO_SIZE,
    borderRadius: BORDER_RADIUS.md,
    backgroundColor: COLORS.gray[200],
  },
  removeButton: {
    position: 'absolute',
    top: -SPACING.xs,
    right: -SPACING.xs,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: COLORS.error,
    justifyContent: 'center',
    alignItems: 'center',
  },
  removeIcon: {
    color: COLORS.white,
    fontSize: 14,
    fontWeight: 'bold',
  },
  photoLabel: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    paddingVertical: SPACING.xs,
    borderBottomLeftRadius: BORDER_RADIUS.md,
    borderBottomRightRadius: BORDER_RADIUS.md,
  },
  photoLabelText: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.white,
    textAlign: 'center',
  },
  addMoreButton: {
    width: PHOTO_SIZE,
    height: PHOTO_SIZE,
    borderRadius: BORDER_RADIUS.md,
    backgroundColor: COLORS.gray[100],
    borderWidth: 2,
    borderColor: COLORS.gray[300],
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
  },
  addMoreIcon: {
    fontSize: 24,
    color: COLORS.gray[600],
  },
  addMoreText: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.gray[600],
    marginTop: SPACING.xs,
  },
  infoBox: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.info + '10',
    padding: SPACING.sm,
    borderRadius: BORDER_RADIUS.sm,
    marginTop: SPACING.md,
  },
  infoIcon: {
    fontSize: 16,
    marginRight: SPACING.sm,
  },
  infoText: {
    flex: 1,
    fontSize: FONT_SIZES.xs,
    color: COLORS.gray[700],
  },
});