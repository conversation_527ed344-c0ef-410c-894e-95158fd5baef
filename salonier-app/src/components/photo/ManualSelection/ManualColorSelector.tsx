import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { COLORS, SPACING, FONT_SIZES, BORDER_RADIUS, HAIR_LEVELS } from '../../../constants';
import Card from '../../common/Card';
import Slider from '@react-native-community/slider';

const HAIR_TONES = [
  { id: 'natural', name: 'Natural', color: '#8B6A47' },
  { id: 'ash', name: '<PERSON><PERSON><PERSON>', color: '#A8A8A8' },
  { id: 'golden', name: '<PERSON><PERSON>', color: '#FFD700' },
  { id: 'copper', name: '<PERSON><PERSON><PERSON>', color: '#B87333' },
  { id: 'mahogany', name: '<PERSON><PERSON>', color: '#C04000' },
  { id: 'violet', name: '<PERSON><PERSON>', color: '#8B7AB8' },
  { id: 'irise', name: '<PERSON><PERSON>', color: '#E6E6FA' },
  { id: 'beige', name: 'Beige', color: '#F5DEB3' },
];

interface ManualColorSelectorProps {
  mode: 'current' | 'desired';
  initialLevel?: number;
  initialTone?: string;
  onChange: (level: number, tone?: string) => void;
}

export default function ManualColorSelector({ 
  mode, 
  initialLevel = 6, 
  initialTone = 'natural',
  onChange 
}: ManualColorSelectorProps) {
  const [selectedLevel, setSelectedLevel] = useState(initialLevel);
  const [selectedTone, setSelectedTone] = useState(initialTone);

  const handleLevelChange = (level: number) => {
    setSelectedLevel(level);
    onChange(level, selectedTone);
  };

  const handleToneChange = (tone: string) => {
    setSelectedTone(tone);
    onChange(selectedLevel, tone);
  };

  return (
    <Card style={styles.container} elevation="medium">
      <Text style={styles.title}>
        {mode === 'current' ? 'Selección Manual del Color Actual' : 'Selección Manual del Color Deseado'}
      </Text>

      {/* Level Selection */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Nivel</Text>
        <View style={styles.levelPreview}>
          <View 
            style={[
              styles.colorSample, 
              { backgroundColor: HAIR_LEVELS[selectedLevel - 1]?.color || '#000' }
            ]} 
          />
          <View style={styles.levelInfo}>
            <Text style={styles.levelNumber}>Nivel {selectedLevel.toFixed(1)}</Text>
            <Text style={styles.levelName}>
              {HAIR_LEVELS[selectedLevel - 1]?.name || ''}
            </Text>
          </View>
        </View>
        <Slider
          style={styles.slider}
          minimumValue={1}
          maximumValue={10}
          step={0.1}
          value={selectedLevel}
          onValueChange={(value) => handleLevelChange(Math.round(value * 10) / 10)}
          minimumTrackTintColor={COLORS.primary}
          maximumTrackTintColor={COLORS.gray[300]}
          thumbTintColor={COLORS.primary}
        />
        <View style={styles.sliderLabels}>
          <Text style={styles.sliderLabel}>1 - Negro</Text>
          <Text style={styles.sliderLabel}>10 - Rubio platino</Text>
        </View>
      </View>

      {/* Tone Selection (only for desired mode) */}
      {mode === 'desired' && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Tono</Text>
          <ScrollView 
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.tonesContainer}
          >
            {HAIR_TONES.map((tone) => (
              <TouchableOpacity
                key={tone.id}
                style={[
                  styles.toneOption,
                  selectedTone === tone.id && styles.toneOptionSelected
                ]}
                onPress={() => handleToneChange(tone.id)}
              >
                <View 
                  style={[
                    styles.toneSample,
                    { backgroundColor: tone.color }
                  ]}
                />
                <Text style={[
                  styles.toneName,
                  selectedTone === tone.id && styles.toneNameSelected
                ]}>
                  {tone.name}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      )}

      {/* Visual Reference */}
      <View style={styles.referenceBox}>
        <Text style={styles.referenceIcon}>💡</Text>
        <Text style={styles.referenceText}>
          {mode === 'current' 
            ? 'Selecciona el nivel que mejor represente el color actual del cabello'
            : 'Selecciona el nivel y tono del color objetivo deseado'}
        </Text>
      </View>
    </Card>
  );
}

const styles = StyleSheet.create({
  container: {
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
  },
  title: {
    fontSize: FONT_SIZES.lg,
    fontWeight: '600',
    color: COLORS.gray[900],
    marginBottom: SPACING.lg,
    textAlign: 'center',
  },
  section: {
    marginBottom: SPACING.xl,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: '600',
    color: COLORS.gray[900],
    marginBottom: SPACING.md,
  },
  levelPreview: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  colorSample: {
    width: 80,
    height: 80,
    borderRadius: BORDER_RADIUS.lg,
    borderWidth: 2,
    borderColor: COLORS.gray[300],
  },
  levelInfo: {
    marginLeft: SPACING.md,
  },
  levelNumber: {
    fontSize: FONT_SIZES.xl,
    fontWeight: 'bold',
    color: COLORS.gray[900],
  },
  levelName: {
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[600],
    marginTop: SPACING.xs,
  },
  slider: {
    width: '100%',
    height: 40,
  },
  sliderLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.xs,
  },
  sliderLabel: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.gray[500],
  },
  tonesContainer: {
    paddingVertical: SPACING.sm,
    gap: SPACING.md,
  },
  toneOption: {
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  toneOptionSelected: {
    transform: [{ scale: 1.1 }],
  },
  toneSample: {
    width: 60,
    height: 60,
    borderRadius: BORDER_RADIUS.full,
    borderWidth: 3,
    borderColor: COLORS.gray[300],
    marginBottom: SPACING.sm,
  },
  toneName: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
  },
  toneNameSelected: {
    color: COLORS.primary,
    fontWeight: '600',
  },
  referenceBox: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: COLORS.info + '10',
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
  },
  referenceIcon: {
    fontSize: 20,
    marginRight: SPACING.sm,
  },
  referenceText: {
    flex: 1,
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[700],
    lineHeight: 20,
  },
});