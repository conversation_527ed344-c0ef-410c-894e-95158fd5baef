import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  FlatList,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { COLORS, SPACING, FONT_SIZES, BORDER_RADIUS } from '../../../constants';
import { useConsultation } from '../../../hooks/useConsultation';
import { dataService } from '../../../services/dataService';
import { Client } from '../../../types';
import Card from '../../common/Card';
import { CreateClientModal } from '../../clients/CreateClientModal';

export default function SelectClientStep() {
  const { setClient, state } = useConsultation();
  const [searchQuery, setSearchQuery] = useState('');
  const [clients, setClients] = useState<Client[]>([]);
  const [filteredClients, setFilteredClients] = useState<Client[]>([]);
  const [, setLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);

  useEffect(() => {
    loadClients();
  }, []);

  useEffect(() => {
    // Filtrar clientes según búsqueda
    if (searchQuery.trim()) {
      const filtered = clients.filter(client =>
        client.full_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        client.phone?.includes(searchQuery)
      );
      setFilteredClients(filtered);
    } else {
      setFilteredClients(clients);
    }
  }, [searchQuery, clients]);

  const loadClients = async () => {
    try {
      const clientsData = await dataService.clients.getAll('1');
      if (clientsData) {
        setClients(clientsData as any);
        setFilteredClients(clientsData as any);
      }
    } catch (error) {
      console.error('Error loading clients:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSelectClient = (client: Client) => {
    setClient(client);
  };

  const handleClientCreated = (newClient: Client) => {
    setClients([newClient, ...clients]);
    setFilteredClients([newClient, ...filteredClients]);
    setClient(newClient);
  };

  const renderClient = ({ item }: { item: Client }) => {
    const isSelected = state.client?.id === item.id;
    const hasAllergies = item.allergies && item.allergies.length > 0;
    const needsRetouch = item.last_visit && 
      new Date(item.last_visit) < new Date(Date.now() - 42 * 24 * 60 * 60 * 1000); // 6 semanas

    return (
      <TouchableOpacity onPress={() => handleSelectClient(item)}>
        <Card 
          style={[
            styles.clientCard,
            isSelected && styles.selectedCard
          ]}
          elevation={isSelected ? 'medium' : 'low'}
        >
          <View style={styles.clientHeader}>
            <Text style={styles.clientName}>{item.full_name}</Text>
            {isSelected && <Text style={styles.checkmark}>✓</Text>}
          </View>
          
          <Text style={styles.clientPhone}>{item.phone || 'Sin teléfono'}</Text>
          
          {item.last_visit && (
            <Text style={styles.lastVisit}>
              Última visita: {new Date(item.last_visit).toLocaleDateString('es-ES')}
            </Text>
          )}

          <View style={styles.alertsContainer}>
            {hasAllergies && (
              <View style={styles.alertBadge}>
                <Text style={styles.alertIcon}>⚠️</Text>
                <Text style={styles.alertText}>Alergias</Text>
              </View>
            )}
            {needsRetouch && (
              <View style={[styles.alertBadge, styles.retouchBadge]}>
                <Text style={styles.alertIcon}>🔔</Text>
                <Text style={styles.alertText}>Retoque</Text>
              </View>
            )}
          </View>
        </Card>
      </TouchableOpacity>
    );
  };

  return (
    <KeyboardAvoidingView 
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <View style={styles.header}>
        <Text style={styles.title}>Seleccionar Cliente</Text>
        <Text style={styles.subtitle}>
          Busca un cliente existente o crea uno nuevo
        </Text>
      </View>

      {/* Barra de búsqueda */}
      <View style={styles.searchContainer}>
        <Text style={styles.searchIcon}>🔍</Text>
        <TextInput
          style={styles.searchInput}
          placeholder="Buscar por nombre o teléfono..."
          placeholderTextColor={COLORS.gray[400]}
          value={searchQuery}
          onChangeText={setSearchQuery}
          autoCapitalize="none"
          autoCorrect={false}
        />
      </View>

      {/* Botón nuevo cliente */}
      <TouchableOpacity 
        style={styles.newClientButton}
        onPress={() => setShowCreateModal(true)}
      >
        <Text style={styles.newClientIcon}>➕</Text>
        <Text style={styles.newClientText}>Crear Nuevo Cliente</Text>
      </TouchableOpacity>

      {/* Lista de clientes */}
      <FlatList
        data={filteredClients}
        renderItem={renderClient}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>
              {searchQuery ? 'No se encontraron clientes' : 'No hay clientes registrados'}
            </Text>
          </View>
        }
      />

      {/* Modal de creación de cliente */}
      <CreateClientModal
        visible={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onClientCreated={handleClientCreated}
        userId="1"
      />
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    padding: SPACING.lg,
    paddingBottom: SPACING.md,
  },
  title: {
    fontSize: FONT_SIZES.xl,
    fontWeight: 'bold',
    color: COLORS.gray[900],
    marginBottom: SPACING.xs,
  },
  subtitle: {
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[600],
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.white,
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.md,
    paddingHorizontal: SPACING.md,
    borderRadius: BORDER_RADIUS.lg,
    borderWidth: 1,
    borderColor: COLORS.gray[200],
  },
  searchIcon: {
    fontSize: 20,
    marginRight: SPACING.sm,
  },
  searchInput: {
    flex: 1,
    paddingVertical: SPACING.md,
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[900],
  },
  newClientButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.accent,
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.lg,
  },
  newClientIcon: {
    fontSize: 18,
    marginRight: SPACING.sm,
  },
  newClientText: {
    fontSize: FONT_SIZES.md,
    fontWeight: '600',
    color: COLORS.gray[800],
  },
  listContent: {
    paddingHorizontal: SPACING.lg,
    paddingBottom: SPACING.xl,
  },
  clientCard: {
    marginBottom: SPACING.md,
  },
  selectedCard: {
    borderWidth: 2,
    borderColor: COLORS.primary,
  },
  clientHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.xs,
  },
  clientName: {
    fontSize: FONT_SIZES.lg,
    fontWeight: '600',
    color: COLORS.gray[900],
  },
  checkmark: {
    fontSize: 20,
    color: COLORS.primary,
    fontWeight: 'bold',
  },
  clientPhone: {
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[600],
    marginBottom: SPACING.xs,
  },
  lastVisit: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[500],
    marginBottom: SPACING.sm,
  },
  alertsContainer: {
    flexDirection: 'row',
    gap: SPACING.sm,
  },
  alertBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.warning + '20',
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.full,
  },
  retouchBadge: {
    backgroundColor: COLORS.info + '20',
  },
  alertIcon: {
    fontSize: 12,
    marginRight: SPACING.xs,
  },
  alertText: {
    fontSize: FONT_SIZES.xs,
    fontWeight: '500',
    color: COLORS.gray[700],
  },
  emptyContainer: {
    paddingVertical: SPACING.xxl,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[500],
  },
});