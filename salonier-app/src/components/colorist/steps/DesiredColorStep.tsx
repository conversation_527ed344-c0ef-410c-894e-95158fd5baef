import React, { useState, useEffect } from 'react';
import {
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  View,
  // Dimensions,
} from 'react-native';
import { COLORS, SPACING, FONT_SIZES, BORDER_RADIUS, HAIR_LEVELS } from '../../../constants';
import { TYPOGRAPHY, SHADOWS, COMPONENTS } from '../../../constants/design-system';
import { useConsultation } from '../../../hooks/useConsultation';
import Card from '../../common/Card';
import SimplePhotoAnalysis from '../SimplePhotoAnalysis';
import { PhotoAnalysisData } from '../../../hooks/usePhotoAnalysis';
import ViabilityAnalysis from '../ViabilityAnalysis';

// const { width: screenWidth } = Dimensions.get('window');

interface DesiredColor {
  level: number;
  tone: string;
  technique: 'global' | 'highlights' | 'balayage' | 'ombre' | 'babylights';
  description: string;
  referencePhoto?: string;
  zoneAnalysis?: {
    roots: { level: number; tone: string; porosity: string; condition: string };
    mids: { level: number; tone: string; porosity: string; condition: string };
    ends: { level: number; tone: string; porosity: string; condition: string };
  };
}

const TECHNIQUES = [
  { 
    id: 'global', 
    name: 'Color Global', 
    icon: '🎨',
    description: 'Aplicación uniforme en todo el cabello'
  },
  { 
    id: 'highlights', 
    name: 'Mechas', 
    icon: '✨',
    description: 'Mechas tradicionales con papel aluminio'
  },
  { 
    id: 'balayage', 
    name: 'Balayage', 
    icon: '🌊',
    description: 'Degradado natural pintado a mano'
  },
  { 
    id: 'ombre', 
    name: 'Ombré', 
    icon: '🌅',
    description: 'Transición de oscuro a claro'
  },
  { 
    id: 'babylights', 
    name: 'Babylights', 
    icon: '💫',
    description: 'Reflejos sutiles y naturales'
  },
];

export default function DesiredColorStep() {
  const { state, setDesiredColor } = useConsultation();
  const [selectedTechnique, setSelectedTechnique] = useState<'global' | 'highlights' | 'balayage' | 'ombre' | 'babylights'>('global');
  const [desiredLevel, setDesiredLevel] = useState<number>(7);
  const [showViability, setShowViability] = useState(false);
  
  // Verificar si es corrección
  const isCorrection = state.technique === 'correction';

  useEffect(() => {
    if (state.desiredColor) {
      setSelectedTechnique(state.desiredColor.technique);
    }
  }, [state.desiredColor]);

  const handlePhotoAnalysisComplete = (data: PhotoAnalysisData) => {
    // Determine average level and tone from zones
    let level = 7;
    let tone = 'natural';
    
    if (data.customizeByZones && data.zoneAnalysis) {
      // Calcular promedio con decimales
      level = Math.round(((data.zoneAnalysis.roots.level + data.zoneAnalysis.mids.level + data.zoneAnalysis.ends.level) / 3) * 10) / 10;
      tone = data.zoneAnalysis.roots.tone || 'natural';
    } else if (data.zoneAnalysis) {
      level = data.zoneAnalysis.roots.level;
      tone = data.zoneAnalysis.roots.tone || 'natural';
    }
    
    // Actualizar nivel deseado para mostrar viabilidad
    setDesiredLevel(level);
    setShowViability(true);

    const desiredColor: DesiredColor = {
      level,
      tone,
      technique: selectedTechnique,
      description: `${HAIR_LEVELS[level - 1]?.name || 'Nivel ' + level} - ${selectedTechnique}`,
      referencePhoto: data.photos[0],
      zoneAnalysis: data.customizeByZones ? {
        roots: {
          level: data.zoneAnalysis!.roots.level,
          tone: data.zoneAnalysis!.roots.tone || tone,
          porosity: data.zoneAnalysis!.roots.porosity,
          condition: data.zoneAnalysis!.roots.condition
        },
        mids: {
          level: data.zoneAnalysis!.mids.level,
          tone: data.zoneAnalysis!.mids.tone || tone,
          porosity: data.zoneAnalysis!.mids.porosity,
          condition: data.zoneAnalysis!.mids.condition
        },
        ends: {
          level: data.zoneAnalysis!.ends.level,
          tone: data.zoneAnalysis!.ends.tone || tone,
          porosity: data.zoneAnalysis!.ends.porosity,
          condition: data.zoneAnalysis!.ends.condition
        }
      } : undefined
    };

    setDesiredColor(desiredColor);
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <SimplePhotoAnalysis
        mode="desired"
        title={isCorrection ? "🎯 Color Objetivo de Corrección" : "Color Objetivo"}
        subtitle={isCorrection ? "¿Qué color quiere lograr el cliente?" : "Muestra o selecciona el color deseado"}
        onComplete={handlePhotoAnalysisComplete}
        initialData={{
          mode: state.desiredColor?.referencePhoto ? 'photo' : 'manual',
          customizeByZones: !!state.desiredColor?.zoneAnalysis,
          photos: state.desiredColor?.referencePhoto ? [state.desiredColor.referencePhoto] : [],
          zoneAnalysis: state.desiredColor?.zoneAnalysis ? {
            roots: {
              level: state.desiredColor.zoneAnalysis.roots.level,
              porosity: state.desiredColor.zoneAnalysis.roots.porosity as 'low' | 'medium' | 'high',
              condition: state.desiredColor.zoneAnalysis.roots.condition as 'damaged' | 'healthy' | 'very_healthy',
              tone: state.desiredColor.zoneAnalysis.roots.tone
            },
            mids: {
              level: state.desiredColor.zoneAnalysis.mids.level,
              porosity: state.desiredColor.zoneAnalysis.mids.porosity as 'low' | 'medium' | 'high',
              condition: state.desiredColor.zoneAnalysis.mids.condition as 'damaged' | 'healthy' | 'very_healthy',
              tone: state.desiredColor.zoneAnalysis.mids.tone
            },
            ends: {
              level: state.desiredColor.zoneAnalysis.ends.level,
              porosity: state.desiredColor.zoneAnalysis.ends.porosity as 'low' | 'medium' | 'high',
              condition: state.desiredColor.zoneAnalysis.ends.condition as 'damaged' | 'healthy' | 'very_healthy',
              tone: state.desiredColor.zoneAnalysis.ends.tone
            }
          } : {
            roots: { level: 7, porosity: 'medium', condition: 'healthy', tone: 'natural' },
            mids: { level: 7, porosity: 'medium', condition: 'healthy', tone: 'natural' },
            ends: { level: 8, porosity: 'medium', condition: 'healthy', tone: 'natural' }
          }
        }}
        showComparison={true}
        comparisonData={state.hairAnalysis}
      />

      {/* Technique Selection - Solo para nueva coloración */}
      {!isCorrection && (
        <Card style={styles.techniqueCard} elevation="medium">
          <Text style={styles.sectionTitle}>Técnica de Aplicación</Text>
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.techniquesContainer}
          >
            {TECHNIQUES.map((technique) => (
              <TouchableOpacity
                key={technique.id}
                style={[
                  styles.techniqueOption,
                  selectedTechnique === technique.id && styles.techniqueSelected
                ]}
                onPress={() => setSelectedTechnique(technique.id as any)}
              >
                <Text style={styles.techniqueIcon}>{technique.icon}</Text>
                <Text style={[
                  styles.techniqueName,
                  selectedTechnique === technique.id && styles.techniqueNameSelected
                ]}>
                  {technique.name}
                </Text>
                <Text style={styles.techniqueDescription}>
                  {technique.description}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </Card>
      )}

      {/* Análisis de Viabilidad en Tiempo Real */}
      {showViability && state.hairAnalysis && (
        <ViabilityAnalysis
          currentLevel={state.hairAnalysis.natural_level}
          desiredLevel={desiredLevel}
          currentCondition={state.hairAnalysis.condition}
          isCorrection={isCorrection}
        />
      )}
      
      {/* Recommendations */}
      <Card style={styles.recommendationCard} elevation="low">
        <Text style={styles.recommendationTitle}>💡 {isCorrection ? 'Consideraciones para Corrección' : 'Recomendaciones'}</Text>
        <Text style={styles.recommendationText}>
          {isCorrection ? 
            `• Define un objetivo realista considerando el estado actual
• Es importante mostrar referencias visuales al cliente
• Considera que pueden ser necesarias múltiples sesiones
• El análisis por zonas es crucial en correcciones complejas` :
            `• Para resultados óptimos, considera el estado actual del cabello
• Las técnicas degradadas requieren más tiempo de aplicación
• El análisis por zonas permite mayor precisión en el resultado`
          }
        </Text>
      </Card>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  techniqueCard: {
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
    ...COMPONENTS.card.elevated,
  },
  sectionTitle: {
    fontSize: TYPOGRAPHY.size.lg,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    color: COLORS.text,
    marginBottom: SPACING.md,
  },
  techniquesContainer: {
    paddingVertical: SPACING.sm,
    gap: SPACING.md,
  },
  techniqueOption: {
    width: 140,
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.xl,
    backgroundColor: COLORS.surface,
    marginRight: SPACING.md,
    ...SHADOWS.sm,
  },
  techniqueSelected: {
    backgroundColor: COLORS.secondary,
    ...SHADOWS.md,
  },
  techniqueIcon: {
    fontSize: 32,
    textAlign: 'center',
    marginBottom: SPACING.sm,
  },
  techniqueName: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    color: COLORS.text,
    textAlign: 'center',
    marginBottom: SPACING.xs,
  },
  techniqueNameSelected: {
    color: COLORS.white,
  },
  techniqueDescription: {
    fontSize: TYPOGRAPHY.size.xs,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.gray[600],
    textAlign: 'center',
  },
  recommendationCard: {
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.xl,
    backgroundColor: COLORS.surface,
    borderLeftWidth: 4,
    borderLeftColor: COLORS.info,
    ...COMPONENTS.card.elevated,
  },
  recommendationTitle: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    color: COLORS.text,
    marginBottom: SPACING.sm,
  },
  recommendationText: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.gray[700],
    lineHeight: 20,
  },
});