import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Switch,
} from 'react-native';
import { COLORS, SPACING, BORDER_RADIUS } from '../../../constants';
import { TYPOGRAPHY, SHADOWS, COMPONENTS } from '../../../constants/design-system';
import { useSimpleConsultation } from '../../../hooks/useSimpleConsultation';
import { Client } from '../../../types';
import { dataService } from '../../../services/dataService';
import Card from '../../common/Card';
import { Icon } from '../../common/Icon';
import Toast from 'react-native-toast-message';

export default function ClientQuickSelect() {
  const { state, setClient, setSafetyChecks } = useSimpleConsultation();
  const [searchQuery, setSearchQuery] = useState('');
  const [clients, setClients] = useState<Client[]>([]);
  const [filteredClients, setFilteredClients] = useState<Client[]>([]);
  const [loading, setLoading] = useState(true);
  const [showSafetyChecks, setShowSafetyChecks] = useState(false);

  useEffect(() => {
    loadClients();
  }, []);

  useEffect(() => {
    filterClients();
  }, [searchQuery, clients]);

  useEffect(() => {
    if (state.client) {
      setShowSafetyChecks(true);
    }
  }, [state.client]);

  const loadClients = async () => {
    try {
      const data = await dataService.clients.getAll();
      setClients(data);
      setFilteredClients(data);
    } catch (error) {
      console.error('Error loading clients:', error);
      Toast.show({
        type: 'error',
        text1: 'Error al cargar clientes',
        position: 'top',
      });
    } finally {
      setLoading(false);
    }
  };

  const filterClients = () => {
    if (!searchQuery.trim()) {
      setFilteredClients(clients);
      return;
    }

    const query = searchQuery.toLowerCase();
    const filtered = clients.filter(client => 
      client.name.toLowerCase().includes(query) ||
      client.phone?.toLowerCase().includes(query) ||
      client.email?.toLowerCase().includes(query)
    );
    setFilteredClients(filtered);
  };

  const handleClientSelect = (client: Client) => {
    setClient(client);
    setShowSafetyChecks(true);
  };

  const renderClient = (client: Client) => {
    const isSelected = state.client?.id === client.id;
    const lastVisit = client.lastVisit ? new Date(client.lastVisit).toLocaleDateString() : 'Primera vez';
    
    return (
      <TouchableOpacity
        key={client.id}
        style={[styles.clientCard, isSelected && styles.clientCardSelected]}
        onPress={() => handleClientSelect(client)}
        activeOpacity={0.7}
      >
        <View style={styles.clientInfo}>
          <Text style={[styles.clientName, isSelected && styles.clientNameSelected]}>
            {client.name}
          </Text>
          <Text style={styles.clientDetails}>
            {client.phone} • Última visita: {lastVisit}
          </Text>
        </View>
        {isSelected && (
          <Icon name="checkmark-circle" size={24} color={COLORS.primary} />
        )}
      </TouchableOpacity>
    );
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Icon name="search" size={20} color={COLORS.gray[400]} />
        <TextInput
          style={styles.searchInput}
          placeholder="Buscar por nombre o teléfono..."
          placeholderTextColor={COLORS.gray[400]}
          value={searchQuery}
          onChangeText={setSearchQuery}
          autoCapitalize="none"
          autoCorrect={false}
        />
      </View>

      {/* Quick Add Button */}
      <TouchableOpacity style={styles.addNewButton}>
        <Icon name="add-circle" size={20} color={COLORS.primary} />
        <Text style={styles.addNewText}>Agregar nuevo cliente</Text>
      </TouchableOpacity>

      {/* Clients List */}
      {!loading && filteredClients.length > 0 && (
        <View style={styles.clientsList}>
          {filteredClients.slice(0, 5).map(renderClient)}
        </View>
      )}

      {/* Safety Checks */}
      {showSafetyChecks && state.client && (
        <Card style={styles.safetyCard} elevation="md">
          <Text style={styles.safetyTitle}>✅ Verificación de Seguridad</Text>
          <Text style={styles.safetySubtitle}>Confirma antes de continuar</Text>

          <View style={styles.checkItem}>
            <View style={styles.checkTextContainer}>
              <Text style={styles.checkLabel}>Test de alergia realizado</Text>
              <Text style={styles.checkDescription}>48 horas antes del servicio</Text>
            </View>
            <Switch
              value={state.safetyChecks.allergyTest}
              onValueChange={(value) => 
                setSafetyChecks({ ...state.safetyChecks, allergyTest: value })
              }
              trackColor={{ false: COLORS.gray[300], true: COLORS.primary }}
              thumbColor={COLORS.white}
            />
          </View>

          <View style={styles.checkItem}>
            <View style={styles.checkTextContainer}>
              <Text style={styles.checkLabel}>Consentimiento firmado</Text>
              <Text style={styles.checkDescription}>Acepta los términos del servicio</Text>
            </View>
            <Switch
              value={state.safetyChecks.consentSigned}
              onValueChange={(value) => 
                setSafetyChecks({ ...state.safetyChecks, consentSigned: value })
              }
              trackColor={{ false: COLORS.gray[300], true: COLORS.primary }}
              thumbColor={COLORS.white}
            />
          </View>

          {state.client.notes && (
            <View style={styles.notesBox}>
              <Icon name="information-circle" size={20} color={COLORS.info} />
              <Text style={styles.notesText}>{state.client.notes}</Text>
            </View>
          )}
        </Card>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.surface,
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.md,
    paddingHorizontal: SPACING.md,
    borderRadius: BORDER_RADIUS.full,
    height: 48,
    ...SHADOWS.sm,
  },
  searchInput: {
    flex: 1,
    marginLeft: SPACING.sm,
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.text,
  },
  addNewButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: SPACING.sm,
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
    paddingVertical: SPACING.sm,
  },
  addNewText: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.medium,
    color: COLORS.primary,
  },
  clientsList: {
    marginHorizontal: SPACING.lg,
    gap: SPACING.sm,
    marginBottom: SPACING.lg,
  },
  clientCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: COLORS.white,
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.lg,
    borderWidth: 2,
    borderColor: COLORS.gray[200],
    ...SHADOWS.sm,
  },
  clientCardSelected: {
    borderColor: COLORS.primary,
    backgroundColor: COLORS.primary + '08',
    ...SHADOWS.md,
  },
  clientInfo: {
    flex: 1,
  },
  clientName: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: SPACING.xs,
  },
  clientNameSelected: {
    color: COLORS.primary,
  },
  clientDetails: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
  },
  safetyCard: {
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
  },
  safetyTitle: {
    fontSize: TYPOGRAPHY.size.lg,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: SPACING.xs,
  },
  safetySubtitle: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
    marginBottom: SPACING.lg,
  },
  checkItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray[200],
  },
  checkTextContainer: {
    flex: 1,
    marginRight: SPACING.md,
  },
  checkLabel: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.medium,
    color: COLORS.text,
    marginBottom: SPACING.xs,
  },
  checkDescription: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
  },
  notesBox: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: SPACING.sm,
    marginTop: SPACING.md,
    padding: SPACING.md,
    backgroundColor: COLORS.info + '10',
    borderRadius: BORDER_RADIUS.md,
  },
  notesText: {
    flex: 1,
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.text,
    lineHeight: 20,
  },
});