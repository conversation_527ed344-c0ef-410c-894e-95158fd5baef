import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import Toast from 'react-native-toast-message';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { COLORS, SPACING, FONT_SIZES, BORDER_RADIUS, HAIR_LEVELS } from '../../../constants';
import { TYPOGRAPHY, SHADOWS, COMPONENTS } from '../../../constants/design-system';
import { useConsultation } from '../../../hooks/useConsultation';
import { ColorFormulation, User, Consultation } from '../../../types';
import {
  generateMockFormulation,
  getMockHairAnalysis,
  getMockDesiredColor,
  getFormulationDetails,
} from '../../../services/mockFormulation';
import { dataService } from '../../../services/dataService';
import Card from '../../common/Card';
import { CorrectionAIService } from '../../../services/correctionAIService';
import { CorrectionFormulation, CorrectionData } from '../../../types/correction-types';
import { InventoryConsumptionService } from '../../../services/inventoryConsumptionService';
import Icon from 'react-native-vector-icons/Ionicons';
import FormulationDisplay from '../FormulationDisplay';

export default function FormulationStep() {
  const { state, setFormulation, setHairAnalysis } = useConsultation();
  const [loading, setLoading] = useState(true);
  const [formulation, setLocalFormulation] = useState<ColorFormulation | null>(null);
  const [formulationDetails, setFormulationDetails] = useState<any>(null);
  const [user, setUser] = useState<User | null>(null);
  const [serviceCost, setServiceCost] = useState<number | null>(null);
  const [correctionFormula, setCorrectionFormula] = useState<CorrectionFormulation | null>(null);
  const [previousConsultation, setPreviousConsultation] = useState<Consultation | null>(null);

  const isCorrection = state.technique === 'correction';
  const correctionData = (state.hairAnalysis as any)?.correctionData as CorrectionData | undefined;

  useEffect(() => {
    generateFormulation();
  }, []);

  const generateFormulation = async () => {
    setLoading(true);

    try {
      // Load user data
      const userData = await dataService.auth.getCurrentUser();
      setUser(userData as any);

      // Load client history if available
      if (state.client?.id) {
        const lastConsult = await dataService.consultations.getLastByClient(state.client.id);
        setPreviousConsultation(lastConsult);
      }

      // Simular delay de procesamiento IA
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Use actual analysis from context or generate mock if not available
      const mockAnalysis = state.hairAnalysis || getMockHairAnalysis();
      const mockDesired = state.desiredColor || getMockDesiredColor();

      // Only save mock analysis if we don't have a real one
      if (!state.hairAnalysis) {
        setHairAnalysis(mockAnalysis);
      }

      // Verificar si es corrección de color
      if (isCorrection && correctionData) {
        // Generar formulación de corrección con IA considerando historial
        const correctionRequest = {
          problemType: correctionData.problem,
          currentAnalysis: mockAnalysis,
          correctionData: correctionData,
          userBrands: userData?.favorite_brands || ["L'Oréal", 'Wella'],
          targetResult: mockDesired.description,
          previousFormulation: previousConsultation?.formulation,
          clientHistory: {
            hairCharacteristics: state.client?.hair_characteristics,
            lastResult: previousConsultation?.actual_result,
          },
        };

        const correctionResult =
          await CorrectionAIService.generateCorrectionFormula(correctionRequest);
        setCorrectionFormula(correctionResult);

        // Convertir a ColorFormulation para compatibilidad
        const adaptedFormulation: ColorFormulation = {
          id: Date.now().toString(),
          consultation_id: state.consultationId || '',
          brand_id: '4', // L'Oréal por defecto
          product_line_id: 'pl1', // Majirel por defecto
          formula: {
            products: correctionResult.mainProducts.map(p => ({
              product_id: p.product_id,
              name: p.name,
              amount: p.amount,
              unit: p.unit,
            })),
            developer_volume: 10,
            processing_time: correctionResult.processingTime,
            technique: correctionResult.technique,
          },
          developer_volume: 10, // Correcciones usan oxidante bajo
          processing_time: correctionResult.processingTime,
          technique: correctionResult.technique,
          notes: correctionResult.precautions.join('. '),
          total_cost: 15, // Costo estimado
          suggested_price: 60, // Precio sugerido para correcciones
          created_at: new Date().toISOString(),
        };

        // Obtener detalles de la formulación
        const details = getFormulationDetails(adaptedFormulation);

        setLocalFormulation(adaptedFormulation);
        setFormulationDetails(details);
        setFormulation(adaptedFormulation);

        // Calculate cost if applicable
        if (
          userData?.inventory_level === 'smart_cost' ||
          userData?.inventory_level === 'full_control'
        ) {
          await calculateServiceCost(adaptedFormulation, userData as any);
        }
      } else {
        // Flujo normal (no corrección) - considerar historial
        const newFormulation = generateMockFormulation(mockAnalysis, mockDesired);

        // Ajustar formulación según historial si existe
        if (previousConsultation && state.client?.hair_characteristics) {
          // Ajustar tiempo de procesamiento según historial
          if (state.client.hair_characteristics.resistant_to_color) {
            newFormulation.processing_time = (newFormulation.processing_time || 30) + 10;
          }

          // Ajustar volumen de oxidante según historial
          if (state.client.hair_characteristics.lifts_easily) {
            newFormulation.developer_volume = Math.max(
              10,
              (newFormulation.developer_volume || 20) - 10
            );
          }

          // Añadir nota sobre sensibilidades
          if (state.client.hair_characteristics.ammonia_sensitive) {
            newFormulation.notes =
              (newFormulation.notes || '') +
              ' IMPORTANTE: Cliente sensible a amoníaco. Usar productos sin amoníaco.';
          }
        }

        const details = getFormulationDetails(newFormulation);

        // Calculate cost if smart_cost or full_control
        if (
          userData?.inventory_level === 'smart_cost' ||
          userData?.inventory_level === 'full_control'
        ) {
          await calculateServiceCost(newFormulation, userData as any);
        }

        setLocalFormulation(newFormulation);
        setFormulationDetails(details);
        setFormulation(newFormulation);
      }
    } catch (error) {
      console.error('Error generating formulation:', error);
    } finally {
      setLoading(false);
    }
  };

  const calculateServiceCost = async (formulation: ColorFormulation, userData: User) => {
    // If user has inventory, calculate real cost from inventory
    if (userData.inventory_level === 'smart_cost' || userData.inventory_level === 'full_control') {
      try {
        const realCost = await InventoryConsumptionService.calculateFormulationCost(
          formulation,
          userData.id
        );
        setServiceCost(realCost);

        // Update formulation with real cost
        formulation.total_cost = realCost;

        // Calculate suggested price based on user's margin
        if ((userData as any).default_margin_percentage) {
          const margin = (userData as any).default_margin_percentage / 100;
          formulation.suggested_price = realCost * (1 + margin);
        }
      } catch (error) {
        console.error('Error calculating real cost:', error);
        // Fallback to estimated cost
        setServiceCost(formulation.total_cost);
      }
    } else {
      // Use estimated cost for no inventory users
      setServiceCost(formulation.total_cost);
    }
  };

  const formatCurrency = (amount: number) => {
    return `€${amount.toFixed(2)}`;
  };

  const getMarginPercentage = (customCost?: number | null) => {
    if (!formulation) return 0;
    const cost =
      customCost !== null && customCost !== undefined ? customCost : formulation.total_cost;
    const margin = ((formulation.suggested_price - cost) / formulation.suggested_price) * 100;
    return margin.toFixed(0);
  };

  const getMarginColor = (customCost?: number | null) => {
    const margin = parseInt(getMarginPercentage(customCost).toString());
    if (margin >= 70) return COLORS.success;
    if (margin >= 50) return COLORS.warning;
    return COLORS.error;
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={COLORS.primary} />
        <Text style={styles.loadingText}>
          {isCorrection ? 'Analizando corrección con IA...' : 'Generando formulación perfecta...'}
        </Text>
      </View>
    );
  }

  if (!formulation || (!formulationDetails && !isCorrection)) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Error al generar la formulación</Text>
      </View>
    );
  }

  // Use actual data from context
  const currentLevel = state.hairAnalysis?.natural_level || 6;
  const desiredLevel = state.desiredColor?.level || 7;
  // const desiredTone = state.desiredColor?.tone || 'ash';

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.header}>
        <Text style={styles.title}>
          {isCorrection ? 'Formulación Correctiva' : 'Formulación Profesional'}
        </Text>
        <Text style={styles.subtitle}>
          {isCorrection
            ? 'Neutralización y corrección basada en IA'
            : 'Basada en el análisis del cabello y color deseado'}
        </Text>
      </View>

      {/* Historial del Cliente */}
      {previousConsultation && (
        <Card style={[styles.analysisCard, styles.historyCard]} elevation="medium">
          <View style={styles.historyHeader}>
            <Icon name="time-outline" size={20} color={COLORS.warning} />
            <Text style={styles.historyTitle}>Historial Aplicado</Text>
          </View>

          <View style={styles.historyContent}>
            {state.client?.hair_characteristics?.resistant_to_color && (
              <View style={styles.adjustmentRow}>
                <Icon name="add-circle" size={16} color={COLORS.info} />
                <Text style={styles.adjustmentText}>
                  +10 min procesamiento (cabello resistente)
                </Text>
              </View>
            )}

            {state.client?.hair_characteristics?.lifts_easily && (
              <View style={styles.adjustmentRow}>
                <Icon name="remove-circle" size={16} color={COLORS.success} />
                <Text style={styles.adjustmentText}>Oxidante reducido (aclara fácilmente)</Text>
              </View>
            )}

            {previousConsultation.actual_result?.variation_notes && (
              <View style={styles.historyNote}>
                <Text style={styles.historyNoteLabel}>Nota de última sesión:</Text>
                <Text style={styles.historyNoteText}>
                  {previousConsultation.actual_result.variation_notes}
                </Text>
              </View>
            )}
          </View>
        </Card>
      )}

      {/* Información de corrección si aplica */}
      {isCorrection && correctionFormula && (
        <Card
          style={[styles.correctionCard, { backgroundColor: COLORS.warning + '10' }]}
          elevation="high"
        >
          <View style={styles.correctionHeader}>
            <Text style={styles.correctionIcon}>🔧</Text>
            <Text style={styles.correctionTitle}>Plan de Corrección</Text>
          </View>

          <View style={styles.neutralizingInfo}>
            <Text style={styles.neutralizingLabel}>Tono Neutralizante:</Text>
            <View style={styles.neutralizingRow}>
              <View
                style={[
                  styles.neutralizingColor,
                  { backgroundColor: correctionFormula.neutralizingTone.color },
                ]}
              />
              <Text style={styles.neutralizingTheory}>
                {correctionFormula.neutralizingTone.theory}
              </Text>
            </View>
          </View>

          <View style={styles.correctionDetails}>
            <Text style={styles.detailLabel}>Resultado Esperado:</Text>
            <Text style={styles.detailValue}>{correctionFormula.expectedResult}</Text>

            <Text style={styles.detailLabel}>Sesiones Necesarias:</Text>
            <Text style={styles.detailValue}>{correctionFormula.sessionsNeeded}</Text>

            <Text style={styles.detailLabel}>Técnica:</Text>
            <Text style={styles.detailValue}>
              {correctionFormula.technique === 'global'
                ? 'Aplicación global'
                : correctionFormula.technique === 'zones'
                  ? 'Por zonas'
                  : 'Selectiva'}
            </Text>
          </View>

          {correctionFormula.precautions.length > 0 && (
            <View style={styles.precautionsContainer}>
              <Text style={styles.precautionsTitle}>⚠️ Precauciones:</Text>
              {correctionFormula.precautions.map((precaution, index) => (
                <Text key={index} style={styles.precautionItem}>
                  • {precaution}
                </Text>
              ))}
            </View>
          )}
        </Card>
      )}

      {/* Resumen del análisis */}
      <Card style={styles.analysisCard} elevation="low">
        <Text style={styles.sectionTitle}>Resumen del Análisis</Text>
        <View style={styles.colorComparison}>
          <View style={styles.colorBox}>
            <View
              style={[styles.colorSample, { backgroundColor: HAIR_LEVELS[currentLevel - 1].color }]}
            />
            <Text style={styles.colorLabel}>Actual</Text>
            <Text style={styles.colorLevel}>Nivel {currentLevel}</Text>
          </View>
          <Text style={styles.arrow}>→</Text>
          <View style={styles.colorBox}>
            <View
              style={[styles.colorSample, { backgroundColor: HAIR_LEVELS[desiredLevel - 1].color }]}
            />
            <Text style={styles.colorLabel}>Deseado</Text>
            <Text style={styles.colorLevel}>Nivel {desiredLevel}</Text>
            {state.desiredColor?.tone && (
              <Text style={styles.colorTone}>
                {state.desiredColor.tone === 'ash'
                  ? 'Ceniza'
                  : state.desiredColor.tone === 'golden'
                    ? 'Dorado'
                    : state.desiredColor.tone === 'natural'
                      ? 'Natural'
                      : state.desiredColor.tone === 'copper'
                        ? 'Cobrizo'
                        : state.desiredColor.tone === 'mahogany'
                          ? 'Caoba'
                          : state.desiredColor.tone === 'violet'
                            ? 'Violeta'
                            : state.desiredColor.tone === 'irise'
                              ? 'Irisado'
                              : state.desiredColor.tone === 'beige'
                                ? 'Beige'
                                : state.desiredColor.tone}
              </Text>
            )}
          </View>
        </View>
        <View style={styles.analysisDetails}>
          <Text style={styles.detailText}>
            • Canas: {state.hairAnalysis?.gray_percentage || 15}%
          </Text>
          <Text style={styles.detailText}>
            • Porosidad:{' '}
            {state.hairAnalysis?.porosity === 'low'
              ? 'Baja'
              : state.hairAnalysis?.porosity === 'high'
                ? 'Alta'
                : 'Media'}
          </Text>
          <Text style={styles.detailText}>
            • Estado:{' '}
            {state.hairAnalysis?.condition === 'damaged'
              ? 'Dañado'
              : state.hairAnalysis?.condition === 'very_healthy'
                ? 'Muy saludable'
                : 'Saludable'}
          </Text>
        </View>
      </Card>

      {/* Fórmula detallada usando el nuevo componente */}
      <FormulationDisplay
        formulation={formulation}
        brandName={formulationDetails?.brand?.name}
        isCorrection={isCorrection}
        serviceCost={serviceCost}
      />

      {/* Información adicional temporal - borrar después */}
      <Card style={styles.formulaCard} elevation="medium">
        <View style={styles.formulaHeader}>
          <Text style={styles.sectionTitle}>Fórmula Recomendada (OLD - REMOVE)</Text>
          {formulationDetails && (
            <View style={styles.brandBadge}>
              <Text style={styles.brandText}>{formulationDetails.brand?.name || 'Marca'}</Text>
            </View>
          )}
        </View>

        <View style={styles.productsList}>
          {formulation.formula.products.map((product, index) => (
            <View key={index} style={styles.productItem}>
              <Text style={styles.productIcon}>
                {product.name.includes('Oxidante') ? '🧴' : '🎨'}
              </Text>
              <View style={styles.productDetails}>
                <Text style={styles.productName}>{product.name}</Text>
                <Text style={styles.productAmount}>
                  {product.amount} {product.unit}
                </Text>
              </View>
            </View>
          ))}
        </View>

        <View style={styles.processingInfo}>
          <View style={styles.infoItem}>
            <Text style={styles.infoIcon}>⏱</Text>
            <Text style={styles.infoLabel}>Tiempo de procesamiento</Text>
            <Text style={styles.infoValue}>{formulation.formula.processing_time} min</Text>
          </View>
          <View style={styles.infoItem}>
            <Text style={styles.infoIcon}>🎯</Text>
            <Text style={styles.infoLabel}>Técnica</Text>
            <Text style={styles.infoValue}>
              {state.desiredColor?.technique === 'highlights'
                ? 'Mechas'
                : state.desiredColor?.technique === 'balayage'
                  ? 'Balayage'
                  : state.desiredColor?.technique === 'ombre'
                    ? 'Ombré'
                    : state.desiredColor?.technique === 'babylights'
                      ? 'Babylights'
                      : 'Aplicación global'}
            </Text>
          </View>
        </View>
      </Card>

      {/* Costos y rentabilidad - Only show for smart_cost or full_control */}
      {(user?.inventory_level === 'smart_cost' || user?.inventory_level === 'full_control') && (
        <Card style={styles.costCard} elevation="medium">
          <View style={styles.costHeader}>
            <Text style={styles.sectionTitle}>Análisis de Costos</Text>
            {serviceCost === null && user?.product_pricing?.length === 0 && (
              <TouchableOpacity
                style={styles.setupButton}
                onPress={() =>
                  Toast.show({
                    type: 'info',
                    text1: 'Configurar Precios',
                    text2:
                      'Para ver el costo real, configura los precios de tus productos en Ajustes > Control de Inventario',
                  })
                }
              >
                <MaterialCommunityIcons name="cog" size={16} color={COLORS.primary} />
                <Text style={styles.setupText}>Configurar</Text>
              </TouchableOpacity>
            )}
          </View>

          <View style={styles.costBreakdown}>
            <View style={styles.costRow}>
              <Text style={styles.costLabel}>Costo de productos:</Text>
              <Text style={styles.costValue}>
                {serviceCost !== null
                  ? formatCurrency(serviceCost)
                  : formatCurrency(formulation.total_cost)}
              </Text>
              {serviceCost !== null && (
                <View style={styles.realCostBadge}>
                  <Text style={styles.realCostText}>Real</Text>
                </View>
              )}
            </View>
            <View style={styles.costRow}>
              <Text style={styles.costLabel}>Precio sugerido:</Text>
              <Text style={[styles.costValue, styles.priceValue]}>
                {formatCurrency(formulation.suggested_price)}
              </Text>
            </View>
            <View style={styles.divider} />
            <View style={styles.costRow}>
              <Text style={styles.costLabel}>Ganancia estimada:</Text>
              <Text style={[styles.costValue, { color: getMarginColor(serviceCost) }]}>
                {formatCurrency(
                  formulation.suggested_price - (serviceCost || formulation.total_cost)
                )}
              </Text>
            </View>
          </View>

          <View style={styles.marginIndicator}>
            <Text style={styles.marginLabel}>Margen de ganancia</Text>
            <View style={styles.marginBar}>
              <View
                style={[
                  styles.marginFill,
                  {
                    width: `${getMarginPercentage(serviceCost)}%` as any,
                    backgroundColor: getMarginColor(serviceCost),
                  },
                ]}
              />
            </View>
            <Text style={[styles.marginPercentage, { color: getMarginColor(serviceCost) }]}>
              {getMarginPercentage(serviceCost)}%
            </Text>
          </View>

          {serviceCost === null && user?.product_pricing && user.product_pricing.length > 0 && (
            <View style={styles.infoBox}>
              <MaterialCommunityIcons name="information" size={16} color="#666" />
              <Text style={styles.infoText}>
                Algunos productos no tienen precio configurado. El costo mostrado es estimado.
              </Text>
            </View>
          )}
        </Card>
      )}

      {/* Botón de conversión */}
      <TouchableOpacity style={styles.conversionButton}>
        <Text style={styles.conversionIcon}>🔄</Text>
        <View style={styles.conversionTextContainer}>
          <Text style={styles.conversionTitle}>¿No tienes esta marca?</Text>
          <Text style={styles.conversionSubtitle}>
            Convierte la fórmula a cualquiera de las 100+ marcas disponibles
          </Text>
        </View>
        <Text style={styles.conversionArrow}>→</Text>
      </TouchableOpacity>

      {/* Notas adicionales */}
      <Card style={styles.notesCard} elevation="low">
        <Text style={styles.notesTitle}>💡 Recomendaciones</Text>
        <Text style={styles.notesText}>
          • Realizar prueba de mechón antes de la aplicación completa{'\n'}• Aplicar desde raíces a
          puntas para resultado uniforme{'\n'}• Considerar un tratamiento post-color para mantener
          el brillo
        </Text>
      </Card>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.surface,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.xl,
  },
  loadingText: {
    marginTop: SPACING.md,
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.xl,
  },
  errorText: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.error,
  },
  header: {
    padding: SPACING.lg,
    paddingBottom: SPACING.md,
  },
  title: {
    fontSize: TYPOGRAPHY.size['2xl'],
    fontFamily: TYPOGRAPHY.fontFamily.bold,
    fontWeight: '700',
    color: COLORS.text,
    marginBottom: SPACING.xs,
    letterSpacing: TYPOGRAPHY.letterSpacing.tight,
  },
  subtitle: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
    lineHeight: TYPOGRAPHY.size.base * TYPOGRAPHY.lineHeight.normal,
  },
  sectionTitle: {
    fontSize: TYPOGRAPHY.size.lg,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: SPACING.md,
    letterSpacing: TYPOGRAPHY.letterSpacing.tight,
  },
  // Correction styles
  correctionCard: {
    ...COMPONENTS.card.elevated,
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
    borderWidth: 2,
    borderColor: COLORS.warning,
  },
  correctionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  correctionIcon: {
    fontSize: 24,
    marginRight: SPACING.sm,
  },
  correctionTitle: {
    fontSize: TYPOGRAPHY.size.lg,
    fontFamily: TYPOGRAPHY.fontFamily.bold,
    fontWeight: '700',
    color: COLORS.text,
  },
  neutralizingInfo: {
    marginBottom: SPACING.lg,
  },
  neutralizingLabel: {
    fontSize: FONT_SIZES.md,
    fontWeight: '600',
    color: COLORS.gray[700],
    marginBottom: SPACING.sm,
  },
  neutralizingRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  neutralizingColor: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: SPACING.md,
    borderWidth: 2,
    borderColor: COLORS.gray[300],
  },
  neutralizingTheory: {
    flex: 1,
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[700],
    lineHeight: 20,
  },
  correctionDetails: {
    marginBottom: SPACING.lg,
  },
  detailLabel: {
    fontSize: FONT_SIZES.sm,
    fontWeight: '600',
    color: COLORS.gray[600],
    marginTop: SPACING.sm,
  },
  detailValue: {
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[900],
    marginBottom: SPACING.sm,
  },
  precautionsContainer: {
    backgroundColor: COLORS.error + '10',
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
  },
  precautionsTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: 'bold',
    color: COLORS.error,
    marginBottom: SPACING.sm,
  },
  precautionItem: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[700],
    lineHeight: 20,
    marginBottom: SPACING.xs,
  },
  // Analysis card
  analysisCard: {
    ...COMPONENTS.card.elevated,
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
  },
  colorComparison: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: SPACING.lg,
  },
  colorBox: {
    alignItems: 'center',
  },
  colorSample: {
    width: 60,
    height: 60,
    borderRadius: BORDER_RADIUS.full,
    marginBottom: SPACING.sm,
    borderWidth: 2,
    borderColor: COLORS.gray[200],
    ...SHADOWS.sm,
  },
  colorLabel: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
  },
  colorLevel: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.text,
  },
  colorTone: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.gray[600],
    fontStyle: 'italic',
  },
  arrow: {
    fontSize: 24,
    marginHorizontal: SPACING.lg,
    color: COLORS.gray[400],
  },
  analysisDetails: {
    gap: SPACING.xs,
  },
  detailText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
  },
  // Formula card
  formulaCard: {
    ...COMPONENTS.card.elevated,
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
  },
  formulaHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  brandBadge: {
    backgroundColor: COLORS.primary + '15',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.full,
    ...SHADOWS.sm,
  },
  brandText: {
    fontSize: TYPOGRAPHY.size.xs,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.primary,
    letterSpacing: TYPOGRAPHY.letterSpacing.wide,
  },
  productsList: {
    gap: SPACING.md,
    marginBottom: SPACING.lg,
  },
  productItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.surface,
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.lg,
    ...SHADOWS.sm,
  },
  productIcon: {
    fontSize: 24,
    marginRight: SPACING.md,
  },
  productDetails: {
    flex: 1,
  },
  productName: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.medium,
    fontWeight: '500',
    color: COLORS.text,
    marginBottom: SPACING.xs,
  },
  productAmount: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    color: COLORS.primary,
    fontWeight: '600',
  },
  processingInfo: {
    flexDirection: 'row',
    gap: SPACING.md,
  },
  infoItem: {
    flex: 1,
    backgroundColor: COLORS.surface,
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.lg,
    alignItems: 'center',
    ...SHADOWS.sm,
  },
  infoIcon: {
    fontSize: 20,
    marginBottom: SPACING.xs,
  },
  infoLabel: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.gray[600],
    marginBottom: SPACING.xs,
  },
  infoValue: {
    fontSize: FONT_SIZES.md,
    fontWeight: '600',
    color: COLORS.gray[900],
  },
  // Cost card
  costCard: {
    ...COMPONENTS.card.elevated,
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
  },
  costBreakdown: {
    marginBottom: SPACING.lg,
  },
  costRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SPACING.sm,
  },
  costLabel: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
  },
  costValue: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.text,
  },
  priceValue: {
    color: COLORS.primary,
    fontSize: TYPOGRAPHY.size.lg,
    fontFamily: TYPOGRAPHY.fontFamily.bold,
    fontWeight: '700',
  },
  divider: {
    height: 1,
    backgroundColor: COLORS.gray[100],
    marginVertical: SPACING.sm,
  },
  marginIndicator: {
    alignItems: 'center',
  },
  marginLabel: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
    marginBottom: SPACING.sm,
  },
  marginBar: {
    width: '100%',
    height: 20,
    backgroundColor: COLORS.surface,
    borderRadius: BORDER_RADIUS.full,
    overflow: 'hidden',
    marginBottom: SPACING.sm,
    ...SHADOWS.sm,
  },
  marginFill: {
    height: '100%',
    borderRadius: BORDER_RADIUS.full,
  },
  marginPercentage: {
    fontSize: TYPOGRAPHY.size.xl,
    fontFamily: TYPOGRAPHY.fontFamily.bold,
    fontWeight: '700',
  },
  costHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  setupButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: COLORS.primary + '10',
    borderRadius: BORDER_RADIUS.sm,
  },
  setupText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.primary,
    fontWeight: '500',
  },
  realCostBadge: {
    backgroundColor: COLORS.success + '15',
    paddingHorizontal: SPACING.sm,
    paddingVertical: 2,
    borderRadius: BORDER_RADIUS.md,
    marginLeft: SPACING.sm,
    ...SHADOWS.sm,
  },
  realCostText: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.success,
    fontWeight: '600',
  },
  infoBox: {
    flexDirection: 'row',
    backgroundColor: '#FFF9C4',
    borderRadius: BORDER_RADIUS.sm,
    padding: 12,
    marginTop: 12,
    gap: 8,
  },
  infoText: {
    flex: 1,
    fontSize: FONT_SIZES.sm,
    color: '#666',
    lineHeight: 18,
  },
  // Conversion button
  conversionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.white,
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
    padding: SPACING.lg,
    borderRadius: BORDER_RADIUS.xl,
    borderWidth: 2,
    borderColor: COLORS.primary + '20',
    ...SHADOWS.lg,
  },
  conversionIcon: {
    fontSize: 32,
    marginRight: SPACING.md,
  },
  conversionTextContainer: {
    flex: 1,
  },
  conversionTitle: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: SPACING.xs,
  },
  conversionSubtitle: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
  },
  conversionArrow: {
    fontSize: 20,
    color: COLORS.gray[400],
  },
  // Notes card
  notesCard: {
    ...COMPONENTS.card.base,
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.xl,
    backgroundColor: COLORS.info + '10',
  },
  notesTitle: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: SPACING.sm,
  },
  notesText: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
    lineHeight: TYPOGRAPHY.size.sm * TYPOGRAPHY.lineHeight.relaxed,
  },
  // History styles
  historyCard: {
    backgroundColor: COLORS.info + '08',
    borderWidth: 1,
    borderColor: COLORS.info + '20',
    marginBottom: SPACING.lg,
  },
  historyHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.md,
    gap: SPACING.sm,
  },
  historyTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: '600',
    color: COLORS.gray[900],
  },
  historyContent: {
    gap: SPACING.sm,
  },
  adjustmentRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.sm,
    paddingVertical: SPACING.xs,
  },
  adjustmentText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[700],
    flex: 1,
  },
  historyNote: {
    marginTop: SPACING.sm,
    padding: SPACING.sm,
    backgroundColor: COLORS.white,
    borderRadius: BORDER_RADIUS.sm,
  },
  historyNoteLabel: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.gray[600],
    marginBottom: SPACING.xs,
  },
  historyNoteText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[800],
    fontStyle: 'italic',
  },
});
