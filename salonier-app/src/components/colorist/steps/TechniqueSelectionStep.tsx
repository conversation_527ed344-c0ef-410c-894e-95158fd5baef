import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { COLORS, SPACING, FONT_SIZES, BORDER_RADIUS } from '../../../constants';
import { TYPOGRAPHY, SHADOWS, COMPONENTS } from '../../../constants/design-system';
import { useConsultation } from '../../../hooks/useConsultation';
import Card from '../../common/Card';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { CorrectionTechniqueAI, TechniqueRecommendation } from '../../../services/correctionTechniqueAI';

interface TechniqueOption {
  id: string;
  title: string;
  description: string;
  icon: any;
  recommendedFor?: string[];
  processingTime: string;
  difficulty: 'easy' | 'medium' | 'hard';
}

const COLORING_TECHNIQUES: TechniqueOption[] = [
  {
    id: 'global',
    title: 'Coloración Global',
    description: 'Aplicación uniforme en todo el cabello',
    icon: 'palette',
    recommendedFor: ['Cambio total de color', 'Cobertura de canas'],
    processingTime: '30-45 min',
    difficulty: 'easy',
  },
  {
    id: 'roots',
    title: 'Retoque de Raíces',
    description: 'Solo aplicación en el crecimiento',
    icon: 'arrow-up-bold',
    recommendedFor: ['Mantenimiento mensual', 'Canas en raíz'],
    processingTime: '30-35 min',
    difficulty: 'easy',
  },
  {
    id: 'highlights',
    title: 'Mechas con Papel',
    description: 'Técnica tradicional con aluminio',
    icon: 'texture',
    recommendedFor: ['Iluminación precisa', 'Contraste marcado'],
    processingTime: '60-90 min',
    difficulty: 'medium',
  },
  {
    id: 'balayage',
    title: 'Balayage',
    description: 'Técnica de pincelado a mano alzada',
    icon: 'brush',
    recommendedFor: ['Efecto natural', 'Bajo mantenimiento'],
    processingTime: '90-120 min',
    difficulty: 'hard',
  },
];

const CORRECTION_TECHNIQUES: TechniqueOption[] = [
  {
    id: 'neutralization',
    title: 'Neutralización Directa',
    description: 'Aplicación de color neutralizante sin decolorar',
    icon: 'circle-half',
    recommendedFor: ['Tonos naranjas leves', 'Reflejos no deseados'],
    processingTime: '20-30 min',
    difficulty: 'easy',
  },
  {
    id: 'pre_pigmentation',
    title: 'Pre-pigmentación + Color',
    description: 'Reponer pigmentos antes del color final',
    icon: 'gradient-vertical',
    recommendedFor: ['De muy claro a oscuro', 'Cabellos porosos'],
    processingTime: '45-60 min',
    difficulty: 'medium',
  },
  {
    id: 'gentle_lightening',
    title: 'Decoloración Suave + Matiz',
    description: 'Aclarado controlado seguido de tonalización',
    icon: 'white-balance-sunny',
    recommendedFor: ['Manchas oscuras', 'Color muy oscuro'],
    processingTime: '60-90 min',
    difficulty: 'hard',
  },
  {
    id: 'color_bath',
    title: 'Baño de Color',
    description: 'Aplicación diluida para ajustes sutiles',
    icon: 'water',
    recommendedFor: ['Ajustes menores', 'Cabello dañado'],
    processingTime: '15-20 min',
    difficulty: 'easy',
  },
  {
    id: 'mordant',
    title: 'Mordiente + Color',
    description: 'Preparación especial para cabello resistente',
    icon: 'lock-open',
    recommendedFor: ['Cabello muy dañado', 'Canas resistentes'],
    processingTime: '40-50 min',
    difficulty: 'medium',
  },
];

export default function TechniqueSelectionStep() {
  const { state, setTechnique } = useConsultation();
  const [selectedTechnique, setSelectedTechnique] = useState(state.technique || 'global');
  const [aiRecommendation, setAiRecommendation] = useState<TechniqueRecommendation | null>(null);
  
  const isCorrection = state.technique === 'correction';
  const techniques = isCorrection ? CORRECTION_TECHNIQUES : COLORING_TECHNIQUES;
  
  // Analizar con IA cuando hay datos disponibles
  useEffect(() => {
    if (isCorrection && state.desiredColor?.correctionGoal && state.hairAnalysis) {
      const analysisData = {
        problemType: state.desiredColor.correctionGoal.problemType,
        priority: state.desiredColor.correctionGoal.priority,
        currentHair: state.hairAnalysis,
        targetColor: state.desiredColor,
        clientExpectation: state.desiredColor.correctionGoal.clientExpectation,
        professionalAssessment: state.desiredColor.correctionGoal.professionalAssessment
      };
      
      const recommendation = CorrectionTechniqueAI.analyzeTechnique(analysisData);
      setAiRecommendation(recommendation);
      
      // Auto-seleccionar la técnica recomendada si no hay una seleccionada
      if (!selectedTechnique || selectedTechnique === 'correction') {
        setSelectedTechnique(recommendation.techniqueId as any);
        setTechnique(recommendation.techniqueId as any);
      }
    } else if (!isCorrection && state.hairAnalysis && state.desiredColor) {
      // IA para coloración normal
      const recommendation = CorrectionTechniqueAI.analyzeColoringTechnique(
        state.hairAnalysis,
        state.desiredColor,
        {}
      );
      setAiRecommendation(recommendation);
    }
  }, [state.desiredColor, state.hairAnalysis, isCorrection]);
  
  const suggestedTechnique = aiRecommendation?.techniqueId;
  
  const handleSelectTechnique = (techniqueId: string) => {
    setSelectedTechnique(techniqueId as any);
    setTechnique(techniqueId as any);
  };
  
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return COLORS.success;
      case 'medium': return COLORS.warning;
      case 'hard': return COLORS.error;
      default: return COLORS.gray[500];
    }
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.header}>
        <Text style={styles.title}>
          {isCorrection ? '🔧 Técnica de Corrección' : '🎨 Técnica de Aplicación'}
        </Text>
        <Text style={styles.subtitle}>
          {isCorrection 
            ? 'Selecciona la mejor técnica según el problema detectado'
            : 'Elige cómo aplicarás el color'
          }
        </Text>
      </View>

      {aiRecommendation && (
        <Card style={styles.suggestionCard} elevation="medium">
          <View style={styles.suggestionHeader}>
            <MaterialCommunityIcons 
              name="robot" 
              size={24} 
              color={COLORS.primary} 
            />
            <Text style={styles.suggestionTitle}>Recomendación IA</Text>
            <View style={styles.confidenceBadge}>
              <Text style={styles.confidenceText}>
                {Math.round(aiRecommendation.confidence * 100)}%
              </Text>
            </View>
          </View>
          
          <Text style={styles.recommendedTechnique}>
            {techniques.find(t => t.id === aiRecommendation.techniqueId)?.title}
          </Text>
          
          <Text style={styles.reasoning}>
            {aiRecommendation.reasoning}
          </Text>
          
          <View style={styles.sessionInfo}>
            <MaterialCommunityIcons name="clock-outline" size={16} color={COLORS.gray[600]} />
            <Text style={styles.sessionText}>
              Estimado: {aiRecommendation.sessionEstimate}
            </Text>
          </View>
          
          {aiRecommendation.warnings && aiRecommendation.warnings.length > 0 && (
            <View style={styles.warningsContainer}>
              <MaterialCommunityIcons name="alert-outline" size={16} color={COLORS.warning} />
              <Text style={styles.warningText}>
                {aiRecommendation.warnings.join('. ')}
              </Text>
            </View>
          )}
        </Card>
      )}

      <View style={styles.techniquesGrid}>
        {techniques.map((technique) => {
          const isSelected = selectedTechnique === technique.id;
          const isSuggested = technique.id === suggestedTechnique;
          
          return (
            <TouchableOpacity
              key={technique.id}
              style={[
                styles.techniqueCard,
                isSelected && styles.selectedCard,
                isSuggested && styles.suggestedCard,
              ]}
              onPress={() => handleSelectTechnique(technique.id)}
              activeOpacity={0.8}
            >
              {isSuggested && (
                <View style={styles.suggestedBadge}>
                  <Text style={styles.suggestedBadgeText}>RECOMENDADO</Text>
                </View>
              )}
              
              <View style={[
                styles.iconContainer,
                isSelected && styles.selectedIconContainer,
              ]}>
                <MaterialCommunityIcons 
                  name={technique.icon} 
                  size={32} 
                  color={isSelected ? COLORS.white : COLORS.primary} 
                />
              </View>
              
              <Text style={[
                styles.techniqueTitle,
                isSelected && styles.selectedTitle,
              ]}>
                {technique.title}
              </Text>
              
              <Text style={[
                styles.techniqueDescription,
                isSelected && styles.selectedDescription,
              ]}>
                {technique.description}
              </Text>
              
              <View style={styles.techniqueDetails}>
                <View style={styles.detailItem}>
                  <MaterialCommunityIcons 
                    name="clock-outline" 
                    size={16} 
                    color={isSelected ? COLORS.white : COLORS.gray[600]} 
                  />
                  <Text style={[
                    styles.detailText,
                    isSelected && styles.selectedDetailText,
                  ]}>
                    {technique.processingTime}
                  </Text>
                </View>
                
                <View style={[
                  styles.difficultyBadge,
                  { backgroundColor: getDifficultyColor(technique.difficulty) + '20' },
                ]}>
                  <Text style={[
                    styles.difficultyText,
                    { color: getDifficultyColor(technique.difficulty) },
                  ]}>
                    {technique.difficulty === 'easy' ? 'Fácil' : 
                     technique.difficulty === 'medium' ? 'Medio' : 'Avanzado'}
                  </Text>
                </View>
              </View>
              
              {technique.recommendedFor && (
                <View style={styles.recommendedForSection}>
                  <Text style={[
                    styles.recommendedForTitle,
                    isSelected && styles.selectedRecommendedForTitle,
                  ]}>
                    Ideal para:
                  </Text>
                  {technique.recommendedFor.map((item, index) => (
                    <Text 
                      key={index} 
                      style={[
                        styles.recommendedForItem,
                        isSelected && styles.selectedRecommendedForItem,
                      ]}
                    >
                      • {item}
                    </Text>
                  ))}
                </View>
              )}
              
              {isSelected && (
                <View style={styles.selectedIndicator}>
                  <MaterialCommunityIcons 
                    name="check-circle" 
                    size={24} 
                    color={COLORS.white} 
                  />
                </View>
              )}
            </TouchableOpacity>
          );
        })}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    padding: SPACING.lg,
    paddingBottom: SPACING.md,
  },
  title: {
    fontSize: TYPOGRAPHY.size['3xl'],
    fontFamily: TYPOGRAPHY.fontFamily.bold,
    color: COLORS.text,
    marginBottom: SPACING.xs,
  },
  subtitle: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.gray[600],
  },
  suggestionCard: {
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
    padding: SPACING.lg,
    backgroundColor: COLORS.surface,
    borderLeftWidth: 4,
    borderLeftColor: COLORS.primary,
    ...SHADOWS.lg,
  },
  suggestionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  suggestionTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: 'bold',
    color: COLORS.gray[900],
    marginLeft: SPACING.sm,
    flex: 1,
  },
  confidenceBadge: {
    backgroundColor: COLORS.secondary,
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.full,
    ...SHADOWS.sm,
  },
  confidenceText: {
    fontSize: TYPOGRAPHY.size.xs,
    fontFamily: TYPOGRAPHY.fontFamily.bold,
    color: COLORS.white,
  },
  recommendedTechnique: {
    fontSize: FONT_SIZES.lg,
    fontWeight: 'bold',
    color: COLORS.primary,
    marginBottom: SPACING.sm,
  },
  reasoning: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[700],
    lineHeight: 20,
    marginBottom: SPACING.sm,
  },
  sessionInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  sessionText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
    marginLeft: SPACING.xs,
  },
  warningsContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: COLORS.surface,
    padding: SPACING.sm,
    borderRadius: BORDER_RADIUS.lg,
    marginTop: SPACING.xs,
    borderLeftWidth: 3,
    borderLeftColor: COLORS.warning,
  },
  warningText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.warning,
    marginLeft: SPACING.xs,
    flex: 1,
    lineHeight: 18,
  },
  techniquesGrid: {
    paddingHorizontal: SPACING.lg,
    paddingBottom: SPACING.xl,
  },
  techniqueCard: {
    backgroundColor: COLORS.white,
    borderRadius: BORDER_RADIUS.xl,
    padding: SPACING.lg,
    marginBottom: SPACING.md,
    position: 'relative',
    ...SHADOWS.md,
  },
  selectedCard: {
    backgroundColor: COLORS.secondary,
    ...SHADOWS.lg,
  },
  suggestedCard: {
    borderWidth: 2,
    borderColor: COLORS.warning,
  },
  suggestedBadge: {
    position: 'absolute',
    top: -10,
    right: SPACING.md,
    backgroundColor: COLORS.warning,
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.full,
    ...SHADOWS.sm,
  },
  suggestedBadgeText: {
    fontSize: TYPOGRAPHY.size.xs,
    fontFamily: TYPOGRAPHY.fontFamily.bold,
    color: COLORS.white,
  },
  iconContainer: {
    width: 56,
    height: 56,
    borderRadius: BORDER_RADIUS.full,
    backgroundColor: COLORS.surface,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.md,
    ...SHADOWS.sm,
  },
  selectedIconContainer: {
    backgroundColor: COLORS.white + '30',
    ...SHADOWS.none,
  },
  techniqueTitle: {
    fontSize: TYPOGRAPHY.size.lg,
    fontFamily: TYPOGRAPHY.fontFamily.bold,
    color: COLORS.text,
    marginBottom: SPACING.xs,
  },
  selectedTitle: {
    color: COLORS.white,
  },
  techniqueDescription: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.gray[600],
    marginBottom: SPACING.md,
  },
  selectedDescription: {
    color: COLORS.white + '90',
  },
  techniqueDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  detailText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
    marginLeft: SPACING.xs,
  },
  selectedDetailText: {
    color: COLORS.white,
  },
  difficultyBadge: {
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.full,
  },
  difficultyText: {
    fontSize: TYPOGRAPHY.size.xs,
    fontFamily: TYPOGRAPHY.fontFamily.bold,
  },
  recommendedForSection: {
    borderTopWidth: 1,
    borderTopColor: COLORS.gray[200],
    paddingTop: SPACING.sm,
  },
  recommendedForTitle: {
    fontSize: FONT_SIZES.sm,
    fontWeight: 'bold',
    color: COLORS.gray[700],
    marginBottom: SPACING.xs,
  },
  selectedRecommendedForTitle: {
    color: COLORS.white + '90',
  },
  recommendedForItem: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
    marginBottom: SPACING.xs,
  },
  selectedRecommendedForItem: {
    color: COLORS.white + '80',
  },
  selectedIndicator: {
    position: 'absolute',
    top: SPACING.md,
    right: SPACING.md,
  },
});