import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Modal,
  TextInput,
} from 'react-native';
import Toast from 'react-native-toast-message';
import { COLORS, SPACING, FONT_SIZES, BORDER_RADIUS } from '../../../constants';
import { TYPOGRAPHY, SHADOWS, COMPONENTS } from '../../../constants/design-system';
import { useConsultation } from '../../../hooks/useConsultation';
import Card from '../../common/Card';
import Icon from 'react-native-vector-icons/Ionicons';
import DigitalSignature from '../DigitalSignature';
import { dataService } from '../../../services/dataService';

interface CheckItem {
  id: keyof SafetyChecks;
  label: string;
  description: string;
  icon: string;
  critical: boolean;
}

interface SafetyChecks {
  allergyTest: boolean;
  consentSigned: boolean;
  allergiesReviewed: boolean;
  productsReady: boolean;
}

const CHECK_ITEMS: CheckItem[] = [
  {
    id: 'allergyTest',
    label: 'Test de Alergia',
    description: 'Prueba de mechón realizada 48h antes',
    icon: '🧪',
    critical: true,
  },
  {
    id: 'consentSigned',
    label: 'Consentimiento Firmado',
    description: 'Cliente informado de riesgos y procedimiento',
    icon: '📝',
    critical: true,
  },
  {
    id: 'allergiesReviewed',
    label: 'Alergias Revisadas',
    description: 'Historial de alergias verificado',
    icon: '⚠️',
    critical: true,
  },
  {
    id: 'productsReady',
    label: 'Productos Preparados',
    description: 'Material y productos listos para usar',
    icon: '📦',
    critical: false,
  },
];

export default function SafetyCheckStep() {
  const { state, setSafetyChecks } = useConsultation();
  const [checks, setChecks] = useState<SafetyChecks>(state.safetyChecks);
  const [showConsentModal, setShowConsentModal] = useState(false);
  const [clientSignature, setClientSignature] = useState('');
  const [protocolValid, setProtocolValid] = useState<boolean | null>(null);
  // const [showStrandTestModal, setShowStrandTestModal] = useState(false);

  const hasAllergies = state.client?.allergies && state.client.allergies.length > 0;
  const isCorrection =
    state.technique === 'correction' ||
    ['neutralization', 'pre_pigmentation', 'gentle_lightening', 'color_bath', 'mordant'].includes(
      state.technique
    );

  // Verificar si el protocolo está vigente
  useEffect(() => {
    if (state.client?.safety_protocol) {
      const protocol = state.client.safety_protocol;
      const today = new Date();

      // Verificar test de alergia (6 meses)
      const allergyTestValid = protocol.allergy_test_valid_until
        ? new Date(protocol.allergy_test_valid_until) > today
        : false;

      // Verificar consentimiento (1 año)
      const consentValid = protocol.consent_valid_until
        ? new Date(protocol.consent_valid_until) > today
        : false;

      // Si ambos están vigentes y no es corrección, auto-completar
      if (allergyTestValid && consentValid && !isCorrection) {
        setProtocolValid(true);
        setChecks({
          allergyTest: true,
          consentSigned: true,
          allergiesReviewed: true,
          productsReady: false,
        });
      } else {
        setProtocolValid(false);
      }
    } else {
      setProtocolValid(false);
    }
  }, [state.client, isCorrection]);

  // Actualizamos el contexto solo cuando los checks cambian realmente
  useEffect(() => {
    const currentChecks = JSON.stringify(checks);
    const stateChecks = JSON.stringify(state.safetyChecks);

    if (currentChecks !== stateChecks) {
      setSafetyChecks(checks);
    }
  }, [checks, state.safetyChecks]);

  const toggleCheck = (id: keyof SafetyChecks) => {
    if (id === 'consentSigned' && !checks.consentSigned) {
      setShowConsentModal(true);
      return;
    }

    if (id === 'allergiesReviewed' && hasAllergies && !checks.allergiesReviewed) {
      Toast.show({
        type: 'warning',
        text1: 'Alergias Detectadas',
        text2: `${state.client?.allergies?.join(', ')} - Toca para confirmar revisión`,
        position: 'top',
        visibilityTime: 5000,
        onPress: () => {
          Toast.hide();
          setChecks(prev => ({ ...prev, allergiesReviewed: true }));
          Toast.show({
            type: 'success',
            text1: 'Confirmado',
            text2: 'Alergias revisadas y precauciones tomadas',
            position: 'top',
          });
        },
      });
      return;
    }

    setChecks(prev => ({ ...prev, [id]: !prev[id] }));
  };

  const handleConsentSign = () => {
    if (clientSignature.trim().length < 3) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Por favor ingresa el nombre completo del cliente',
        position: 'top',
      });
      return;
    }
    setChecks(prev => ({ ...prev, consentSigned: true }));
    setShowConsentModal(false);
    setClientSignature('');
  };

  const handleDigitalSignature = async (signatureBase64: string) => {
    // Guardar la firma en el protocolo del cliente
    if (state.client) {
      const today = new Date();
      const sixMonthsLater = new Date(today);
      sixMonthsLater.setMonth(sixMonthsLater.getMonth() + 6);
      const oneYearLater = new Date(today);
      oneYearLater.setFullYear(oneYearLater.getFullYear() + 1);

      const updatedClient = {
        ...state.client,
        safety_protocol: {
          last_allergy_test: today.toISOString(),
          allergy_test_valid_until: sixMonthsLater.toISOString(),
          last_consent_signed: today.toISOString(),
          consent_valid_until: oneYearLater.toISOString(),
          digital_signature: signatureBase64,
          known_allergies: state.client.allergies || [],
        },
      };

      // Actualizar en el servicio de datos
      await dataService.clients.update(state.client.id, updatedClient);

      // Actualizar checks
      setChecks(prev => ({ ...prev, consentSigned: true }));
      setShowConsentModal(false);

      Toast.show({
        type: 'success',
        text1: 'Firma Guardada',
        text2: 'El consentimiento ha sido firmado y enviado al cliente.',
        position: 'top',
      });
    }
  };

  const allCriticalChecked =
    CHECK_ITEMS.filter(item => item.critical).every(item => checks[item.id]) &&
    (!isCorrection || checks.allergyTest); // En corrección, el test de mechón es obligatorio

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.header}>
        <Text style={styles.title}>Protocolo de Seguridad</Text>
        <Text style={styles.subtitle}>
          {protocolValid
            ? 'Protocolo vigente - Verifica los productos'
            : 'Completa todos los puntos antes de continuar'}
        </Text>
      </View>

      {/* Mostrar vigencia del protocolo si existe */}
      {state.client?.safety_protocol && (
        <Card style={styles.protocolStatus} elevation="low">
          <View style={styles.protocolStatusContent}>
            <Icon
              name="shield-checkmark"
              size={24}
              color={protocolValid ? COLORS.success : COLORS.warning}
            />
            <View style={styles.protocolInfo}>
              <Text style={styles.protocolStatusTitle}>
                {protocolValid ? 'Protocolo Vigente' : 'Protocolo Requiere Actualización'}
              </Text>
              {state.client.safety_protocol.allergy_test_valid_until && (
                <Text style={styles.protocolStatusDate}>
                  Test válido hasta:{' '}
                  {new Date(
                    state.client.safety_protocol.allergy_test_valid_until
                  ).toLocaleDateString()}
                </Text>
              )}
              {state.client.safety_protocol.consent_valid_until && (
                <Text style={styles.protocolStatusDate}>
                  Consentimiento hasta:{' '}
                  {new Date(state.client.safety_protocol.consent_valid_until).toLocaleDateString()}
                </Text>
              )}
            </View>
          </View>
        </Card>
      )}

      {/* Alerta de alergias si existen */}
      {hasAllergies && (
        <Card style={styles.allergyAlert} elevation="medium">
          <Text style={styles.allergyIcon}>🚨</Text>
          <View style={styles.allergyContent}>
            <Text style={styles.allergyTitle}>¡Atención! Cliente con alergias</Text>
            <Text style={styles.allergyList}>{state.client?.allergies?.join(', ')}</Text>
            <Text style={styles.allergyWarning}>Usa productos libres de estos componentes</Text>
          </View>
        </Card>
      )}

      {/* Alerta de test de mechón para correcciones */}
      {isCorrection && (
        <Card
          style={[
            styles.allergyAlert,
            { backgroundColor: COLORS.warning + '10', borderColor: COLORS.warning },
          ]}
          elevation="high"
        >
          <Text style={styles.allergyIcon}>🧬</Text>
          <View style={styles.allergyContent}>
            <Text style={[styles.allergyTitle, { color: COLORS.warning }]}>
              TEST DE MECHÓN OBLIGATORIO
            </Text>
            <Text style={styles.allergyList}>
              Al ser una corrección de color, es CRUCIAL realizar un test de mechón antes de
              proceder.
            </Text>
            <TouchableOpacity
              style={styles.strandTestButton}
              onPress={() =>
                Toast.show({
                  type: 'info',
                  text1: 'Protocolo Test de Mechón',
                  text2: 'Selecciona mechón → Aplica fórmula → Procesa → Evalúa → Ajusta',
                  position: 'top',
                  visibilityTime: 5000,
                })
              }
            >
              <Text style={styles.strandTestButtonText}>Ver protocolo completo</Text>
            </TouchableOpacity>
          </View>
        </Card>
      )}

      {/* Lista de verificación */}
      <View style={styles.checklistContainer}>
        {CHECK_ITEMS.map(item => {
          const isChecked = checks[item.id];
          const needsAttention = item.id === 'allergiesReviewed' && hasAllergies && !isChecked;

          return (
            <TouchableOpacity
              key={item.id}
              onPress={() => toggleCheck(item.id)}
              activeOpacity={0.7}
            >
              <Card
                style={[
                  styles.checkItem,
                  isChecked && styles.checkedItem,
                  needsAttention && styles.warningItem,
                ]}
                elevation={isChecked ? 'medium' : 'low'}
              >
                <View style={styles.checkItemLeft}>
                  <Text style={styles.checkIcon}>{item.icon}</Text>
                  <View style={styles.checkItemContent}>
                    <Text style={[styles.checkLabel, isChecked && styles.checkedLabel]}>
                      {item.label}
                    </Text>
                    <Text style={styles.checkDescription}>{item.description}</Text>
                  </View>
                </View>
                <View
                  style={[
                    styles.checkbox,
                    isChecked && styles.checkboxChecked,
                    needsAttention && styles.checkboxWarning,
                  ]}
                >
                  {isChecked && <Text style={styles.checkmark}>✓</Text>}
                </View>
              </Card>
            </TouchableOpacity>
          );
        })}
      </View>

      {/* Resumen de seguridad */}
      <Card style={styles.summaryCard} elevation="low">
        <Text style={styles.summaryTitle}>
          {allCriticalChecked
            ? '✅ Todos los puntos críticos completados'
            : '⏳ Completa todos los puntos críticos'}
        </Text>
        {!allCriticalChecked && (
          <Text style={styles.summarySubtitle}>
            Los items marcados con verificación son obligatorios
          </Text>
        )}
      </Card>

      {/* Modal de firma digital */}
      <DigitalSignature
        visible={showConsentModal}
        onClose={() => setShowConsentModal(false)}
        onSign={handleDigitalSignature}
        clientName={state.client?.full_name || 'Cliente'}
        clientEmail={state.client?.email}
        clientPhone={state.client?.phone}
      />

      {/* Modal antiguo - comentado
      <Modal
        visible={showConsentModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowConsentModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Consentimiento Informado</Text>
            
            <ScrollView style={styles.consentTextContainer}>
              <Text style={styles.consentText}>
                El cliente ha sido informado sobre:{'\n\n'}
                • Los productos químicos a utilizar{'\n'}
                • Posibles riesgos y reacciones{'\n'}
                • La importancia del test de alergia{'\n'}
                • El procedimiento completo{'\n'}
                • Los cuidados post-tratamiento{'\n\n'}
                El cliente acepta el tratamiento bajo su responsabilidad.
              </Text>
            </ScrollView>

            <Text style={styles.signatureLabel}>
              Firma del cliente (nombre completo):
            </Text>
            <TextInput
              style={styles.signatureInput}
              placeholder="Nombre completo del cliente"
              value={clientSignature}
              onChangeText={setClientSignature}
              autoCapitalize="words"
            />

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => setShowConsentModal(false)}
              >
                <Text style={styles.cancelButtonText}>Cancelar</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.modalButton, styles.signButton]}
                onPress={handleConsentSign}
              >
                <Text style={styles.signButtonText}>Firmar</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
      */}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    padding: SPACING.lg,
    paddingBottom: SPACING.md,
  },
  title: {
    fontSize: TYPOGRAPHY.size['2xl'],
    fontFamily: TYPOGRAPHY.fontFamily.bold,
    color: COLORS.text,
    marginBottom: SPACING.xs,
  },
  subtitle: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.gray[600],
  },
  allergyAlert: {
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
    backgroundColor: COLORS.surface,
    ...SHADOWS.lg,
    flexDirection: 'row',
    alignItems: 'center',
    borderLeftWidth: 4,
    borderLeftColor: COLORS.error,
  },
  allergyIcon: {
    fontSize: 32,
    marginRight: SPACING.md,
  },
  allergyContent: {
    flex: 1,
  },
  allergyTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: '600',
    color: COLORS.error,
    marginBottom: SPACING.xs,
  },
  allergyList: {
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[800],
    fontWeight: '500',
    marginBottom: SPACING.xs,
  },
  allergyWarning: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
  },
  checklistContainer: {
    paddingHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
  },
  checkItem: {
    marginBottom: SPACING.md,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    ...COMPONENTS.card.base,
  },
  checkedItem: {
    backgroundColor: COLORS.success + '10',
    ...SHADOWS.md,
    borderLeftWidth: 3,
    borderLeftColor: COLORS.success,
  },
  warningItem: {
    backgroundColor: COLORS.warning + '10',
    ...SHADOWS.md,
    borderLeftWidth: 3,
    borderLeftColor: COLORS.warning,
  },
  checkItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  checkIcon: {
    fontSize: 24,
    marginRight: SPACING.md,
  },
  checkItemContent: {
    flex: 1,
  },
  checkLabel: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    color: COLORS.text,
    marginBottom: SPACING.xs,
  },
  checkedLabel: {
    color: COLORS.success,
  },
  checkDescription: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.gray[600],
  },
  checkbox: {
    width: 28,
    height: 28,
    borderRadius: BORDER_RADIUS.full,
    backgroundColor: COLORS.surface,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: SPACING.md,
    ...SHADOWS.sm,
  },
  checkboxChecked: {
    backgroundColor: COLORS.secondary,
    ...SHADOWS.md,
  },
  checkboxWarning: {
    borderColor: COLORS.warning,
  },
  checkmark: {
    color: COLORS.white,
    fontSize: 18,
    fontWeight: 'bold',
  },
  summaryCard: {
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.xl,
    backgroundColor: COLORS.surface,
    ...COMPONENTS.card.elevated,
  },
  summaryTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: '600',
    color: COLORS.gray[800],
    textAlign: 'center',
  },
  summarySubtitle: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
    textAlign: 'center',
    marginTop: SPACING.xs,
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.lg,
  },
  modalContent: {
    backgroundColor: COLORS.white,
    borderRadius: BORDER_RADIUS.xxl,
    padding: SPACING.xl,
    width: '100%',
    maxWidth: 400,
    maxHeight: '80%',
    ...SHADOWS.xl,
  },
  protocolStatus: {
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
    backgroundColor: COLORS.surface,
    ...COMPONENTS.card.base,
  },
  protocolStatusContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.md,
  },
  protocolInfo: {
    flex: 1,
  },
  protocolStatusTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: '600',
    color: COLORS.gray[900],
    marginBottom: SPACING.xs,
  },
  protocolStatusDate: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
    marginTop: SPACING.xs,
  },
  modalTitle: {
    fontSize: FONT_SIZES.xl,
    fontWeight: 'bold',
    color: COLORS.gray[900],
    marginBottom: SPACING.lg,
    textAlign: 'center',
  },
  consentTextContainer: {
    maxHeight: 200,
    marginBottom: SPACING.lg,
  },
  consentText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[700],
    lineHeight: 22,
  },
  signatureLabel: {
    fontSize: FONT_SIZES.md,
    fontWeight: '600',
    color: COLORS.gray[800],
    marginBottom: SPACING.sm,
  },
  signatureInput: {
    ...COMPONENTS.input.base,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    marginBottom: SPACING.lg,
  },
  modalButtons: {
    flexDirection: 'row',
    gap: SPACING.md,
  },
  modalButton: {
    flex: 1,
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.full,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: COLORS.surface,
    ...SHADOWS.sm,
  },
  cancelButtonText: {
    fontSize: TYPOGRAPHY.size.base,
    color: COLORS.text,
    fontFamily: TYPOGRAPHY.fontFamily.medium,
  },
  signButton: {
    backgroundColor: COLORS.secondary,
    ...SHADOWS.md,
  },
  signButtonText: {
    fontSize: TYPOGRAPHY.size.base,
    color: COLORS.white,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
  },
  strandTestButton: {
    marginTop: SPACING.sm,
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.md,
    backgroundColor: COLORS.warning,
    borderRadius: BORDER_RADIUS.sm,
    alignSelf: 'flex-start',
  },
  strandTestButtonText: {
    color: COLORS.white,
    fontSize: FONT_SIZES.sm,
    fontWeight: 'bold',
  },
});
