import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import { COLORS, SPACING, FONT_SIZES, BORDER_RADIUS } from '../../../constants';
import { TYPOGRAPHY, SHADOWS, COMPONENTS } from '../../../constants/design-system';
import { useConsultation } from '../../../hooks/useConsultation';
import { generateBrandConversions } from '../../../services/mockFormulation';
import Card from '../../common/Card';

interface ConversionOption {
  id: string;
  brand_id?: string;
  brand?: any;
  formula: any;
  total_cost: number;
  suggested_price: number;
  confidence_level: number;
  availability: 'in_stock' | 'low_stock' | 'out_of_stock';
  differences: {
    price_difference: number;
    time_difference: number;
    notes: string;
  };
}

export default function BrandConversionStep() {
  const { state, setSelectedFormulation } = useConsultation();
  const [loading, setLoading] = useState(true);
  const [conversions, setConversions] = useState<ConversionOption[]>([]);
  const [selectedConversion, setSelectedConversion] = useState<string | null>(null);

  useEffect(() => {
    loadConversions();
  }, []);

  const loadConversions = async () => {
    setLoading(true);
    
    // Simular delay de procesamiento
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    if (state.formulation) {
      const generatedConversions = generateBrandConversions(state.formulation);
      setConversions(generatedConversions);
      
      // Mantener la original como primera opción
      const originalWithDetails: ConversionOption = {
        ...state.formulation,
        brand_id: state.formulation?.brand_id || '4',
        confidence_level: 100,
        availability: 'in_stock' as const,
        differences: {
          price_difference: 0,
          time_difference: 0,
          notes: 'Formulación original recomendada'
        },
        brand: { id: '4', name: "L'Oréal Professional" },
      };
      
      setConversions([originalWithDetails, ...generatedConversions]);
      setSelectedConversion(originalWithDetails.id);
    }
    
    setLoading(false);
  };

  const handleSelectConversion = (conversionId: string) => {
    setSelectedConversion(conversionId);
    const selected = conversions.find(c => c.id === conversionId);
    if (selected) {
      setSelectedFormulation(selected as any);
    }
  };

  const formatCurrency = (amount: number) => {
    return `€${amount.toFixed(2)}`;
  };

  const formatPriceDifference = (diff: number) => {
    if (diff === 0) return 'Mismo precio';
    const sign = diff > 0 ? '+' : '';
    return `${sign}€${diff.toFixed(2)}`;
  };

  const formatTimeDifference = (diff: number) => {
    if (diff === 0) return 'Mismo tiempo';
    const sign = diff > 0 ? '+' : '';
    return `${sign}${diff} min`;
  };

  const getAvailabilityColor = (availability: string) => {
    switch (availability) {
      case 'in_stock': return COLORS.success;
      case 'low_stock': return COLORS.warning;
      case 'out_of_stock': return COLORS.error;
      default: return COLORS.gray[500];
    }
  };

  const getAvailabilityText = (availability: string) => {
    switch (availability) {
      case 'in_stock': return 'En stock';
      case 'low_stock': return 'Stock bajo';
      case 'out_of_stock': return 'Sin stock';
      default: return 'Desconocido';
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={COLORS.primary} />
        <Text style={styles.loadingText}>Buscando alternativas disponibles...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.header}>
        <Text style={styles.title}>Conversión de Marcas</Text>
        <Text style={styles.subtitle}>
          Selecciona la marca que prefieras usar para esta formulación
        </Text>
      </View>

      {/* Lista de conversiones */}
      <View style={styles.conversionsList}>
        {conversions.map((conversion, index) => {
          const isSelected = selectedConversion === conversion.id;
          const isOriginal = index === 0;

          return (
            <TouchableOpacity
              key={conversion.id}
              onPress={() => handleSelectConversion(conversion.id)}
              activeOpacity={0.7}
            >
              <Card 
                style={[
                  styles.conversionCard,
                  isSelected && styles.selectedCard,
                ]}
                elevation={isSelected ? 'high' : 'medium'}
              >
                {/* Header con marca */}
                <View style={styles.cardHeader}>
                  <View style={styles.brandInfo}>
                    <Text style={styles.brandName}>{conversion.brand?.name}</Text>
                    {isOriginal && (
                      <View style={styles.originalBadge}>
                        <Text style={styles.originalText}>Original</Text>
                      </View>
                    )}
                  </View>
                  <View style={styles.selectionIndicator}>
                    <View style={[
                      styles.radioOuter,
                      isSelected && styles.radioOuterSelected
                    ]}>
                      {isSelected && <View style={styles.radioInner} />}
                    </View>
                  </View>
                </View>

                {/* Productos */}
                <View style={styles.productsSection}>
                  {conversion.formula.products.map((product: any, idx: number) => (
                    <Text key={idx} style={styles.productText}>
                      • {product.name} - {product.amount}{product.unit}
                    </Text>
                  ))}
                </View>

                {/* Indicadores */}
                <View style={styles.indicatorsGrid}>
                  {/* Precio */}
                  <View style={styles.indicator}>
                    <Text style={styles.indicatorLabel}>Costo</Text>
                    <Text style={styles.indicatorValue}>
                      {formatCurrency(conversion.total_cost)}
                    </Text>
                    {!isOriginal && (
                      <Text style={[
                        styles.indicatorDiff,
                        { color: conversion.differences.price_difference > 0 ? COLORS.error : COLORS.success }
                      ]}>
                        {formatPriceDifference(conversion.differences.price_difference)}
                      </Text>
                    )}
                  </View>

                  {/* Tiempo */}
                  <View style={styles.indicator}>
                    <Text style={styles.indicatorLabel}>Tiempo</Text>
                    <Text style={styles.indicatorValue}>
                      {conversion.formula.processing_time} min
                    </Text>
                    {!isOriginal && (
                      <Text style={[
                        styles.indicatorDiff,
                        { color: conversion.differences.time_difference > 0 ? COLORS.error : COLORS.success }
                      ]}>
                        {formatTimeDifference(conversion.differences.time_difference)}
                      </Text>
                    )}
                  </View>

                  {/* Disponibilidad */}
                  <View style={styles.indicator}>
                    <Text style={styles.indicatorLabel}>Stock</Text>
                    <View style={[
                      styles.availabilityBadge,
                      { backgroundColor: getAvailabilityColor(conversion.availability) + '20' }
                    ]}>
                      <Text style={[
                        styles.availabilityText,
                        { color: getAvailabilityColor(conversion.availability) }
                      ]}>
                        {getAvailabilityText(conversion.availability)}
                      </Text>
                    </View>
                  </View>

                  {/* Confianza */}
                  <View style={styles.indicator}>
                    <Text style={styles.indicatorLabel}>Precisión</Text>
                    <Text style={[
                      styles.confidenceValue,
                      { color: conversion.confidence_level >= 95 ? COLORS.success : COLORS.warning }
                    ]}>
                      {conversion.confidence_level}%
                    </Text>
                  </View>
                </View>

                {/* Notas */}
                {conversion.differences.notes && (
                  <Text style={styles.notesText}>
                    💡 {conversion.differences.notes}
                  </Text>
                )}
              </Card>
            </TouchableOpacity>
          );
        })}
      </View>

      {/* Resumen de selección */}
      <Card style={styles.summaryCard} elevation="low">
        <Text style={styles.summaryTitle}>
          {selectedConversion ? '✅ Marca seleccionada' : '⚠️ Selecciona una marca'}
        </Text>
        {selectedConversion && (
          <Text style={styles.summaryText}>
            Pulsa "Confirmar Selección" para continuar con{' '}
            {conversions.find(c => c.id === selectedConversion)?.brand?.name}
          </Text>
        )}
      </Card>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.surface,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.xl,
  },
  loadingText: {
    marginTop: SPACING.md,
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
  },
  header: {
    padding: SPACING.lg,
    paddingBottom: SPACING.md,
  },
  title: {
    fontSize: TYPOGRAPHY.size['2xl'],
    fontFamily: TYPOGRAPHY.fontFamily.bold,
    fontWeight: '700',
    color: COLORS.text,
    marginBottom: SPACING.xs,
    letterSpacing: TYPOGRAPHY.letterSpacing.tight,
  },
  subtitle: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
    lineHeight: TYPOGRAPHY.size.base * TYPOGRAPHY.lineHeight.normal,
  },
  conversionsList: {
    paddingHorizontal: SPACING.lg,
    gap: SPACING.md,
  },
  conversionCard: {
    marginBottom: SPACING.md,
    ...COMPONENTS.card.elevated,
  },
  selectedCard: {
    borderWidth: 2,
    borderColor: COLORS.primary,
    ...SHADOWS.xl,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  brandInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.sm,
  },
  brandName: {
    fontSize: TYPOGRAPHY.size.lg,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.text,
  },
  originalBadge: {
    backgroundColor: COLORS.primary + '15',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.full,
    ...SHADOWS.sm,
  },
  originalText: {
    fontSize: TYPOGRAPHY.size.xs,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    color: COLORS.primary,
    fontWeight: '600',
    letterSpacing: TYPOGRAPHY.letterSpacing.wide,
  },
  selectionIndicator: {
    padding: SPACING.xs,
  },
  radioOuter: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: COLORS.gray[200],
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.white,
    ...SHADOWS.sm,
  },
  radioOuterSelected: {
    borderColor: COLORS.primary,
  },
  radioInner: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: COLORS.primary,
  },
  productsSection: {
    marginBottom: SPACING.md,
  },
  productText: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
    marginBottom: SPACING.xs,
    lineHeight: TYPOGRAPHY.size.sm * TYPOGRAPHY.lineHeight.normal,
  },
  indicatorsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.sm,
    marginBottom: SPACING.md,
  },
  indicator: {
    flex: 1,
    minWidth: '45%',
    backgroundColor: COLORS.surface,
    padding: SPACING.sm,
    borderRadius: BORDER_RADIUS.lg,
    alignItems: 'center',
    ...SHADOWS.sm,
  },
  indicatorLabel: {
    fontSize: TYPOGRAPHY.size.xs,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
    marginBottom: SPACING.xs,
    letterSpacing: TYPOGRAPHY.letterSpacing.wide,
    textTransform: 'uppercase',
  },
  indicatorValue: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.text,
  },
  indicatorDiff: {
    fontSize: TYPOGRAPHY.size.xs,
    fontFamily: TYPOGRAPHY.fontFamily.medium,
    fontWeight: '500',
    marginTop: 2,
  },
  availabilityBadge: {
    paddingHorizontal: SPACING.sm,
    paddingVertical: 4,
    borderRadius: BORDER_RADIUS.full,
    ...SHADOWS.sm,
  },
  availabilityText: {
    fontSize: TYPOGRAPHY.size.xs,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
  },
  confidenceValue: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
  },
  notesText: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
    fontStyle: 'italic',
    lineHeight: TYPOGRAPHY.size.sm * TYPOGRAPHY.lineHeight.relaxed,
  },
  summaryCard: {
    ...COMPONENTS.card.base,
    marginHorizontal: SPACING.lg,
    marginVertical: SPACING.lg,
    backgroundColor: COLORS.info + '10',
  },
  summaryTitle: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.text,
    textAlign: 'center',
  },
  summaryText: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginTop: SPACING.xs,
    lineHeight: TYPOGRAPHY.size.sm * TYPOGRAPHY.lineHeight.normal,
  },
});