import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Switch,
  TextInput,
} from 'react-native';
import Toast from 'react-native-toast-message';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { COLORS, SPACING, FONT_SIZES, BORDER_RADIUS, HAIR_LEVELS } from '../../../constants';
import { TYPOGRAPHY, SHADOWS, COMPONENTS } from '../../../constants/design-system';
import { useConsultation } from '../../../hooks/useConsultation';
import Card from '../../common/Card';
import { sendWhatsAppMessage, formatConsultationMessage } from '../../../utils/whatsapp';
import { generateConsultationPDF } from '../../../utils/pdf';
import { dataService } from '../../../services/dataService';
import { User, Product, ColorResult } from '../../../types';
import { useBehaviorTracking } from '../../../hooks/useBehaviorTracking';
import { CorrectionData } from '../../../types/correction-types';
import { InventoryConsumptionService } from '../../../services/inventoryConsumptionService';
import Slider from '@react-native-community/slider';

export default function CompletionStep({ navigation }: any) {
  const { state, saveConsultation, resetConsultation } = useConsultation();
  const [saving, setSaving] = useState(false);
  const [saved, setSaved] = useState(false);
  const [rating, setRating] = useState(0);
  const [user, setUser] = useState<User | null>(null);
  const [consumeInventory, setConsumeInventory] = useState(true);
  const [inventoryProducts, setInventoryProducts] = useState<Product[]>([]);
  const [stockValidation, setStockValidation] = useState<{ valid: boolean; missing: any[] }>({
    valid: true,
    missing: [],
  });
  // const [consumptionResult, setConsumptionResult] = useState<ConsumptionResult | null>(null);

  // Estados para resultado real
  const [resultLevel, setResultLevel] = useState<number>(state.desiredColor?.level || 7);
  const [resultTone, setResultTone] = useState<string>(state.desiredColor?.tone || '');
  const [resultNotes, setResultNotes] = useState<string>('');
  const [clientSatisfaction, setClientSatisfaction] = useState<number>(5);

  const isCorrection = state.technique === 'correction';
  const correctionData = (state.hairAnalysis as any)?.correctionData as CorrectionData | undefined;

  const { trackConsultationCompleted, trackFeatureUsage } = useBehaviorTracking({
    user,
    onUserUpdate: setUser,
  });

  const calculateTotalPrice = () => {
    return state.formulation?.suggested_price || 0;
  };

  const calculateProfit = () => {
    if (!state.formulation) return 0;
    return state.formulation.suggested_price - state.formulation.total_cost;
  };

  useEffect(() => {
    loadUserAndInventory();
  }, []);

  const loadUserAndInventory = async () => {
    try {
      const userData = await dataService.auth.getCurrentUser();
      setUser(userData as any);

      // Load inventory and check stock if user has inventory enabled
      if (
        userData?.inventory_level === 'full_control' ||
        userData?.inventory_level === 'smart_cost'
      ) {
        const products = await dataService.inventory.getProducts(userData.id);
        setInventoryProducts(products as any[]);

        // Check stock availability
        if (state.formulation) {
          const stockCheck = await InventoryConsumptionService.checkStock(
            state.formulation,
            userData.id
          );
          setStockValidation({
            valid: stockCheck.hasStock,
            missing: stockCheck.missing,
          });
        }
      }
    } catch (error) {
      console.error('Error loading user data:', error);
    }
  };

  // const validateStock = (products: Product[]) => {
  //   if (!state.formulation?.formula?.products || !Array.isArray(state.formulation.formula.products)) return;
  //
  //   const missing: any[] = [];
  //   let allValid = true;
  //
  //   state.formulation.formula.products.forEach(formulaProduct => {
  //     // Validate that formulaProduct has a name
  //     if (!formulaProduct.name) return;
  //
  //     // Try to find matching inventory product
  //     const inventoryProduct = products.find(p =>
  //       p.name?.toLowerCase().includes(formulaProduct.name?.toLowerCase() || '') ||
  //       formulaProduct.name?.toLowerCase().includes(p.name?.toLowerCase() || '')
  //     );
  //
  //     if (inventoryProduct) {
  //       if (inventoryProduct.current_stock < formulaProduct.amount) {
  //         allValid = false;
  //         missing.push({
  //           name: formulaProduct.name,
  //           required: formulaProduct.amount,
  //           available: inventoryProduct.current_stock,
  //           unit: inventoryProduct.unit
  //         });
  //       }
  //     }
  //   });
  //
  //   setStockValidation({ valid: allValid, missing });
  // };

  const handleSaveConsultation = async () => {
    // Validate stock if inventory consumption is enabled
    if (user?.inventory_level === 'full_control' && consumeInventory && !stockValidation.valid) {
      Toast.show({
        type: 'warning',
        text1: 'Stock Insuficiente',
        text2: 'No hay suficiente stock. Continuando sin descontar del inventario...',
        position: 'top',
        visibilityTime: 3000,
      });
      setConsumeInventory(false);
      proceedWithSave();
      return;
    }

    proceedWithSave();
  };

  const proceedWithSave = async () => {
    setSaving(true);

    try {
      // Simular guardado
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Consume inventory if enabled
      if (user?.inventory_level === 'full_control' && consumeInventory) {
        await consumeProductsFromInventory();
      }

      // Preparar resultado real
      const actualResult: ColorResult = {
        achieved_level: resultLevel,
        achieved_tone: resultTone,
        evenness: clientSatisfaction >= 4 ? 'perfect' : clientSatisfaction >= 3 ? 'good' : 'uneven',
        client_feedback: `Satisfacción: ${clientSatisfaction}/5`,
        stylist_notes: resultNotes,
        matches_target:
          resultLevel === state.desiredColor?.level && resultTone === state.desiredColor?.tone,
        variation_notes: resultNotes,
        next_session_suggestions: isCorrection
          ? 'Continuar con el proceso de corrección'
          : 'Retoque de raíces en 6-8 semanas',
      };

      // Guardar consulta con resultado real
      const consultationData = {
        ...state,
        actual_result: actualResult,
        client_satisfaction: clientSatisfaction as 1 | 2 | 3 | 4 | 5,
        follow_up_notes: resultNotes,
      };

      // En producción, aquí se guardaría en Supabase
      await saveConsultation(consultationData);

      // Track consultation completion
      const servicePrice = calculateTotalPrice();
      const margin = calculateProfit();
      const productsUsed = state.formulation?.formula.products.map(p => p.name) || [];

      await trackConsultationCompleted(servicePrice, margin, productsUsed);

      // Track feature usage if user viewed costs
      if (user?.inventory_level === 'smart_cost' || user?.inventory_level === 'full_control') {
        await trackFeatureUsage('costs_viewed');
      }

      setSaved(true);
      Toast.show({
        type: 'success',
        text1: 'Consulta Guardada',
        text2:
          consumeInventory && user?.inventory_level === 'full_control'
            ? 'Los productos se han descontado del inventario'
            : 'La consulta se ha guardado exitosamente',
        position: 'top',
      });
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'No se pudo guardar la consulta',
        position: 'top',
      });
    } finally {
      setSaving(false);
    }
  };

  const consumeProductsFromInventory = async () => {
    if (!state.formulation || !user || !state.client) return;

    const result = await InventoryConsumptionService.consumeFormulation(
      state.formulation,
      user.id,
      state.consultationId || `consultation-${Date.now()}`,
      state.client.full_name
    );

    // setConsumptionResult(result);

    if (!result.success && result.errors) {
      console.error('Inventory consumption errors:', result.errors);
    }

    return result;
  };

  const handleSendWhatsApp = async () => {
    if (!state.client?.phone) {
      Toast.show({
        type: 'warning',
        text1: 'Número no disponible',
        text2: 'El cliente no tiene número de teléfono registrado',
        position: 'top',
        onPress: () => {
          Toast.hide();
          navigation.navigate('ClientDetail', { client: state.client });
        },
      });
      return;
    }

    const message = formatConsultationMessage(state);

    Toast.show({
      type: 'info',
      text1: 'Enviando por WhatsApp',
      text2: `Enviando a ${state.client.full_name}...`,
      position: 'top',
    });
    await sendWhatsAppMessage(state.client?.phone || '', message);
  };

  const handleScheduleNext = () => {
    // TODO: Implement appointment scheduling modal
    Toast.show({
      type: 'info',
      text1: 'Próximamente',
      text2: 'Funcionalidad de agendar citas disponible pronto',
      position: 'top',
    });
  };

  const scheduleAppointment = (weeksFromNow: number) => {
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + weeksFromNow * 7);

    // Preparar datos para la nueva cita
    const appointmentData = {
      client: state.client,
      service: state.desiredColor?.technique === 'global' ? 'Retoque de Raíz' : 'Coloración',
      date: futureDate,
      notes: `Retoque programado - Cliente: ${state.client?.full_name}`,
    };

    // Navegar a la pantalla de citas con los datos pre-poblados
    navigation.navigate('Main', {
      screen: 'Appointments',
      params: {
        openCreateModal: true,
        prefilledData: appointmentData,
      },
    });
  };

  const handleNewConsultation = () => {
    resetConsultation();
    navigation.navigate('Dashboard');
  };

  const handleGeneratePDF = async () => {
    Toast.show({
      type: 'info',
      text1: 'Generando PDF',
      text2: 'Preparando el resumen de la consulta...',
      position: 'top',
    });

    try {
      setSaving(true);
      await generateConsultationPDF(state);
      Toast.show({
        type: 'success',
        text1: 'PDF Generado',
        text2: 'El PDF se ha compartido automáticamente',
        position: 'top',
      });
    } catch (error) {
      console.error('Error generating PDF:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'No se pudo generar el PDF',
        position: 'top',
      });
    } finally {
      setSaving(false);
    }
  };

  if (saving) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={COLORS.primary} />
        <Text style={styles.loadingText}>Guardando consulta...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Success header */}
      <View style={styles.successHeader}>
        <Text style={styles.successIcon}>{isCorrection ? '🔧' : '🎉'}</Text>
        <Text style={styles.successTitle}>
          {isCorrection ? '¡Corrección Completada!' : '¡Consulta Completada!'}
        </Text>
        <Text style={styles.successSubtitle}>
          {isCorrection
            ? `Corrección de ${
                correctionData?.problem === 'orange'
                  ? 'tonos naranjas'
                  : correctionData?.problem === 'green'
                    ? 'tonos verdes'
                    : correctionData?.problem === 'uneven'
                      ? 'color desigual'
                      : correctionData?.problem === 'too_dark'
                        ? 'color muy oscuro'
                        : correctionData?.problem === 'too_light'
                          ? 'color muy claro'
                          : 'color'
              } 
               realizada para ${state.client?.full_name}`
            : `Servicio realizado con éxito para ${state.client?.full_name}`}
        </Text>
      </View>

      {/* Actual Result Section */}
      <Card style={styles.resultCard} elevation="medium">
        <Text style={styles.sectionTitle}>Resultado Real Obtenido</Text>

        <View style={styles.resultSection}>
          <Text style={styles.resultLabel}>Nivel logrado:</Text>
          <View style={styles.sliderContainer}>
            <Text style={styles.levelText}>{resultLevel}</Text>
            <Slider
              style={styles.slider}
              minimumValue={1}
              maximumValue={12}
              step={0.5}
              value={resultLevel}
              onValueChange={setResultLevel}
              minimumTrackTintColor={COLORS.primary}
              maximumTrackTintColor={COLORS.gray[300]}
              thumbTintColor={COLORS.primary}
            />
            <Text style={styles.levelName}>{HAIR_LEVELS[Math.round(resultLevel)]}</Text>
          </View>
        </View>

        <View style={styles.resultSection}>
          <Text style={styles.resultLabel}>Tono logrado:</Text>
          <TextInput
            style={styles.toneInput}
            value={resultTone}
            onChangeText={setResultTone}
            placeholder="Ej: Dorado, Ceniza, Natural..."
            placeholderTextColor={COLORS.gray[400]}
          />
        </View>

        <View style={styles.resultSection}>
          <Text style={styles.resultLabel}>Satisfacción del cliente:</Text>
          <View style={styles.satisfactionContainer}>
            {[1, 2, 3, 4, 5].map(level => (
              <TouchableOpacity
                key={level}
                onPress={() => setClientSatisfaction(level)}
                style={[
                  styles.satisfactionButton,
                  clientSatisfaction === level && styles.satisfactionButtonActive,
                ]}
              >
                <Text
                  style={[
                    styles.satisfactionEmoji,
                    clientSatisfaction === level && styles.satisfactionEmojiActive,
                  ]}
                >
                  {level === 1
                    ? '😞'
                    : level === 2
                      ? '😐'
                      : level === 3
                        ? '🙂'
                        : level === 4
                          ? '😊'
                          : '🤩'}
                </Text>
                <Text
                  style={[
                    styles.satisfactionText,
                    clientSatisfaction === level && styles.satisfactionTextActive,
                  ]}
                >
                  {level === 1
                    ? 'Muy insatisfecho'
                    : level === 2
                      ? 'Insatisfecho'
                      : level === 3
                        ? 'Neutral'
                        : level === 4
                          ? 'Satisfecho'
                          : 'Muy satisfecho'}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View style={styles.resultSection}>
          <Text style={styles.resultLabel}>Notas adicionales:</Text>
          <TextInput
            style={styles.notesInput}
            value={resultNotes}
            onChangeText={setResultNotes}
            placeholder="Observaciones sobre el resultado, reacciones del cliente, etc."
            placeholderTextColor={COLORS.gray[400]}
            multiline
            numberOfLines={3}
          />
        </View>

        {/* Result comparison */}
        {state.desiredColor && (
          <View style={styles.comparisonSection}>
            <View style={styles.comparisonHeader}>
              <MaterialCommunityIcons
                name={
                  resultLevel === state.desiredColor.level && resultTone === state.desiredColor.tone
                    ? 'check-circle'
                    : 'alert-circle'
                }
                size={20}
                color={
                  resultLevel === state.desiredColor.level && resultTone === state.desiredColor.tone
                    ? COLORS.success
                    : COLORS.warning
                }
              />
              <Text style={styles.comparisonTitle}>
                {resultLevel === state.desiredColor.level && resultTone === state.desiredColor.tone
                  ? 'Objetivo alcanzado'
                  : 'Diferencia con el objetivo'}
              </Text>
            </View>

            {(resultLevel !== state.desiredColor.level ||
              resultTone !== state.desiredColor.tone) && (
              <View style={styles.comparisonDetails}>
                <Text style={styles.comparisonText}>
                  Objetivo: Nivel {state.desiredColor.level} - {state.desiredColor.tone}
                </Text>
                <Text style={styles.comparisonText}>
                  Resultado: Nivel {resultLevel} - {resultTone || 'Sin especificar'}
                </Text>
              </View>
            )}
          </View>
        )}
      </Card>

      {/* Summary cards */}
      <Card style={styles.summaryCard} elevation="medium">
        <Text style={styles.sectionTitle}>Resumen del Servicio</Text>

        {/* Client info */}
        <View style={styles.summarySection}>
          <Text style={styles.summaryLabel}>Cliente</Text>
          <Text style={styles.summaryValue}>{state.client?.full_name}</Text>
          <Text style={styles.summaryDetail}>{state.client?.phone}</Text>
        </View>

        {/* Service info */}
        <View style={styles.summarySection}>
          <Text style={styles.summaryLabel}>Servicio Realizado</Text>
          <Text style={styles.summaryValue}>
            {isCorrection
              ? `Corrección de Color - ${correctionData?.problemDescription || 'Problema de color'}`
              : state.desiredColor?.description || 'Coloración'}
          </Text>
          <Text style={styles.summaryDetail}>
            Técnica:{' '}
            {state.technique === 'correction'
              ? 'Corrección'
              : state.desiredColor?.technique === 'global'
                ? 'Color Global'
                : state.desiredColor?.technique === 'highlights'
                  ? 'Mechas'
                  : state.desiredColor?.technique === 'balayage'
                    ? 'Balayage'
                    : state.desiredColor?.technique === 'ombre'
                      ? 'Ombré'
                      : state.desiredColor?.technique === 'babylights'
                        ? 'Babylights'
                        : 'Global'}
          </Text>
        </View>

        {/* Formula used */}
        <View style={styles.summarySection}>
          <Text style={styles.summaryLabel}>Fórmula Aplicada</Text>
          {state.formulation?.formula?.products?.map((product, index) => (
            <Text key={index} style={styles.formulaProduct}>
              • {product.name || 'Producto sin nombre'} - {product.amount}
              {product.unit}
            </Text>
          ))}
        </View>

        {/* Timing */}
        <View style={styles.summarySection}>
          <Text style={styles.summaryLabel}>Tiempo de Procesamiento</Text>
          <Text style={styles.summaryValue}>
            {state.formulation?.formula?.processing_time || 35} minutos
          </Text>
        </View>
      </Card>

      {/* Expectativas para correcciones */}
      {isCorrection && (
        <Card
          style={[styles.summaryCard, { backgroundColor: COLORS.warning + '10' }]}
          elevation="high"
        >
          <Text style={[styles.sectionTitle, { color: COLORS.warning }]}>
            ⚠️ Expectativas del Resultado
          </Text>

          <View style={styles.expectationItem}>
            <Text style={styles.expectationLabel}>Resultado logrado:</Text>
            <Text style={styles.expectationValue}>
              {correctionData?.problem === 'orange'
                ? '85-90% neutralizado'
                : correctionData?.problem === 'green'
                  ? '80-85% corregido'
                  : correctionData?.problem === 'uneven'
                    ? '70-80% igualado'
                    : correctionData?.problem === 'too_dark'
                      ? '1-2 tonos más claro'
                      : correctionData?.problem === 'too_light'
                        ? '90% del color objetivo'
                        : '80% mejorado'}
            </Text>
          </View>

          <View style={styles.expectationItem}>
            <Text style={styles.expectationLabel}>Próximos pasos:</Text>
            <Text style={styles.expectationValue}>
              {correctionData?.problem === 'orange' || correctionData?.problem === 'green'
                ? 'Usar shampoo matizador 2x/semana'
                : correctionData?.problem === 'uneven'
                  ? 'Posible segunda sesión en 2-3 semanas'
                  : 'Tratamiento hidratante semanal'}
            </Text>
          </View>

          <View style={styles.expectationItem}>
            <Text style={styles.expectationLabel}>Mantenimiento:</Text>
            <Text style={styles.expectationValue}>
              Revisión recomendada en {isCorrection ? '2-3' : '4-6'} semanas
            </Text>
          </View>

          <Text style={styles.expectationNote}>
            💡 Las correcciones de color pueden requerir múltiples sesiones para alcanzar el
            resultado óptimo sin dañar el cabello.
          </Text>
        </Card>
      )}

      {/* Inventory consumption - Only for full_control users */}
      {user?.inventory_level === 'full_control' && (
        <Card style={styles.inventoryCard} elevation="medium">
          <View style={styles.inventoryHeader}>
            <Text style={styles.sectionTitle}>Control de Inventario</Text>
            <Switch
              value={consumeInventory}
              onValueChange={setConsumeInventory}
              trackColor={{ false: '#ccc', true: COLORS.primary + '50' }}
              thumbColor={consumeInventory ? COLORS.primary : '#f4f3f4'}
            />
          </View>

          {consumeInventory && (
            <>
              <Text style={styles.inventorySubtitle}>
                Los siguientes productos se descontarán del inventario:
              </Text>

              {state.formulation?.formula?.products?.map((product, index) => {
                // Skip products without names
                if (!product.name) return null;

                const inventoryProduct = inventoryProducts.find(
                  p =>
                    p.name?.toLowerCase().includes(product.name?.toLowerCase() || '') ||
                    product.name?.toLowerCase().includes(p.name?.toLowerCase() || '')
                );
                const hasStock =
                  inventoryProduct && inventoryProduct.current_stock >= product.amount;

                return (
                  <View key={index} style={styles.inventoryItem}>
                    <View style={styles.inventoryItemInfo}>
                      <Text style={styles.inventoryProductName}>{product.name}</Text>
                      <Text style={styles.inventoryAmount}>
                        -{product.amount} {product.unit}
                      </Text>
                    </View>
                    {inventoryProduct && (
                      <View style={styles.stockStatus}>
                        <MaterialCommunityIcons
                          name={hasStock ? 'check-circle' : 'alert-circle'}
                          size={20}
                          color={hasStock ? COLORS.success : COLORS.error}
                        />
                        <Text
                          style={[
                            styles.stockText,
                            { color: hasStock ? COLORS.success : COLORS.error },
                          ]}
                        >
                          Stock: {inventoryProduct.current_stock} {inventoryProduct.unit}
                        </Text>
                      </View>
                    )}
                  </View>
                );
              })}

              {!stockValidation.valid && (
                <View style={styles.warningBox}>
                  <MaterialCommunityIcons name="alert" size={20} color={COLORS.warning} />
                  <Text style={styles.warningText}>
                    Stock insuficiente en algunos productos. Se consumirá lo disponible.
                  </Text>
                </View>
              )}
            </>
          )}
        </Card>
      )}

      {/* Financial summary */}
      <Card style={styles.financialCard} elevation="high">
        <Text style={styles.sectionTitle}>Resumen Financiero</Text>

        <View style={styles.priceRow}>
          <Text style={styles.priceLabel}>Costo de productos:</Text>
          <Text style={styles.priceValue}>
            €{state.formulation?.total_cost.toFixed(2) || '0.00'}
          </Text>
        </View>

        <View style={styles.priceRow}>
          <Text style={styles.priceLabel}>Precio del servicio:</Text>
          <Text style={[styles.priceValue, styles.totalPrice]}>
            €{calculateTotalPrice().toFixed(2)}
          </Text>
        </View>

        <View style={styles.divider} />

        <View style={styles.priceRow}>
          <Text style={styles.priceLabel}>Ganancia:</Text>
          <Text style={[styles.priceValue, styles.profitValue]}>
            €{calculateProfit().toFixed(2)}
          </Text>
        </View>

        <View style={styles.profitPercentage}>
          <Text style={styles.profitText}>
            Margen de ganancia: {((calculateProfit() / calculateTotalPrice()) * 100).toFixed(0)}%
          </Text>
        </View>
      </Card>

      {/* Expectativas para corrección */}
      {isCorrection && (
        <Card
          style={[styles.expectationsCard, { backgroundColor: COLORS.info + '10' }]}
          elevation="medium"
        >
          <View style={styles.expectationsHeader}>
            <MaterialCommunityIcons name="information" size={24} color={COLORS.info} />
            <Text style={styles.expectationsTitle}>Expectativas Realistas</Text>
          </View>

          <View style={styles.expectationsList}>
            <Text style={styles.expectationItem}>
              • Las correcciones de color pueden requerir múltiples sesiones
            </Text>
            <Text style={styles.expectationItem}>
              • El resultado final dependerá del estado actual del cabello
            </Text>
            <Text style={styles.expectationItem}>
              • Es normal que el color evolucione en los próximos lavados
            </Text>
            {correctionData?.problem === 'orange' && (
              <Text style={styles.expectationItem}>
                • Los tonos naranjas pueden reaparecer gradualmente
              </Text>
            )}
            {correctionData?.problem === 'green' && (
              <Text style={styles.expectationItem}>
                • Use shampoo sin sulfatos para mantener la corrección
              </Text>
            )}
          </View>

          <View style={styles.maintenanceSection}>
            <Text style={styles.maintenanceTitle}>Cuidados en Casa:</Text>
            <Text style={styles.maintenanceText}>
              - Shampoo matizador 2 veces por semana{'\n'}- Mascarilla hidratante semanal{'\n'}-
              Evitar herramientas de calor excesivo{'\n'}- Protector solar para el cabello
            </Text>
          </View>

          <View style={styles.nextSessionInfo}>
            <MaterialCommunityIcons name="calendar-clock" size={20} color={COLORS.warning} />
            <Text style={styles.nextSessionText}>
              Próxima sesión recomendada: {isCorrection ? '2-3 semanas' : '6-8 semanas'}
            </Text>
          </View>
        </Card>
      )}

      {/* Rating */}
      <Card style={styles.ratingCard} elevation="low">
        <Text style={styles.sectionTitle}>Califica la Experiencia</Text>
        <View style={styles.ratingStars}>
          {[1, 2, 3, 4, 5].map(star => (
            <TouchableOpacity key={star} onPress={() => setRating(star)}>
              <Text style={[styles.star, star <= rating && styles.starFilled]}>
                {star <= rating ? '★' : '☆'}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
        {rating > 0 && (
          <Text style={styles.ratingText}>
            {rating === 5
              ? '¡Excelente!'
              : rating === 4
                ? 'Muy bueno'
                : rating === 3
                  ? 'Bueno'
                  : rating === 2
                    ? 'Regular'
                    : 'Necesita mejorar'}
          </Text>
        )}
      </Card>

      {/* Action buttons */}
      <View style={styles.actionButtons}>
        {!saved ? (
          <TouchableOpacity
            style={[styles.actionButton, styles.saveButton]}
            onPress={handleSaveConsultation}
          >
            <Text style={styles.saveIcon}>💾</Text>
            <Text style={styles.saveButtonText}>Guardar Consulta</Text>
          </TouchableOpacity>
        ) : (
          <View style={styles.savedIndicator}>
            <Text style={styles.savedIcon}>✅</Text>
            <Text style={styles.savedText}>Consulta Guardada</Text>
          </View>
        )}

        <TouchableOpacity style={styles.actionButton} onPress={handleSendWhatsApp}>
          <Text style={styles.actionIcon}>📱</Text>
          <Text style={styles.actionText}>Enviar por WhatsApp</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.actionButton} onPress={handleGeneratePDF}>
          <Text style={styles.actionIcon}>📄</Text>
          <Text style={styles.actionText}>Generar PDF</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.actionButton} onPress={handleScheduleNext}>
          <Text style={styles.actionIcon}>📅</Text>
          <Text style={styles.actionText}>Agendar Próxima Cita</Text>
        </TouchableOpacity>
      </View>

      {/* New consultation button */}
      <TouchableOpacity style={styles.newConsultationButton} onPress={handleNewConsultation}>
        <Text style={styles.newConsultationText}>Nueva Consulta</Text>
      </TouchableOpacity>

      <View style={styles.bottomSpacer} />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.surface,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.xl,
  },
  loadingText: {
    marginTop: SPACING.md,
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
  },
  // Success header
  successHeader: {
    alignItems: 'center',
    padding: SPACING.xl,
    paddingTop: SPACING.lg,
  },
  successIcon: {
    fontSize: 64,
    marginBottom: SPACING.md,
  },
  successTitle: {
    fontSize: TYPOGRAPHY.size['3xl'],
    fontFamily: TYPOGRAPHY.fontFamily.bold,
    fontWeight: '700',
    color: COLORS.text,
    marginBottom: SPACING.sm,
    letterSpacing: TYPOGRAPHY.letterSpacing.tight,
  },
  successSubtitle: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: TYPOGRAPHY.size.base * TYPOGRAPHY.lineHeight.normal,
  },
  // Summary cards
  summaryCard: {
    ...COMPONENTS.card.elevated,
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
  },
  financialCard: {
    ...COMPONENTS.card.elevated,
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
    backgroundColor: COLORS.accent,
  },
  sectionTitle: {
    fontSize: TYPOGRAPHY.size.lg,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: SPACING.md,
    letterSpacing: TYPOGRAPHY.letterSpacing.tight,
  },
  summarySection: {
    marginBottom: SPACING.lg,
  },
  summaryLabel: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
    marginBottom: SPACING.xs,
    letterSpacing: TYPOGRAPHY.letterSpacing.wide,
    textTransform: 'uppercase',
  },
  summaryValue: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.text,
  },
  summaryDetail: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
    marginTop: SPACING.xs,
  },
  formulaProduct: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
    marginTop: SPACING.xs,
  },
  // Financial
  priceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SPACING.sm,
  },
  priceLabel: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
  },
  priceValue: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.text,
  },
  totalPrice: {
    fontSize: TYPOGRAPHY.size.lg,
    fontFamily: TYPOGRAPHY.fontFamily.bold,
    fontWeight: '700',
    color: COLORS.primary,
  },
  profitValue: {
    color: COLORS.success,
  },
  divider: {
    height: 1,
    backgroundColor: COLORS.gray[100],
    marginVertical: SPACING.sm,
  },
  profitPercentage: {
    alignItems: 'center',
    marginTop: SPACING.sm,
    padding: SPACING.sm,
    backgroundColor: COLORS.success + '15',
    borderRadius: BORDER_RADIUS.lg,
    ...SHADOWS.sm,
  },
  profitText: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.success,
  },
  // Inventory section
  inventoryCard: {
    ...COMPONENTS.card.elevated,
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
  },
  inventoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  inventorySubtitle: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
    marginBottom: SPACING.md,
  },
  inventoryItem: {
    paddingVertical: SPACING.sm,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray[200],
  },
  inventoryItemInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.xs,
  },
  inventoryProductName: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
    flex: 1,
  },
  inventoryAmount: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.text,
  },
  stockStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.xs,
  },
  stockText: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
  },
  warningBox: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.sm,
    backgroundColor: COLORS.warning + '15',
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.lg,
    marginTop: SPACING.md,
    ...SHADOWS.sm,
  },
  warningText: {
    flex: 1,
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.warning,
  },
  // Expectations (for corrections)
  expectationsCard: {
    ...COMPONENTS.card.elevated,
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
    borderWidth: 2,
    borderColor: COLORS.info,
  },
  expectationsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.sm,
    marginBottom: SPACING.lg,
  },
  expectationsTitle: {
    fontSize: TYPOGRAPHY.size.lg,
    fontFamily: TYPOGRAPHY.fontFamily.bold,
    fontWeight: '700',
    color: COLORS.text,
  },
  expectationsList: {
    marginBottom: SPACING.lg,
  },
  expectationItem: {
    marginBottom: SPACING.md,
  },
  expectationLabel: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
    marginBottom: SPACING.xs,
  },
  expectationValue: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.medium,
    color: COLORS.text,
    fontWeight: '500',
    lineHeight: TYPOGRAPHY.size.base * TYPOGRAPHY.lineHeight.normal,
  },
  expectationNote: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
    fontStyle: 'italic',
    marginTop: SPACING.md,
    paddingTop: SPACING.md,
    borderTopWidth: 1,
    borderTopColor: COLORS.warning + '30',
    lineHeight: TYPOGRAPHY.size.sm * TYPOGRAPHY.lineHeight.relaxed,
  },
  maintenanceSection: {
    backgroundColor: COLORS.surface,
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.lg,
    marginBottom: SPACING.lg,
    ...SHADOWS.sm,
  },
  maintenanceTitle: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: SPACING.sm,
  },
  maintenanceText: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
    lineHeight: TYPOGRAPHY.size.sm * TYPOGRAPHY.lineHeight.relaxed,
  },
  nextSessionInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.sm,
    backgroundColor: COLORS.warning + '15',
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.lg,
    ...SHADOWS.sm,
  },
  nextSessionText: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.medium,
    color: COLORS.warning,
    fontWeight: '500',
  },
  // Rating
  ratingCard: {
    ...COMPONENTS.card.base,
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
    alignItems: 'center',
  },
  ratingStars: {
    flexDirection: 'row',
    gap: SPACING.sm,
    marginBottom: SPACING.md,
  },
  star: {
    fontSize: 36,
    color: COLORS.gray[400],
  },
  starFilled: {
    color: COLORS.warning,
  },
  ratingText: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.medium,
    color: COLORS.textSecondary,
    fontWeight: '500',
  },
  // Action buttons
  actionButtons: {
    paddingHorizontal: SPACING.lg,
    gap: SPACING.md,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.white,
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.xl,
    borderWidth: 1,
    borderColor: COLORS.gray[200],
    gap: SPACING.sm,
    ...SHADOWS.md,
  },
  saveButton: {
    backgroundColor: COLORS.primary,
    borderColor: COLORS.primary,
  },
  actionIcon: {
    fontSize: 20,
  },
  actionText: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.medium,
    fontWeight: '500',
    color: COLORS.text,
  },
  saveIcon: {
    fontSize: 20,
  },
  saveButtonText: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.white,
  },
  savedIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.success + '15',
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.xl,
    gap: SPACING.sm,
    ...SHADOWS.md,
  },
  savedIcon: {
    fontSize: 20,
  },
  savedText: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.success,
  },
  // New consultation
  newConsultationButton: {
    marginHorizontal: SPACING.lg,
    marginTop: SPACING.xl,
    backgroundColor: COLORS.surface,
    padding: SPACING.lg,
    borderRadius: BORDER_RADIUS.xl,
    alignItems: 'center',
    ...SHADOWS.md,
  },
  newConsultationText: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.textSecondary,
  },
  bottomSpacer: {
    height: SPACING.xl,
  },
  // Actual result styles
  resultCard: {
    ...COMPONENTS.card.elevated,
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
    backgroundColor: COLORS.primary + '08',
  },
  resultSection: {
    marginBottom: SPACING.lg,
  },
  resultLabel: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.medium,
    color: COLORS.textSecondary,
    marginBottom: SPACING.sm,
    fontWeight: '500',
    letterSpacing: TYPOGRAPHY.letterSpacing.wide,
    textTransform: 'uppercase',
  },
  sliderContainer: {
    paddingHorizontal: SPACING.sm,
  },
  slider: {
    width: '100%',
    height: 40,
  },
  levelText: {
    fontSize: TYPOGRAPHY.size.xl,
    fontFamily: TYPOGRAPHY.fontFamily.bold,
    fontWeight: '700',
    color: COLORS.primary,
    textAlign: 'center',
    marginBottom: SPACING.xs,
  },
  levelName: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginTop: SPACING.xs,
  },
  toneInput: {
    ...COMPONENTS.input.base,
    borderWidth: 1,
    borderColor: COLORS.gray[200],
  },
  satisfactionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: SPACING.xs,
  },
  satisfactionButton: {
    flex: 1,
    alignItems: 'center',
    padding: SPACING.sm,
    borderRadius: BORDER_RADIUS.lg,
    backgroundColor: COLORS.surface,
    ...SHADOWS.sm,
  },
  satisfactionButtonActive: {
    backgroundColor: COLORS.primary + '15',
    borderWidth: 2,
    borderColor: COLORS.primary,
    ...SHADOWS.md,
  },
  satisfactionEmoji: {
    fontSize: 24,
    marginBottom: SPACING.xs,
  },
  satisfactionEmojiActive: {
    fontSize: 28,
  },
  satisfactionText: {
    fontSize: 10,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
  satisfactionTextActive: {
    color: COLORS.primary,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
  },
  notesInput: {
    ...COMPONENTS.input.base,
    borderWidth: 1,
    borderColor: COLORS.gray[200],
    minHeight: 80,
    textAlignVertical: 'top',
  },
  comparisonSection: {
    backgroundColor: COLORS.surface,
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.lg,
    marginTop: SPACING.md,
    ...SHADOWS.sm,
  },
  comparisonHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.sm,
    marginBottom: SPACING.sm,
  },
  comparisonTitle: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.text,
  },
  comparisonDetails: {
    gap: SPACING.xs,
  },
  comparisonText: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
  },
});
