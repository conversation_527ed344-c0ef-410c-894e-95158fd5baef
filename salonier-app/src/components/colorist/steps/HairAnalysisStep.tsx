import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  // Dimensions,
  TextInput,
  Switch,
} from 'react-native';
import Toast from 'react-native-toast-message';
import { COLORS, SPACING, FONT_SIZES, BORDER_RADIUS } from '../../../constants';
import { TYPOGRAPHY, SHADOWS, COMPONENTS } from '../../../constants/design-system';
import { useConsultation } from '../../../hooks/useConsultation';
import { HairAnalysis, Consultation } from '../../../types';
import Card from '../../common/Card';
import SimplePhotoAnalysis from '../SimplePhotoAnalysis';
import { PhotoAnalysisData } from '../../../hooks/usePhotoAnalysis';
import { dataService } from '../../../services/dataService';
import Icon from 'react-native-vector-icons/Ionicons';
import LevelPicker from '../LevelPicker';

// const { width: screenWidth } = Dimensions.get('window');

interface AnalysisState {
  natural_level: number;
  undertone: 'cool' | 'neutral' | 'warm';
  gray_percentage: number;
  porosity: 'low' | 'medium' | 'high';
  density: 'thin' | 'medium' | 'thick';
  condition: 'damaged' | 'healthy' | 'very_healthy';
  previous_treatments: string[];
  // Nuevos campos para análisis mejorado
  underlying_pigment?: 'red' | 'red-orange' | 'orange' | 'yellow' | 'pale-yellow';
  white_hair_distribution?: 'none' | 'scattered' | 'concentrated' | 'patches';
}

export default function HairAnalysisStep() {
  const { state, setHairAnalysis } = useConsultation();
  const [analysis, setAnalysis] = useState<AnalysisState>({
    natural_level: 6,
    undertone: 'neutral',
    gray_percentage: 0,
    porosity: 'medium',
    density: 'medium',
    condition: 'healthy',
    previous_treatments: [],
  });

  // Estado para corrección de color
  const [correctionData, setCorrectionData] = useState({
    lastColorDate: '',
    productsUsed: '',
    bleachingDone: false,
  });

  const [previousConsultation, setPreviousConsultation] = useState<Consultation | null>(null);
  const [loadingHistory, setLoadingHistory] = useState(false);

  const isCorrection = state.technique === 'correction';

  useEffect(() => {
    // Si ya existe un análisis previo, cargar esos datos
    if (state.hairAnalysis) {
      setAnalysis({
        natural_level: state.hairAnalysis.natural_level,
        undertone: state.hairAnalysis.undertone as 'cool' | 'neutral' | 'warm',
        gray_percentage: state.hairAnalysis.gray_percentage,
        porosity: state.hairAnalysis.porosity as 'low' | 'medium' | 'high',
        density: state.hairAnalysis.density as 'thin' | 'medium' | 'thick',
        condition: state.hairAnalysis.condition as 'damaged' | 'healthy' | 'very_healthy',
        previous_treatments: state.hairAnalysis.previous_treatments || [],
      });
    }
  }, [state.hairAnalysis]);

  // Cargar historial del cliente al montar el componente
  useEffect(() => {
    loadClientHistory();
  }, [state.client?.id]);

  const loadClientHistory = async () => {
    if (!state.client?.id) return;

    setLoadingHistory(true);
    try {
      const lastConsultation = await dataService.consultations.getLastByClient(state.client.id);
      if (lastConsultation) {
        setPreviousConsultation(lastConsultation);

        // Si hay historial y no hay análisis actual, pre-llenar con datos anteriores
        if (!state.hairAnalysis && lastConsultation.current_hair_analysis) {
          const prevAnalysis = lastConsultation.current_hair_analysis;
          setAnalysis({
            natural_level: prevAnalysis.natural_level,
            undertone: prevAnalysis.undertone as 'cool' | 'neutral' | 'warm',
            gray_percentage: prevAnalysis.gray_percentage,
            porosity: prevAnalysis.porosity as 'low' | 'medium' | 'high',
            density: prevAnalysis.density as 'thin' | 'medium' | 'thick',
            condition: prevAnalysis.condition as 'damaged' | 'healthy' | 'very_healthy',
            previous_treatments: prevAnalysis.previous_treatments || [],
          });

          // Mostrar alerta de que se cargaron datos previos
          Toast.show({
            type: 'success',
            text1: 'Historial Cargado',
            text2: `Datos de la última consulta (${new Date(lastConsultation.created_at).toLocaleDateString()})`,
            position: 'top',
          });
        }
      }
    } catch (error) {
      console.error('Error loading client history:', error);
    } finally {
      setLoadingHistory(false);
    }
  };

  const handlePhotoAnalysisComplete = (data: PhotoAnalysisData) => {
    let hairAnalysis: HairAnalysis;

    if (data.customizeByZones && data.zoneAnalysis) {
      // Use average values from zones
      const avgLevel = Math.round(
        (data.zoneAnalysis.roots.level +
          data.zoneAnalysis.mids.level +
          data.zoneAnalysis.ends.level) /
          3
      );
      const avgGrayPercentage = Math.round(
        ((data.zoneAnalysis.roots.grayPercentage || 0) +
          (data.zoneAnalysis.mids.grayPercentage || 0) +
          (data.zoneAnalysis.ends.grayPercentage || 0)) /
          3
      );

      // Use most common porosity and condition
      const porosities = [
        data.zoneAnalysis.roots.porosity,
        data.zoneAnalysis.mids.porosity,
        data.zoneAnalysis.ends.porosity,
      ];
      const conditions = [
        data.zoneAnalysis.roots.condition,
        data.zoneAnalysis.mids.condition,
        data.zoneAnalysis.ends.condition,
      ];

      const mostCommonPorosity =
        porosities
          .sort(
            (a, b) =>
              porosities.filter(v => v === a).length - porosities.filter(v => v === b).length
          )
          .pop() || 'medium';

      const mostCommonCondition =
        conditions
          .sort(
            (a, b) =>
              conditions.filter(v => v === a).length - conditions.filter(v => v === b).length
          )
          .pop() || 'healthy';

      hairAnalysis = {
        id: Date.now().toString(),
        consultation_id: state.consultationId || '',
        natural_level: avgLevel,
        undertone: analysis.undertone,
        gray_percentage: avgGrayPercentage,
        porosity: mostCommonPorosity,
        density: analysis.density,
        condition: mostCommonCondition,
        previous_treatments: analysis.previous_treatments,
        zone_analysis: {
          roots: {
            level: data.zoneAnalysis.roots.level,
            porosity: data.zoneAnalysis.roots.porosity,
            condition: data.zoneAnalysis.roots.condition,
            grayPercentage: data.zoneAnalysis.roots.grayPercentage || 0,
          },
          mids: {
            level: data.zoneAnalysis.mids.level,
            porosity: data.zoneAnalysis.mids.porosity,
            condition: data.zoneAnalysis.mids.condition,
            grayPercentage: data.zoneAnalysis.mids.grayPercentage || 0,
          },
          ends: {
            level: data.zoneAnalysis.ends.level,
            porosity: data.zoneAnalysis.ends.porosity,
            condition: data.zoneAnalysis.ends.condition,
            grayPercentage: data.zoneAnalysis.ends.grayPercentage || 0,
          },
        },
        created_at: new Date().toISOString(),
      };
    } else {
      // Use single values
      const level = data.zoneAnalysis?.roots.level || analysis.natural_level;
      const grayPercentage = data.zoneAnalysis?.roots.grayPercentage || analysis.gray_percentage;
      const porosity = data.zoneAnalysis?.roots.porosity || analysis.porosity;
      const condition = data.zoneAnalysis?.roots.condition || analysis.condition;

      hairAnalysis = {
        id: Date.now().toString(),
        consultation_id: state.consultationId || '',
        natural_level: level,
        undertone: analysis.undertone,
        gray_percentage: grayPercentage,
        porosity: porosity,
        density: analysis.density,
        condition: condition,
        previous_treatments: analysis.previous_treatments,
        created_at: new Date().toISOString(),
        // Incluir nuevos campos
        underlying_pigment: analysis.underlying_pigment,
        white_hair_distribution: analysis.white_hair_distribution,
      };
    }

    // Si es corrección, añadir los datos adicionales al análisis
    if (isCorrection) {
      (hairAnalysis as any).correctionData = correctionData;
    }

    setHairAnalysis(hairAnalysis);
  };

  const toggleTreatment = (treatment: string) => {
    setAnalysis(prev => ({
      ...prev,
      previous_treatments: prev.previous_treatments.includes(treatment)
        ? prev.previous_treatments.filter(t => t !== treatment)
        : [...prev.previous_treatments, treatment],
    }));
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <SimplePhotoAnalysis
        mode="current"
        title={isCorrection ? '🔧 Análisis del Problema' : 'Análisis del Cabello'}
        subtitle={
          isCorrection
            ? 'Captura o describe el problema de color actual'
            : 'Captura o selecciona el estado actual del cabello'
        }
        onComplete={handlePhotoAnalysisComplete}
        initialData={{
          mode: state.hairAnalysis?.zone_analysis ? 'photo' : 'manual',
          customizeByZones: !!state.hairAnalysis?.zone_analysis,
          zoneAnalysis: state.hairAnalysis?.zone_analysis || {
            roots: {
              level: state.hairAnalysis?.natural_level || 6,
              porosity: state.hairAnalysis?.porosity || 'medium',
              condition: state.hairAnalysis?.condition || 'healthy',
              grayPercentage: state.hairAnalysis?.gray_percentage || 0,
            },
            mids: { level: 6, porosity: 'medium', condition: 'healthy', grayPercentage: 0 },
            ends: { level: 7, porosity: 'medium', condition: 'healthy', grayPercentage: 0 },
          },
        }}
      />

      {/* Historial del Cliente */}
      {previousConsultation && (
        <Card style={[styles.analysisCard, styles.historyCard]} elevation="medium">
          <View style={styles.historyHeader}>
            <Icon name="time-outline" size={20} color={COLORS.warning} />
            <Text style={styles.historyTitle}>Historial de Color</Text>
          </View>

          <View style={styles.historyContent}>
            <View style={styles.historyRow}>
              <Text style={styles.historyLabel}>Última coloración:</Text>
              <Text style={styles.historyValue}>
                {new Date(previousConsultation.created_at).toLocaleDateString()}
              </Text>
            </View>

            {previousConsultation.formulation && (
              <View style={styles.historyRow}>
                <Text style={styles.historyLabel}>Fórmula anterior:</Text>
                <Text style={styles.historyValue}>
                  {previousConsultation.formulation.formula?.products[0]?.name || 'No disponible'}
                </Text>
              </View>
            )}

            {previousConsultation.actual_result && (
              <View style={styles.historyRow}>
                <Text style={styles.historyLabel}>Resultado obtenido:</Text>
                <Text style={styles.historyValue}>
                  Nivel {previousConsultation.actual_result.achieved_level} -{' '}
                  {previousConsultation.actual_result.achieved_tone}
                </Text>
              </View>
            )}

            {previousConsultation.follow_up_notes && (
              <View style={styles.historyNote}>
                <Text style={styles.historyNoteLabel}>Notas para próxima sesión:</Text>
                <Text style={styles.historyNoteText}>{previousConsultation.follow_up_notes}</Text>
              </View>
            )}
          </View>

          {state.client?.hair_characteristics?.ammonia_sensitive && (
            <View style={styles.warningBox}>
              <Icon name="alert-circle" size={16} color={COLORS.error} />
              <Text style={styles.warningText}>Cliente sensible a amoníaco</Text>
            </View>
          )}
        </Card>
      )}

      {/* Nivel con decimales usando LevelPicker */}
      <Card style={styles.analysisCard} elevation="medium">
        <LevelPicker
          value={analysis.natural_level}
          onChange={value => setAnalysis(prev => ({ ...prev, natural_level: value }))}
          label="Nivel Natural del Cabello"
        />
      </Card>

      {/* Additional Analysis Parameters */}
      <Card style={styles.analysisCard} elevation="medium">
        <Text style={styles.parameterTitle}>Subtono</Text>
        <View style={styles.undertoneOptions}>
          {(['cool', 'neutral', 'warm'] as const).map(tone => (
            <TouchableOpacity
              key={tone}
              style={[
                styles.undertoneOption,
                analysis.undertone === tone && styles.undertoneSelected,
              ]}
              onPress={() => setAnalysis(prev => ({ ...prev, undertone: tone }))}
            >
              <View
                style={[
                  styles.undertoneSample,
                  {
                    backgroundColor:
                      tone === 'cool' ? '#E6E6FA' : tone === 'warm' ? '#FFE4B5' : '#F5F5DC',
                  },
                ]}
              />
              <Text
                style={[
                  styles.undertoneText,
                  analysis.undertone === tone && styles.undertoneTextSelected,
                ]}
              >
                {tone === 'cool' ? 'Frío' : tone === 'warm' ? 'Cálido' : 'Neutro'}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </Card>

      {/* Pigmento Subyacente - Nuevo campo */}
      <Card style={styles.analysisCard} elevation="medium">
        <Text style={styles.parameterTitle}>Pigmento Subyacente</Text>
        <Text style={styles.parameterSubtitle}>Determina el fondo de aclaración natural</Text>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.pigmentOptions}
        >
          {[
            { id: 'red', label: 'Rojo', color: '#8B0000' },
            { id: 'red-orange', label: 'Rojo-Naranja', color: '#FF4500' },
            { id: 'orange', label: 'Naranja', color: '#FFA500' },
            { id: 'yellow', label: 'Amarillo', color: '#FFD700' },
            { id: 'pale-yellow', label: 'Amarillo Pálido', color: '#FFFFE0' },
          ].map(pigment => (
            <TouchableOpacity
              key={pigment.id}
              style={[
                styles.pigmentOption,
                analysis.underlying_pigment === pigment.id && styles.pigmentSelected,
              ]}
              onPress={() =>
                setAnalysis(prev => ({ ...prev, underlying_pigment: pigment.id as any }))
              }
            >
              <View style={[styles.pigmentSample, { backgroundColor: pigment.color }]} />
              <Text
                style={[
                  styles.pigmentText,
                  analysis.underlying_pigment === pigment.id && styles.pigmentTextSelected,
                ]}
              >
                {pigment.label}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </Card>

      {/* Distribución de Canas - Nuevo campo */}
      <Card style={styles.analysisCard} elevation="medium">
        <Text style={styles.parameterTitle}>Distribución de Canas</Text>
        <View style={styles.distributionOptions}>
          {[
            { id: 'none', label: 'Sin canas', icon: '✓' },
            { id: 'scattered', label: 'Dispersas', icon: '···' },
            { id: 'concentrated', label: 'Concentradas', icon: '•••' },
            { id: 'patches', label: 'Por zonas', icon: '■□' },
          ].map(dist => (
            <TouchableOpacity
              key={dist.id}
              style={[
                styles.distributionOption,
                analysis.white_hair_distribution === dist.id && styles.distributionSelected,
              ]}
              onPress={() =>
                setAnalysis(prev => ({ ...prev, white_hair_distribution: dist.id as any }))
              }
            >
              <Text style={styles.distributionIcon}>{dist.icon}</Text>
              <Text
                style={[
                  styles.distributionText,
                  analysis.white_hair_distribution === dist.id && styles.distributionTextSelected,
                ]}
              >
                {dist.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </Card>

      {/* Previous Treatments */}
      <Card style={styles.analysisCard} elevation="medium">
        <Text style={styles.parameterTitle}>Tratamientos Previos</Text>
        <View style={styles.treatmentsGrid}>
          {[
            'Coloración reciente',
            'Decoloración',
            'Permanente',
            'Alisado químico',
            'Keratin',
            'Botox capilar',
          ].map(treatment => (
            <TouchableOpacity
              key={treatment}
              style={[
                styles.treatmentChip,
                analysis.previous_treatments.includes(treatment) && styles.treatmentChipSelected,
              ]}
              onPress={() => toggleTreatment(treatment)}
            >
              <Text
                style={[
                  styles.treatmentText,
                  analysis.previous_treatments.includes(treatment) && styles.treatmentTextSelected,
                ]}
              >
                {treatment}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </Card>

      {/* Additional Details for Corrections */}
      {isCorrection && (
        <Card style={styles.correctionCard} elevation="medium">
          <Text style={styles.correctionTitle}>📋 Detalles del Proceso Anterior</Text>
          <Text style={styles.correctionSubtitle}>Información para planificar la corrección</Text>

          <Text style={styles.parameterTitle}>¿Cuándo se hizo el último color?</Text>
          <View style={styles.dateOptions}>
            {['Hoy', 'Ayer', '1 semana', '2 semanas', '1 mes', 'Más de 1 mes'].map(date => (
              <TouchableOpacity
                key={date}
                style={[
                  styles.dateChip,
                  correctionData.lastColorDate === date && styles.dateChipSelected,
                ]}
                onPress={() => setCorrectionData(prev => ({ ...prev, lastColorDate: date }))}
              >
                <Text
                  style={[
                    styles.dateText,
                    correctionData.lastColorDate === date && styles.dateTextSelected,
                  ]}
                >
                  {date}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          <Text style={styles.parameterTitle}>¿Qué productos se usaron?</Text>
          <View style={styles.inputContainer}>
            <TextInput
              style={styles.textInput}
              placeholder="Marca, producto y volumen de oxidante"
              value={correctionData.productsUsed}
              onChangeText={text => setCorrectionData(prev => ({ ...prev, productsUsed: text }))}
            />
          </View>

          <View style={styles.switchContainer}>
            <Text style={styles.switchLabel}>¿Se hizo decoloración?</Text>
            <Switch
              value={correctionData.bleachingDone}
              onValueChange={value =>
                setCorrectionData(prev => ({ ...prev, bleachingDone: value }))
              }
              trackColor={{ false: COLORS.gray[300], true: COLORS.primary }}
              thumbColor={correctionData.bleachingDone ? COLORS.white : COLORS.gray[100]}
            />
          </View>
        </Card>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  analysisCard: {
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
    ...COMPONENTS.card.elevated,
  },
  parameterTitle: {
    fontSize: TYPOGRAPHY.size.lg,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    color: COLORS.text,
    marginBottom: SPACING.md,
  },
  // Undertone
  undertoneOptions: {
    flexDirection: 'row',
    gap: SPACING.md,
  },
  undertoneOption: {
    flex: 1,
    alignItems: 'center',
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.lg,
    backgroundColor: COLORS.surface,
    ...SHADOWS.sm,
  },
  undertoneSelected: {
    backgroundColor: COLORS.primary,
    ...SHADOWS.md,
  },
  undertoneSample: {
    width: 40,
    height: 40,
    borderRadius: BORDER_RADIUS.full,
    marginBottom: SPACING.sm,
  },
  undertoneText: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.medium,
    color: COLORS.gray[600],
  },
  undertoneTextSelected: {
    color: COLORS.white,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
  },
  // Treatments
  treatmentsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.sm,
  },
  treatmentChip: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.full,
    backgroundColor: COLORS.surface,
    ...SHADOWS.sm,
  },
  treatmentChipSelected: {
    backgroundColor: COLORS.secondary,
    ...SHADOWS.md,
  },
  treatmentText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[700],
  },
  treatmentTextSelected: {
    color: COLORS.white,
  },

  // Estilos para corrección
  correctionCard: {
    marginTop: SPACING.lg,
    marginHorizontal: SPACING.lg,
    padding: SPACING.lg,
    backgroundColor: COLORS.surface,
    ...SHADOWS.lg,
    borderLeftWidth: 4,
    borderLeftColor: COLORS.warning,
  },
  correctionHeader: {
    marginBottom: SPACING.lg,
  },
  correctionTitle: {
    fontSize: FONT_SIZES.xl,
    fontWeight: 'bold',
    color: COLORS.gray[900],
    marginBottom: SPACING.xs,
  },
  correctionSubtitle: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
  },
  problemGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.sm,
    marginBottom: SPACING.lg,
  },
  problemOption: {
    flex: 1,
    minWidth: '45%',
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.lg,
    borderWidth: 2,
    borderColor: COLORS.gray[300],
    backgroundColor: COLORS.white,
    alignItems: 'center',
    minHeight: 100,
    justifyContent: 'center',
  },
  problemSelected: {
    borderColor: COLORS.warning,
    backgroundColor: COLORS.warning + '20',
  },
  problemEmoji: {
    fontSize: 24,
    marginBottom: SPACING.xs,
  },
  problemText: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.gray[700],
    textAlign: 'center',
  },
  problemTextSelected: {
    color: COLORS.warning,
    fontWeight: 'bold',
  },
  inputContainer: {
    marginBottom: SPACING.lg,
  },
  textInput: {
    ...COMPONENTS.input.base,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
  },
  dateOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.sm,
    marginBottom: SPACING.lg,
  },
  dateChip: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.full,
    backgroundColor: COLORS.surface,
    ...SHADOWS.sm,
  },
  dateChipSelected: {
    backgroundColor: COLORS.warning,
    ...SHADOWS.md,
  },
  dateText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[700],
  },
  dateTextSelected: {
    color: COLORS.white,
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.md,
  },
  switchLabel: {
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[700],
  },
  problemDescription: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.gray[600],
    textAlign: 'center',
    marginTop: SPACING.xs,
    paddingHorizontal: SPACING.xs,
  },
  // History styles
  historyCard: {
    backgroundColor: COLORS.surface,
    borderWidth: 0,
    ...SHADOWS.md,
  },
  historyHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.md,
    gap: SPACING.sm,
  },
  historyTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: '600',
    color: COLORS.gray[900],
  },
  historyContent: {
    gap: SPACING.sm,
  },
  historyRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: SPACING.xs,
  },
  historyLabel: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
  },
  historyValue: {
    fontSize: FONT_SIZES.sm,
    fontWeight: '600',
    color: COLORS.gray[800],
  },
  historyNote: {
    marginTop: SPACING.sm,
    padding: SPACING.sm,
    backgroundColor: COLORS.white,
    borderRadius: BORDER_RADIUS.sm,
  },
  historyNoteLabel: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.gray[600],
    marginBottom: SPACING.xs,
  },
  historyNoteText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[800],
    fontStyle: 'italic',
  },
  warningBox: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.xs,
    marginTop: SPACING.md,
    padding: SPACING.sm,
    backgroundColor: COLORS.error + '10',
    borderRadius: BORDER_RADIUS.sm,
  },
  warningText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.error,
    fontWeight: '600',
  },
  parameterSubtitle: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.gray[600],
    marginBottom: SPACING.md,
  },
  // Pigment styles
  pigmentOptions: {
    flexDirection: 'row',
    gap: SPACING.md,
    paddingVertical: SPACING.sm,
  },
  pigmentOption: {
    alignItems: 'center',
    padding: SPACING.sm,
    borderRadius: BORDER_RADIUS.lg,
    backgroundColor: COLORS.surface,
    minWidth: 90,
    ...SHADOWS.sm,
  },
  pigmentSelected: {
    backgroundColor: COLORS.primary,
    ...SHADOWS.md,
  },
  pigmentSample: {
    width: 50,
    height: 50,
    borderRadius: BORDER_RADIUS.full,
    marginBottom: SPACING.sm,
    borderWidth: 2,
    borderColor: COLORS.white,
    ...SHADOWS.sm,
  },
  pigmentText: {
    fontSize: TYPOGRAPHY.size.xs,
    fontFamily: TYPOGRAPHY.fontFamily.medium,
    color: COLORS.gray[700],
    textAlign: 'center',
  },
  pigmentTextSelected: {
    color: COLORS.white,
  },
  // Distribution styles
  distributionOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.sm,
  },
  distributionOption: {
    flex: 1,
    minWidth: '45%',
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.sm,
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.lg,
    backgroundColor: COLORS.surface,
    ...SHADOWS.sm,
  },
  distributionSelected: {
    backgroundColor: COLORS.secondary,
    ...SHADOWS.md,
  },
  distributionIcon: {
    fontSize: FONT_SIZES.lg,
    color: COLORS.gray[600],
  },
  distributionText: {
    fontSize: FONT_SIZES.sm,
    fontFamily: TYPOGRAPHY.fontFamily.medium,
    color: COLORS.gray[700],
    flex: 1,
  },
  distributionTextSelected: {
    color: COLORS.white,
  },
});
