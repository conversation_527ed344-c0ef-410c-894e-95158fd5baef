import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { COLORS, SPACING, BORDER_RADIUS } from '../../../constants';
import { TYPOGRAPHY, COMPONENTS } from '../../../constants/design-system';
import { useSimpleConsultation } from '../../../hooks/useSimpleConsultation';
import Card from '../../common/Card';
import { Icon } from '../../common/Icon';
import BigActionButton from '../../common/BigActionButton';
import Toast from 'react-native-toast-message';

export default function ApplyAndSave() {
  const navigation = useNavigation<any>();
  const { state, saveConsultation, resetConsultation } = useSimpleConsultation();
  const [notes, setNotes] = useState('');
  const [saving, setSaving] = useState(false);
  const [saved, setSaved] = useState(false);

  const handleSave = async () => {
    setSaving(true);
    
    try {
      await saveConsultation();
      setSaved(true);
      
      // Success feedback
      Toast.show({
        type: 'success',
        text1: '✅ Consulta Guardada',
        text2: `Cliente: ${state.client?.full_name || state.client?.name || 'Sin nombre'}`,
        position: 'top',
      });
      
      // Navigate back after a short delay
      setTimeout(() => {
        resetConsultation();
        navigation.navigate('Dashboard');
      }, 2000);
    } catch (error) {
      setSaving(false);
      Toast.show({
        type: 'error',
        text1: 'Error al guardar',
        text2: 'Intenta nuevamente',
        position: 'top',
      });
    }
  };

  const getProcessingTime = () => {
    return state.formulation?.processing_time || 30;
  };

  const getFormulaSummary = () => {
    if (!state.formulation) return '';
    const mainProduct = state.formulation.formula.products[0];
    const additionalCount = state.formulation.formula.products.length - 1;
    return `${mainProduct.name}${additionalCount > 0 ? ` + ${additionalCount} más` : ''}`;
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Summary Card */}
      <Card style={styles.summaryCard} elevation="md">
        <Text style={styles.summaryTitle}>📋 Resumen de la Consulta</Text>
        
        <View style={styles.summaryRow}>
          <Icon name="person" size={20} color={COLORS.gray[600]} />
          <Text style={styles.summaryLabel}>Cliente:</Text>
          <Text style={styles.summaryValue}>{state.client?.full_name || state.client?.name || 'Sin nombre'}</Text>
        </View>
        
        <View style={styles.summaryRow}>
          <Icon name="color-palette" size={20} color={COLORS.gray[600]} />
          <Text style={styles.summaryLabel}>Fórmula:</Text>
          <Text style={styles.summaryValue}>{getFormulaSummary()}</Text>
        </View>
        
        <View style={styles.summaryRow}>
          <Icon name="time" size={20} color={COLORS.gray[600]} />
          <Text style={styles.summaryLabel}>Tiempo:</Text>
          <Text style={styles.summaryValue}>{getProcessingTime()} min</Text>
        </View>
        
        {state.isCorrection && (
          <View style={styles.correctionBadge}>
            <Icon name="construct" size={16} color={COLORS.warning} />
            <Text style={styles.correctionText}>Corrección de color</Text>
          </View>
        )}
      </Card>

      {/* Timer Card */}
      <Card style={styles.timerCard} elevation="sm">
        <View style={styles.timerHeader}>
          <Text style={styles.timerTitle}>⏱️ Temporizador</Text>
          <Text style={styles.timerSubtitle}>Toca para iniciar cuando apliques</Text>
        </View>
        
        <TouchableOpacity style={styles.timerButton} activeOpacity={0.8}>
          <Text style={styles.timerDisplay}>{getProcessingTime()}:00</Text>
          <Text style={styles.timerAction}>INICIAR</Text>
        </TouchableOpacity>
      </Card>

      {/* Notes */}
      <Card style={styles.notesCard} elevation="sm">
        <Text style={styles.notesTitle}>📝 Notas (opcional)</Text>
        <TextInput
          style={styles.notesInput}
          placeholder="Observaciones del proceso o resultado..."
          placeholderTextColor={COLORS.gray[400]}
          value={notes}
          onChangeText={setNotes}
          multiline
          numberOfLines={3}
          textAlignVertical="top"
        />
      </Card>

      {/* Quick Actions */}
      <View style={styles.quickActions}>
        <TouchableOpacity style={styles.quickAction}>
          <Icon name="camera" size={24} color={COLORS.secondary} />
          <Text style={styles.quickActionText}>Foto resultado</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.quickAction}>
          <Icon name="calendar" size={24} color={COLORS.secondary} />
          <Text style={styles.quickActionText}>Agendar retoque</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.quickAction}>
          <Icon name="share-social" size={24} color={COLORS.secondary} />
          <Text style={styles.quickActionText}>Compartir</Text>
        </TouchableOpacity>
      </View>

      {/* Save Button */}
      <View style={styles.saveContainer}>
        {saved ? (
          <View style={styles.savedMessage}>
            <Icon name="checkmark-circle" size={48} color={COLORS.success} />
            <Text style={styles.savedText}>¡Consulta guardada!</Text>
            <Text style={styles.savedSubtext}>Regresando al inicio...</Text>
          </View>
        ) : (
          <BigActionButton
            title="Guardar Consulta"
            subtitle="Finalizar y guardar en el historial"
            onPress={handleSave}
            disabled={saving}
            variant="success"
            emoji="💾"
          />
        )}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  summaryCard: {
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
  },
  summaryTitle: {
    fontSize: TYPOGRAPHY.size.lg,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: SPACING.lg,
  },
  summaryRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.sm,
    paddingVertical: SPACING.sm,
  },
  summaryLabel: {
    flex: 1,
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
  },
  summaryValue: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.text,
  },
  correctionBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.xs,
    marginTop: SPACING.md,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    backgroundColor: COLORS.warning + '20',
    borderRadius: BORDER_RADIUS.full,
    alignSelf: 'flex-start',
  },
  correctionText: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.medium,
    color: COLORS.warning,
  },
  timerCard: {
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
  },
  timerHeader: {
    marginBottom: SPACING.lg,
  },
  timerTitle: {
    fontSize: TYPOGRAPHY.size.lg,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: SPACING.xs,
  },
  timerSubtitle: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
  },
  timerButton: {
    alignItems: 'center',
    padding: SPACING.xl,
    backgroundColor: COLORS.primary + '10',
    borderRadius: BORDER_RADIUS.xl,
    borderWidth: 2,
    borderColor: COLORS.primary,
  },
  timerDisplay: {
    fontSize: 48,
    fontFamily: TYPOGRAPHY.fontFamily.bold,
    fontWeight: '700',
    color: COLORS.primary,
    marginBottom: SPACING.sm,
  },
  timerAction: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.primary,
    letterSpacing: TYPOGRAPHY.letterSpacing.wide,
  },
  notesCard: {
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
  },
  notesTitle: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.medium,
    color: COLORS.text,
    marginBottom: SPACING.md,
  },
  notesInput: {
    ...COMPONENTS.input.base,
    minHeight: 80,
    paddingTop: SPACING.md,
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.xl,
  },
  quickAction: {
    alignItems: 'center',
    gap: SPACING.xs,
  },
  quickActionText: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
  },
  saveContainer: {
    paddingHorizontal: SPACING.lg,
    paddingBottom: SPACING.xl,
  },
  savedMessage: {
    alignItems: 'center',
    padding: SPACING.xl,
  },
  savedText: {
    fontSize: TYPOGRAPHY.size.xl,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.success,
    marginTop: SPACING.md,
    marginBottom: SPACING.xs,
  },
  savedSubtext: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
  },
});