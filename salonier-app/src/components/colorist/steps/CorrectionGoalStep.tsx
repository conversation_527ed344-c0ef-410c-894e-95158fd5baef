import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
} from 'react-native';
import { COLORS, SPACING, FONT_SIZES, BORDER_RADIUS } from '../../../constants';
import { useConsultation } from '../../../hooks/useConsultation';
import Card from '../../common/Card';
import { MaterialCommunityIcons } from '@expo/vector-icons';

interface CorrectionGoal {
  problemType: 'orange' | 'green' | 'yellow' | 'uneven' | 'too_dark' | 'too_light' | 'bands' | 'spots';
  targetLevel?: number;
  targetTone?: string;
  priority: 'neutralize' | 'lighten' | 'darken' | 'even_out';
  clientExpectation: string;
  professionalAssessment: string;
}

const PROBLEM_TYPES = [
  {
    id: 'orange',
    label: 'Tonos Naranjas',
    icon: 'circle',
    color: '#FF6B35',
    solutions: ['Neutralización con base azul', 'Matización con violeta'],
  },
  {
    id: 'green',
    label: 'Tonos Verdes',
    icon: 'circle',
    color: '#4CAF50',
    solutions: ['Neutralización con rojo-cobre', 'Pre-pigmentación'],
  },
  {
    id: 'yellow',
    label: 'Tonos Amarillos',
    icon: 'circle',
    color: '#FFD93D',
    solutions: ['Matización con violeta', 'Tono ceniza'],
  },
  {
    id: 'uneven',
    label: 'Color Disparejo',
    icon: 'texture-box',
    color: '#9E9E9E',
    solutions: ['Igualación de color', 'Técnica de empalme'],
  },
  {
    id: 'too_dark',
    label: 'Demasiado Oscuro',
    icon: 'brightness-3',
    color: '#424242',
    solutions: ['Decoloración suave', 'Barrido de color'],
  },
  {
    id: 'too_light',
    label: 'Demasiado Claro',
    icon: 'brightness-7',
    color: '#EEEEEE',
    solutions: ['Re-pigmentación', 'Relleno de color'],
  },
  {
    id: 'bands',
    label: 'Bandas/Líneas',
    icon: 'view-parallel',
    color: '#795548',
    solutions: ['Difuminado', 'Técnica de empalme'],
  },
  {
    id: 'spots',
    label: 'Manchas',
    icon: 'blur',
    color: '#607D8B',
    solutions: ['Aplicación localizada', 'Igualación'],
  },
];

const PRIORITIES = [
  {
    id: 'neutralize',
    label: 'Neutralizar Tono',
    description: 'Eliminar reflejos no deseados',
    icon: 'palette-swatch',
  },
  {
    id: 'lighten',
    label: 'Aclarar',
    description: 'Subir el nivel de color',
    icon: 'white-balance-sunny',
  },
  {
    id: 'darken',
    label: 'Oscurecer',
    description: 'Bajar el nivel de color',
    icon: 'brightness-4',
  },
  {
    id: 'even_out',
    label: 'Emparejar',
    description: 'Unificar el color',
    icon: 'gradient-horizontal',
  },
];

export default function CorrectionGoalStep() {
  const { state, setDesiredColor } = useConsultation();
  const [selectedProblem, setSelectedProblem] = useState<string>('orange');
  const [selectedPriority, setSelectedPriority] = useState<string>('neutralize');
  const [clientExpectation, setClientExpectation] = useState('');
  const [professionalAssessment, setProfessionalAssessment] = useState('');

  useEffect(() => {
    // Load existing data if available
    if (state.desiredColor?.correctionGoal) {
      const goal = state.desiredColor.correctionGoal;
      setSelectedProblem(goal.problemType);
      setSelectedPriority(goal.priority);
      setClientExpectation(goal.clientExpectation);
      setProfessionalAssessment(goal.professionalAssessment);
    }
  }, [state.desiredColor]);

  useEffect(() => {
    // Auto-save when data changes
    if (selectedProblem && selectedPriority) {
      const correctionGoal: CorrectionGoal = {
        problemType: selectedProblem as any,
        priority: selectedPriority as any,
        clientExpectation,
        professionalAssessment,
      };

      setDesiredColor({
        level: state.hairAnalysis?.natural_level || 7,
        tone: 'correction',
        technique: 'correction',
        description: `Corrección: ${PROBLEM_TYPES.find(p => p.id === selectedProblem)?.label}`,
        correctionGoal,
      });
    }
  }, [selectedProblem, selectedPriority, clientExpectation, professionalAssessment]);

  const selectedProblemData = PROBLEM_TYPES.find(p => p.id === selectedProblem);

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Problem Identification */}
      <Card style={styles.card} elevation="medium">
        <Text style={styles.sectionTitle}>🔍 Identificación del Problema</Text>
        <Text style={styles.subtitle}>¿Cuál es el problema principal que necesita corrección?</Text>
        
        <View style={styles.problemGrid}>
          {PROBLEM_TYPES.map((problem) => (
            <TouchableOpacity
              key={problem.id}
              style={[
                styles.problemOption,
                selectedProblem === problem.id && styles.selectedOption,
              ]}
              onPress={() => setSelectedProblem(problem.id)}
            >
              <MaterialCommunityIcons
                name={problem.icon as any}
                size={24}
                color={selectedProblem === problem.id ? COLORS.white : problem.color}
              />
              <Text style={[
                styles.problemLabel,
                selectedProblem === problem.id && styles.selectedLabel,
              ]}>
                {problem.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {selectedProblemData && (
          <View style={styles.solutionsBox}>
            <Text style={styles.solutionsTitle}>Soluciones Recomendadas:</Text>
            {selectedProblemData.solutions.map((solution, index) => (
              <Text key={index} style={styles.solutionItem}>
                • {solution}
              </Text>
            ))}
          </View>
        )}
      </Card>

      {/* Correction Priority */}
      <Card style={styles.card} elevation="medium">
        <Text style={styles.sectionTitle}>Prioridad de Corrección</Text>
        <Text style={styles.subtitle}>¿Cuál es el objetivo principal?</Text>
        
        <View style={styles.priorityContainer}>
          {PRIORITIES.map((priority) => (
            <TouchableOpacity
              key={priority.id}
              style={[
                styles.priorityOption,
                selectedPriority === priority.id && styles.selectedPriority,
              ]}
              onPress={() => setSelectedPriority(priority.id)}
            >
              <MaterialCommunityIcons
                name={priority.icon as any}
                size={20}
                color={selectedPriority === priority.id ? COLORS.primary : COLORS.gray[600]}
              />
              <View style={styles.priorityTextContainer}>
                <Text style={[
                  styles.priorityLabel,
                  selectedPriority === priority.id && styles.selectedPriorityLabel,
                ]}>
                  {priority.label}
                </Text>
                <Text style={styles.priorityDescription}>
                  {priority.description}
                </Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </Card>

      {/* Expectations Management */}
      <Card style={styles.card} elevation="medium">
        <Text style={styles.sectionTitle}>Gestión de Expectativas</Text>
        
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Expectativa del Cliente</Text>
          <TextInput
            style={styles.textInput}
            value={clientExpectation}
            onChangeText={setClientExpectation}
            placeholder="¿Qué espera lograr el cliente?"
            placeholderTextColor={COLORS.gray[400]}
            multiline
            numberOfLines={3}
          />
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Evaluación Profesional</Text>
          <TextInput
            style={styles.textInput}
            value={professionalAssessment}
            onChangeText={setProfessionalAssessment}
            placeholder="¿Qué es realista lograr en esta sesión?"
            placeholderTextColor={COLORS.gray[400]}
            multiline
            numberOfLines={3}
          />
        </View>
      </Card>

      {/* Next Step Info */}
      <Card style={[styles.card, styles.infoCard]} elevation="low">
        <Text style={styles.infoTitle}>📋 Siguiente Paso</Text>
        <Text style={styles.infoText}>
          Una vez identificado el problema, definiremos el color objetivo que el cliente desea lograr.
        </Text>
      </Card>

      {/* Quick Tips */}
      <Card style={[styles.card, styles.tipsCard]} elevation="low">
        <Text style={styles.tipsTitle}>💡 Recordatorios Rápidos</Text>
        <Text style={styles.tipText}>
          • Siempre realiza prueba de mechón en correcciones{'\n'}
          • Documenta el proceso para futuras referencias{'\n'}
          • Explica al cliente si serán necesarias múltiples sesiones
        </Text>
      </Card>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  card: {
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
    padding: SPACING.lg,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: '700',
    color: COLORS.gray[900],
    marginBottom: SPACING.xs,
  },
  subtitle: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
    marginBottom: SPACING.md,
  },
  problemGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.md,
    marginBottom: SPACING.md,
  },
  problemOption: {
    width: '47%',
    flexDirection: 'row',
    alignItems: 'center',
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    backgroundColor: COLORS.gray[50],
    borderWidth: 2,
    borderColor: COLORS.gray[200],
    gap: SPACING.sm,
  },
  selectedOption: {
    backgroundColor: COLORS.primary,
    borderColor: COLORS.primary,
  },
  problemLabel: {
    flex: 1,
    fontSize: FONT_SIZES.sm,
    fontWeight: '600',
    color: COLORS.gray[800],
  },
  selectedLabel: {
    color: COLORS.white,
  },
  solutionsBox: {
    backgroundColor: COLORS.info + '10',
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    marginTop: SPACING.sm,
  },
  solutionsTitle: {
    fontSize: FONT_SIZES.sm,
    fontWeight: '600',
    color: COLORS.gray[800],
    marginBottom: SPACING.xs,
  },
  solutionItem: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[700],
    lineHeight: 20,
  },
  priorityContainer: {
    gap: SPACING.md,
  },
  priorityOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    backgroundColor: COLORS.gray[50],
    borderWidth: 2,
    borderColor: COLORS.gray[200],
    gap: SPACING.md,
  },
  selectedPriority: {
    borderColor: COLORS.primary,
    backgroundColor: COLORS.primary + '10',
  },
  priorityTextContainer: {
    flex: 1,
  },
  priorityLabel: {
    fontSize: FONT_SIZES.md,
    fontWeight: '600',
    color: COLORS.gray[800],
  },
  selectedPriorityLabel: {
    color: COLORS.primary,
  },
  priorityDescription: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.gray[600],
    marginTop: 2,
  },
  inputContainer: {
    marginBottom: SPACING.md,
  },
  inputLabel: {
    fontSize: FONT_SIZES.sm,
    fontWeight: '600',
    color: COLORS.gray[700],
    marginBottom: SPACING.xs,
  },
  textInput: {
    borderWidth: 1,
    borderColor: COLORS.gray[300],
    borderRadius: BORDER_RADIUS.md,
    padding: SPACING.md,
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[900],
    textAlignVertical: 'top',
    minHeight: 80,
  },
  infoCard: {
    backgroundColor: COLORS.info + '10',
    marginBottom: SPACING.lg,
  },
  infoTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: '600',
    color: COLORS.gray[900],
    marginBottom: SPACING.sm,
  },
  infoText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[700],
    lineHeight: 20,
  },
  tipsCard: {
    backgroundColor: COLORS.warning + '10',
    marginBottom: SPACING.xl,
  },
  tipsTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: '600',
    color: COLORS.gray[900],
    marginBottom: SPACING.sm,
  },
  tipText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[700],
    lineHeight: 20,
  },
});