import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Animated,
} from 'react-native';
import { COLORS, SPACING, BORDER_RADIUS } from '../../../constants';
import { TYPOGRAPHY, SHADOWS, COMPONENTS } from '../../../constants/design-system';
import { useSimpleConsultation } from '../../../hooks/useSimpleConsultation';
import { ColorFormulation } from '../../../types';
import { dataService } from '../../../services/dataService';
import { formulationAIService } from '../../../services/formulationAIService';
import Card from '../../common/Card';
import { Icon } from '../../common/Icon';
import BigActionButton from '../../common/BigActionButton';

export default function SmartFormulation() {
  const { state, setFormulation } = useSimpleConsultation();
  const [loading, setLoading] = useState(true);
  const [formulation, setLocalFormulation] = useState<ColorFormulation | null>(null);
  const [showDetails, setShowDetails] = useState(false);
  const fadeAnim = new Animated.Value(0);

  useEffect(() => {
    generateFormulation();
  }, []);

  const generateFormulation = async () => {
    setLoading(true);
    
    try {
      // Simulate AI generation (3 seconds)
      setTimeout(async () => {
        const userPreferences = await dataService.user.getPreferences();
        
        // Generate formulation based on analysis
        const aiFormulation = await formulationAIService.generateFormulation({
          currentAnalysis: state.hairAnalysis!,
          desiredColor: state.desiredColor || { level: 7, tone: 'natural' },
          technique: state.technique,
          preferredBrands: userPreferences.preferred_brands || [],
          colorHistory: null,
        });

        setLocalFormulation(aiFormulation);
        setFormulation(aiFormulation);
        setLoading(false);

        // Animate in
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }).start();
      }, 3000);
    } catch (error) {
      console.error('Error generating formulation:', error);
      setLoading(false);
    }
  };

  const regenerateFormulation = () => {
    fadeAnim.setValue(0);
    generateFormulation();
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingEmoji}>🤖</Text>
        <ActivityIndicator size="large" color={COLORS.primary} style={{ marginVertical: SPACING.lg }} />
        <Text style={styles.loadingTitle}>Generando Fórmula Inteligente</Text>
        <Text style={styles.loadingSubtitle}>
          {state.isCorrection 
            ? 'Calculando la mejor corrección basada en el problema detectado...'
            : 'Analizando la transición de color y seleccionando productos óptimos...'}
        </Text>
      </View>
    );
  }

  if (!formulation) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorEmoji}>😕</Text>
        <Text style={styles.errorText}>No se pudo generar la fórmula</Text>
        <BigActionButton
          title="Reintentar"
          onPress={regenerateFormulation}
          style={{ marginTop: SPACING.lg }}
        />
      </View>
    );
  }

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <Animated.View style={{ opacity: fadeAnim }}>
        {/* Main Formula Card */}
        <Card style={styles.formulaCard} elevation="md">
          <View style={styles.formulaHeader}>
            <Text style={styles.formulaTitle}>
              {state.isCorrection ? '🔧 Fórmula de Corrección' : '✨ Fórmula Recomendada'}
            </Text>
            <TouchableOpacity onPress={regenerateFormulation}>
              <Icon name="refresh" size={24} color={COLORS.primary} />
            </TouchableOpacity>
          </View>

          {/* Main Product */}
          <View style={styles.mainProduct}>
            <View style={styles.productInfo}>
              <Text style={styles.productName}>{formulation.formula.products[0].name}</Text>
              <Text style={styles.productAmount}>{formulation.formula.products[0].amount}g</Text>
            </View>
            <View style={[styles.colorSwatch, { backgroundColor: formulation.formula.products[0].color || COLORS.gray[400] }]} />
          </View>

          {/* Additional Products */}
          {formulation.formula.products.slice(1).map((product, index) => (
            <View key={index} style={styles.additionalProduct}>
              <Text style={styles.plusSign}>+</Text>
              <View style={styles.productInfo}>
                <Text style={styles.additionalProductName}>{product.name}</Text>
                <Text style={styles.additionalProductAmount}>{product.amount}g</Text>
              </View>
            </View>
          ))}

          {/* Developer */}
          <View style={styles.developer}>
            <Icon name="water" size={20} color={COLORS.secondary} />
            <Text style={styles.developerText}>
              Oxidante {formulation.formula.developer_volume}vol - {formulation.formula.developer_amount}ml
            </Text>
          </View>
        </Card>

        {/* Processing Time */}
        <Card style={styles.timeCard} elevation="sm">
          <View style={styles.timeRow}>
            <Icon name="time" size={20} color={COLORS.primary} />
            <Text style={styles.timeLabel}>Tiempo de proceso:</Text>
            <Text style={styles.timeValue}>{formulation.processing_time} minutos</Text>
          </View>
          
          {state.isCorrection && (
            <View style={styles.sessionsRow}>
              <Icon name="calendar" size={20} color={COLORS.warning} />
              <Text style={styles.sessionsText}>
                Sesiones estimadas: {formulation.estimated_sessions || 1}
              </Text>
            </View>
          )}
        </Card>

        {/* Technical Details Toggle */}
        <TouchableOpacity 
          style={styles.detailsToggle}
          onPress={() => setShowDetails(!showDetails)}
        >
          <Text style={styles.detailsToggleText}>
            {showDetails ? 'Ocultar' : 'Ver'} detalles técnicos
          </Text>
          <Icon 
            name={showDetails ? 'chevron-up' : 'chevron-down'} 
            size={20} 
            color={COLORS.textSecondary} 
          />
        </TouchableOpacity>

        {/* Technical Details */}
        {showDetails && (
          <Card style={styles.detailsCard} elevation="sm">
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Técnica:</Text>
              <Text style={styles.detailValue}>{formulation.technique}</Text>
            </View>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Ratio mezcla:</Text>
              <Text style={styles.detailValue}>{formulation.formula.mixing_ratio}</Text>
            </View>
            {formulation.notes && (
              <View style={styles.notesContainer}>
                <Text style={styles.notesLabel}>Notas técnicas:</Text>
                <Text style={styles.notesText}>{formulation.notes}</Text>
              </View>
            )}
          </Card>
        )}

        {/* AI Confidence */}
        <View style={styles.confidenceContainer}>
          <Icon name="sparkles" size={16} color={COLORS.secondary} />
          <Text style={styles.confidenceText}>
            IA {formulation.confidence}% segura • Basado en {formulation.similar_cases || '100+'} casos similares
          </Text>
        </View>
      </Animated.View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.xl,
  },
  loadingEmoji: {
    fontSize: 64,
    marginBottom: SPACING.lg,
  },
  loadingTitle: {
    fontSize: TYPOGRAPHY.size.xl,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: SPACING.sm,
    textAlign: 'center',
  },
  loadingSubtitle: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.xl,
  },
  errorEmoji: {
    fontSize: 64,
    marginBottom: SPACING.lg,
  },
  errorText: {
    fontSize: TYPOGRAPHY.size.lg,
    fontFamily: TYPOGRAPHY.fontFamily.medium,
    color: COLORS.text,
  },
  formulaCard: {
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
  },
  formulaHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  formulaTitle: {
    fontSize: TYPOGRAPHY.size.xl,
    fontFamily: TYPOGRAPHY.fontFamily.bold,
    fontWeight: '700',
    color: COLORS.text,
  },
  mainProduct: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray[200],
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    fontSize: TYPOGRAPHY.size.lg,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: SPACING.xs,
  },
  productAmount: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.medium,
    color: COLORS.primary,
  },
  colorSwatch: {
    width: 48,
    height: 48,
    borderRadius: BORDER_RADIUS.full,
    borderWidth: 2,
    borderColor: COLORS.white,
    ...SHADOWS.sm,
  },
  additionalProduct: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.sm,
    paddingLeft: SPACING.lg,
  },
  plusSign: {
    fontSize: TYPOGRAPHY.size.xl,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.gray[400],
    marginRight: SPACING.md,
  },
  additionalProductName: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.medium,
    color: COLORS.text,
  },
  additionalProductAmount: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
  },
  developer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.sm,
    marginTop: SPACING.md,
    paddingTop: SPACING.md,
    borderTopWidth: 1,
    borderTopColor: COLORS.gray[200],
  },
  developerText: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.medium,
    color: COLORS.secondary,
  },
  timeCard: {
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.md,
    flexDirection: 'column',
    gap: SPACING.sm,
  },
  timeRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.sm,
  },
  timeLabel: {
    flex: 1,
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
  },
  timeValue: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.text,
  },
  sessionsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.sm,
  },
  sessionsText: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.medium,
    color: COLORS.warning,
  },
  detailsToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: SPACING.xs,
    paddingVertical: SPACING.md,
    marginHorizontal: SPACING.lg,
  },
  detailsToggleText: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.medium,
    color: COLORS.textSecondary,
  },
  detailsCard: {
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: SPACING.sm,
  },
  detailLabel: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
  },
  detailValue: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.medium,
    color: COLORS.text,
  },
  notesContainer: {
    marginTop: SPACING.md,
    paddingTop: SPACING.md,
    borderTopWidth: 1,
    borderTopColor: COLORS.gray[200],
  },
  notesLabel: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.medium,
    color: COLORS.textSecondary,
    marginBottom: SPACING.xs,
  },
  notesText: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.text,
    lineHeight: 20,
  },
  confidenceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: SPACING.xs,
    marginBottom: SPACING.xl,
  },
  confidenceText: {
    fontSize: TYPOGRAPHY.size.xs,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
  },
});