import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  PanResponder,
  Vibration,
} from 'react-native';
import { COLORS, SPACING, BORDER_RADIUS } from '../../constants';
import { TYPOGRAPHY, SHADOWS } from '../../constants/design-system';
import Card from '../common/Card';

interface QuickLevelSelectorProps {
  initialLevel?: number;
  onChange: (level: number) => void;
  label?: string;
}

const LEVELS = [1, 1.5, 2, 2.5, 3, 3.5, 4, 4.5, 5, 5.5, 6, 6.5, 7, 7.5, 8, 8.5, 9, 9.5, 10];

const getLevelColor = (level: number): string => {
  const colors = [
    '#000000', // 1 - Negro
    '#1a0a00', // 1.5
    '#2d1400', // 2 - <PERSON><PERSON><PERSON> muy oscuro
    '#3d1f00', // 2.5
    '#4d2900', // 3 - <PERSON><PERSON><PERSON> oscuro
    '#5d3400', // 3.5
    '#6d3e00', // 4 - Castaño medio
    '#7d4900', // 4.5
    '#8d5300', // 5 - Castaño claro
    '#9d5e00', // 5.5
    '#ad6800', // 6 - Rubio oscuro
    '#bd7300', // 6.5
    '#cd7d00', // 7 - Rubio medio
    '#dd8800', // 7.5
    '#ed9200', // 8 - Rubio claro
    '#fd9d00', // 8.5
    '#ffa700', // 9 - Rubio muy claro
    '#ffb200', // 9.5
    '#ffbc00', // 10 - Rubio platino
  ];
  
  const index = LEVELS.indexOf(level);
  return colors[index] || colors[5]; // Default to level 6 color
};

export default function QuickLevelSelector({ 
  initialLevel = 6, 
  onChange,
  label = "Nivel del Cabello"
}: QuickLevelSelectorProps) {
  const [selectedLevel, setSelectedLevel] = useState(initialLevel);
  const animatedValue = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    const levelIndex = LEVELS.indexOf(initialLevel);
    animatedValue.setValue(levelIndex);
  }, [initialLevel]);

  // Pan responder for future slider implementation
  // const panResponder = useRef(
  //   PanResponder.create({...})
  // ).current;

  const handleLevelPress = (level: number) => {
    setSelectedLevel(level);
    onChange(level);
    Vibration.vibrate(10);
    
    // Animate to new position
    const levelIndex = LEVELS.indexOf(level);
    Animated.spring(animatedValue, {
      toValue: levelIndex,
      useNativeDriver: true,
    }).start();
  };

  const handleQuickAdjust = (increment: number) => {
    const currentIndex = LEVELS.indexOf(selectedLevel);
    const newIndex = Math.max(0, Math.min(LEVELS.length - 1, currentIndex + increment));
    const newLevel = LEVELS[newIndex];
    
    setSelectedLevel(newLevel);
    onChange(newLevel);
    Vibration.vibrate(10);
    
    Animated.spring(animatedValue, {
      toValue: newIndex,
      useNativeDriver: true,
    }).start();
  };

  return (
    <Card style={styles.container} elevation="md">
      <Text style={styles.label}>{label}</Text>
      
      {/* Large level display */}
      <Animated.View 
        style={[
          styles.levelDisplay,
          { 
            backgroundColor: getLevelColor(selectedLevel),
            transform: [{ scale: scaleAnim }]
          }
        ]}
      >
        <Text style={styles.levelNumber}>{selectedLevel}</Text>
        <Text style={styles.levelDescription}>
          {selectedLevel <= 3 ? 'Oscuro' : 
           selectedLevel <= 6 ? 'Medio' : 
           selectedLevel <= 8 ? 'Claro' : 'Muy Claro'}
        </Text>
      </Animated.View>

      {/* Quick adjust buttons */}
      <View style={styles.quickAdjustContainer}>
        <TouchableOpacity 
          style={styles.adjustButton}
          onPress={() => handleQuickAdjust(-1)}
          disabled={selectedLevel === LEVELS[0]}
        >
          <Text style={[styles.adjustButtonText, selectedLevel === LEVELS[0] && styles.adjustButtonDisabled]}>
            −
          </Text>
        </TouchableOpacity>
        
        <View style={styles.adjustTextContainer}>
          <Text style={styles.adjustText}>Ajustar nivel</Text>
        </View>
        
        <TouchableOpacity 
          style={styles.adjustButton}
          onPress={() => handleQuickAdjust(1)}
          disabled={selectedLevel === LEVELS[LEVELS.length - 1]}
        >
          <Text style={[styles.adjustButtonText, selectedLevel === LEVELS[LEVELS.length - 1] && styles.adjustButtonDisabled]}>
            +
          </Text>
        </TouchableOpacity>
      </View>

      {/* Level grid */}
      <View style={styles.levelGrid}>
        {LEVELS.map((level) => (
          <TouchableOpacity
            key={level}
            style={[
              styles.levelOption,
              { backgroundColor: getLevelColor(level) },
              selectedLevel === level && styles.levelOptionSelected,
            ]}
            onPress={() => handleLevelPress(level)}
            activeOpacity={0.8}
          >
            <Text style={[
              styles.levelOptionText,
              level >= 7 && styles.levelOptionTextDark,
              selectedLevel === level && styles.levelOptionTextSelected,
            ]}>
              {level}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Additional info */}
      <View style={styles.infoContainer}>
        <View style={styles.infoItem}>
          <Text style={styles.infoLabel}>Subtono detectado:</Text>
          <Text style={styles.infoValue}>
            {selectedLevel <= 5 ? 'Rojizo' : selectedLevel <= 7 ? 'Dorado' : 'Cenizo'}
          </Text>
        </View>
      </View>
    </Card>
  );
}

const styles = StyleSheet.create({
  container: {
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
    padding: SPACING.lg,
  },
  label: {
    fontSize: TYPOGRAPHY.size.lg,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: SPACING.lg,
    textAlign: 'center',
  },
  levelDisplay: {
    width: 120,
    height: 120,
    borderRadius: BORDER_RADIUS.full,
    alignSelf: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: SPACING.lg,
    ...SHADOWS.lg,
    borderWidth: 3,
    borderColor: COLORS.white,
  },
  levelNumber: {
    fontSize: 48,
    fontFamily: TYPOGRAPHY.fontFamily.bold,
    fontWeight: '700',
    color: COLORS.white,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  levelDescription: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.medium,
    color: COLORS.white,
    opacity: 0.9,
    marginTop: -SPACING.xs,
  },
  quickAdjustContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: SPACING.lg,
  },
  adjustButton: {
    width: 48,
    height: 48,
    borderRadius: BORDER_RADIUS.full,
    backgroundColor: COLORS.surface,
    alignItems: 'center',
    justifyContent: 'center',
    ...SHADOWS.sm,
  },
  adjustButtonText: {
    fontSize: 28,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    color: COLORS.primary,
  },
  adjustButtonDisabled: {
    color: COLORS.gray[300],
  },
  adjustTextContainer: {
    marginHorizontal: SPACING.lg,
  },
  adjustText: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
  },
  levelGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    gap: SPACING.xs,
    marginBottom: SPACING.lg,
  },
  levelOption: {
    width: 44,
    height: 44,
    borderRadius: BORDER_RADIUS.md,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  levelOptionSelected: {
    borderColor: COLORS.primary,
    ...SHADOWS.md,
  },
  levelOptionText: {
    fontSize: TYPOGRAPHY.size.xs,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.white,
  },
  levelOptionTextDark: {
    color: COLORS.text,
  },
  levelOptionTextSelected: {
    fontSize: TYPOGRAPHY.size.sm,
  },
  infoContainer: {
    borderTopWidth: 1,
    borderTopColor: COLORS.gray[200],
    paddingTop: SPACING.md,
  },
  infoItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  infoLabel: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
  },
  infoValue: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.text,
  },
});