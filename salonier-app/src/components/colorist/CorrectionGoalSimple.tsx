import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Vibration,
} from 'react-native';
import { COLORS, SPACING, BORDER_RADIUS } from '../../constants';
import { TYPOGRAPHY, SHADOWS } from '../../constants/design-system';
import Card from '../common/Card';
import { Icon } from '../common/Icon';

interface CorrectionProblem {
  id: string;
  icon: string;
  title: string;
  description: string;
  color: string;
}

interface CorrectionGoalSimpleProps {
  onProblemSelect: (problemId: string, timeframe: string) => void;
  initialProblem?: string;
  initialTimeframe?: string;
}

const PROBLEMS: CorrectionProblem[] = [
  { 
    id: 'orange', 
    icon: '🟠', 
    title: 'Naranja indeseado',
    description: 'Tonos anaranjados o cobrizos',
    color: '#FF6B35'
  },
  { 
    id: 'green', 
    icon: '🟢', 
    title: 'Tonos verdes',
    description: 'Reflejos verdosos o cenizos',
    color: '#4ECB71'
  },
  { 
    id: 'too_dark', 
    icon: '⚫', 
    title: '<PERSON><PERSON> oscuro',
    description: 'Color más oscuro del deseado',
    color: '#2D3436'
  },
  { 
    id: 'too_light', 
    icon: '⚪', 
    title: 'Muy claro',
    description: 'Color más claro del esperado',
    color: '#DFE6E9'
  },
  { 
    id: 'uneven', 
    icon: '🔀', 
    title: 'Color desigual',
    description: 'Manchas o zonas disparejas',
    color: '#6C5CE7'
  },
];

const TIMEFRAMES = [
  { id: 'today', label: 'Hoy', days: 0 },
  { id: 'yesterday', label: 'Ayer', days: 1 },
  { id: 'week', label: '1 semana', days: 7 },
  { id: '2weeks', label: '2 semanas', days: 14 },
  { id: 'month', label: '1 mes', days: 30 },
  { id: 'more', label: 'Más de 1 mes', days: 45 },
];

export default function CorrectionGoalSimple({ 
  onProblemSelect, 
  initialProblem,
  initialTimeframe = 'week'
}: CorrectionGoalSimpleProps) {
  const [selectedProblem, setSelectedProblem] = useState(initialProblem);
  const [selectedTimeframe, setSelectedTimeframe] = useState(initialTimeframe);

  const handleProblemSelect = (problemId: string) => {
    setSelectedProblem(problemId);
    Vibration.vibrate(10);
    
    // Auto-advance to next step after selection
    if (selectedTimeframe) {
      setTimeout(() => {
        onProblemSelect(problemId, selectedTimeframe);
      }, 300);
    }
  };

  const handleTimeframeSelect = (timeframeId: string) => {
    setSelectedTimeframe(timeframeId);
    
    if (selectedProblem) {
      onProblemSelect(selectedProblem, timeframeId);
    }
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Step 1: Problem Selection */}
      <Card style={styles.card} elevation="md">
        <Text style={styles.title}>¿Qué problema tiene el color?</Text>
        <Text style={styles.subtitle}>Toca el que mejor describa la situación</Text>
        
        <View style={styles.problemGrid}>
          {PROBLEMS.map((problem) => (
            <TouchableOpacity
              key={problem.id}
              style={[
                styles.problemOption,
                selectedProblem === problem.id && styles.problemSelected,
                { borderColor: selectedProblem === problem.id ? problem.color : 'transparent' }
              ]}
              onPress={() => handleProblemSelect(problem.id)}
              activeOpacity={0.8}
            >
              <Text style={styles.problemIcon}>{problem.icon}</Text>
              <Text style={[
                styles.problemTitle,
                selectedProblem === problem.id && styles.problemTitleSelected
              ]}>
                {problem.title}
              </Text>
              <Text style={styles.problemDescription}>
                {problem.description}
              </Text>
              {selectedProblem === problem.id && (
                <View style={[styles.checkmark, { backgroundColor: problem.color }]}>
                  <Icon name="checkmark" size={16} color={COLORS.white} />
                </View>
              )}
            </TouchableOpacity>
          ))}
        </View>
      </Card>

      {/* Step 2: Timeframe Selection */}
      {selectedProblem && (
        <Card style={[styles.card, styles.timeframeCard]} elevation="sm">
          <Text style={styles.timeframeTitle}>¿Cuándo se hizo el color?</Text>
          
          <View style={styles.timeframeGrid}>
            {TIMEFRAMES.map((timeframe) => (
              <TouchableOpacity
                key={timeframe.id}
                style={[
                  styles.timeframeOption,
                  selectedTimeframe === timeframe.id && styles.timeframeSelected
                ]}
                onPress={() => handleTimeframeSelect(timeframe.id)}
              >
                <Text style={[
                  styles.timeframeText,
                  selectedTimeframe === timeframe.id && styles.timeframeTextSelected
                ]}>
                  {timeframe.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          {selectedTimeframe && (
            <View style={styles.warningBox}>
              <Icon name="information-circle" size={20} color={COLORS.warning} />
              <Text style={styles.warningText}>
                {selectedTimeframe === 'today' || selectedTimeframe === 'yesterday' 
                  ? 'El cabello está muy sensible. Proceder con precaución.'
                  : selectedTimeframe === 'more' 
                  ? 'Buen momento para corrección, el cabello ha descansado.'
                  : 'Tiempo adecuado para realizar corrección segura.'}
              </Text>
            </View>
          )}
        </Card>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  card: {
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
  },
  title: {
    fontSize: TYPOGRAPHY.size.xl,
    fontFamily: TYPOGRAPHY.fontFamily.bold,
    fontWeight: '700',
    color: COLORS.text,
    textAlign: 'center',
    marginBottom: SPACING.xs,
  },
  subtitle: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginBottom: SPACING.xl,
  },
  problemGrid: {
    gap: SPACING.md,
  },
  problemOption: {
    padding: SPACING.lg,
    borderRadius: BORDER_RADIUS.xl,
    backgroundColor: COLORS.surface,
    borderWidth: 3,
    borderColor: 'transparent',
    ...SHADOWS.sm,
    position: 'relative',
    overflow: 'hidden',
  },
  problemSelected: {
    backgroundColor: COLORS.white,
    ...SHADOWS.lg,
  },
  problemIcon: {
    fontSize: 48,
    textAlign: 'center',
    marginBottom: SPACING.sm,
  },
  problemTitle: {
    fontSize: TYPOGRAPHY.size.lg,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.text,
    textAlign: 'center',
    marginBottom: SPACING.xs,
  },
  problemTitleSelected: {
    fontFamily: TYPOGRAPHY.fontFamily.bold,
    fontWeight: '700',
  },
  problemDescription: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
  checkmark: {
    position: 'absolute',
    top: SPACING.md,
    right: SPACING.md,
    width: 28,
    height: 28,
    borderRadius: BORDER_RADIUS.full,
    alignItems: 'center',
    justifyContent: 'center',
    ...SHADOWS.sm,
  },
  timeframeCard: {
    marginTop: -SPACING.sm,
  },
  timeframeTitle: {
    fontSize: TYPOGRAPHY.size.lg,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: SPACING.lg,
    textAlign: 'center',
  },
  timeframeGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.sm,
    justifyContent: 'center',
    marginBottom: SPACING.lg,
  },
  timeframeOption: {
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    borderRadius: BORDER_RADIUS.full,
    backgroundColor: COLORS.surface,
    minWidth: 100,
    ...SHADOWS.sm,
  },
  timeframeSelected: {
    backgroundColor: COLORS.secondary,
    ...SHADOWS.md,
  },
  timeframeText: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.medium,
    color: COLORS.text,
    textAlign: 'center',
  },
  timeframeTextSelected: {
    color: COLORS.white,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
  },
  warningBox: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: SPACING.sm,
    padding: SPACING.md,
    backgroundColor: COLORS.warning + '10',
    borderRadius: BORDER_RADIUS.lg,
    borderLeftWidth: 3,
    borderLeftColor: COLORS.warning,
  },
  warningText: {
    flex: 1,
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.text,
    lineHeight: 20,
  },
});