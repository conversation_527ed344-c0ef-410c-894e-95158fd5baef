import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ScrollView,
} from 'react-native';
import { COLORS, SPACING, FONT_SIZES, BORDER_RADIUS, HAIR_LEVELS } from '../../constants';
import { TYPOGRAPHY, SHADOWS, COMPONENTS } from '../../constants/design-system';
import Icon from 'react-native-vector-icons/Ionicons';
import * as Haptics from 'expo-haptics';

interface LevelPickerProps {
  value: number;
  onChange: (value: number) => void;
  label?: string;
}

export default function LevelPicker({ value, onChange, label = "Nivel" }: LevelPickerProps) {
  const [showModal, setShowModal] = useState(false);
  const [tempInteger, setTempInteger] = useState(Math.floor(value));
  const [tempDecimal, setTempDecimal] = useState(Math.round((value % 1) * 10) / 10);

  const handleConfirm = () => {
    const newValue = tempInteger + tempDecimal;
    onChange(newValue);
    setShowModal(false);
  };

  const handleQuickAdjust = (delta: number) => {
    const newValue = Math.max(1, Math.min(10, Math.round((value + delta) * 10) / 10));
    onChange(newValue);
    // Feedback háptico sutil
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const levelInfo = HAIR_LEVELS[Math.floor(value) - 1];

  return (
    <>
      <View style={styles.container}>
        <Text style={styles.label}>{label}</Text>
        
        <View style={styles.levelControl}>
          {/* Quick adjust buttons */}
          <TouchableOpacity 
            style={styles.adjustButton}
            onPress={() => handleQuickAdjust(-0.1)}
          >
            <Icon name="remove" size={20} color={COLORS.gray[600]} />
          </TouchableOpacity>

          {/* Main display */}
          <TouchableOpacity 
            style={styles.levelDisplay}
            onPress={() => setShowModal(true)}
          >
            <View 
              style={[
                styles.colorSample,
                { backgroundColor: levelInfo?.color || '#000' }
              ]}
            />
            <View style={styles.levelInfo}>
              <Text style={styles.levelValue}>{value.toFixed(1)}</Text>
              <Text style={styles.levelName}>{levelInfo?.name || ''}</Text>
            </View>
            <Icon name="chevron-down" size={20} color={COLORS.gray[400]} />
          </TouchableOpacity>

          {/* Quick adjust buttons */}
          <TouchableOpacity 
            style={styles.adjustButton}
            onPress={() => handleQuickAdjust(0.1)}
          >
            <Icon name="add" size={20} color={COLORS.gray[600]} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Precision Modal */}
      <Modal
        visible={showModal}
        transparent
        animationType="slide"
        onRequestClose={() => setShowModal(false)}
      >
        <TouchableOpacity 
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setShowModal(false)}
        >
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Selecciona el Nivel</Text>
              <TouchableOpacity onPress={() => setShowModal(false)}>
                <Icon name="close" size={24} color={COLORS.gray[600]} />
              </TouchableOpacity>
            </View>

            {/* Integer selector */}
            <View style={styles.selectorSection}>
              <Text style={styles.selectorLabel}>Nivel Base</Text>
              <ScrollView 
                horizontal 
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={styles.integerOptions}
              >
                {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((num) => (
                  <TouchableOpacity
                    key={num}
                    style={[
                      styles.integerOption,
                      tempInteger === num && styles.optionSelected
                    ]}
                    onPress={() => {
                      setTempInteger(num);
                      Haptics.selectionAsync();
                    }}
                  >
                    <View 
                      style={[
                        styles.integerColorSample,
                        { backgroundColor: HAIR_LEVELS[num - 1]?.color || '#000' }
                      ]}
                    />
                    <Text style={[
                      styles.integerText,
                      tempInteger === num && styles.optionTextSelected
                    ]}>
                      {num}
                    </Text>
                    <Text style={styles.integerName}>
                      {HAIR_LEVELS[num - 1]?.name.split(' ')[0]}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>

            {/* Decimal selector */}
            <View style={styles.selectorSection}>
              <Text style={styles.selectorLabel}>Precisión</Text>
              <View style={styles.decimalOptions}>
                {[0, 0.3, 0.5, 0.7].map((decimal) => (
                  <TouchableOpacity
                    key={decimal}
                    style={[
                      styles.decimalOption,
                      Math.abs(tempDecimal - decimal) < 0.01 && styles.optionSelected
                    ]}
                    onPress={() => {
                      setTempDecimal(decimal);
                      Haptics.selectionAsync();
                    }}
                  >
                    <Text style={[
                      styles.decimalText,
                      Math.abs(tempDecimal - decimal) < 0.01 && styles.optionTextSelected
                    ]}>
                      .{(decimal * 10).toFixed(0)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Preview */}
            <View style={styles.preview}>
              <Text style={styles.previewLabel}>Resultado:</Text>
              <View style={styles.previewValue}>
                <View 
                  style={[
                    styles.previewColorSample,
                    { backgroundColor: HAIR_LEVELS[tempInteger - 1]?.color || '#000' }
                  ]}
                />
                <Text style={styles.previewLevel}>
                  {(tempInteger + tempDecimal).toFixed(1)}
                </Text>
                <Text style={styles.previewName}>
                  {HAIR_LEVELS[tempInteger - 1]?.name || ''}
                </Text>
              </View>
            </View>

            {/* Confirm button */}
            <TouchableOpacity 
              style={styles.confirmButton}
              onPress={handleConfirm}
            >
              <Text style={styles.confirmButtonText}>Confirmar</Text>
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: SPACING.lg,
  },
  label: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.medium,
    color: COLORS.textSecondary,
    marginBottom: SPACING.sm,
  },
  levelControl: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.sm,
  },
  adjustButton: {
    width: 36,
    height: 36,
    borderRadius: BORDER_RADIUS.full,
    backgroundColor: COLORS.surface,
    alignItems: 'center',
    justifyContent: 'center',
    ...SHADOWS.sm,
  },
  levelDisplay: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.surface,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.md,
    ...SHADOWS.sm,
  },
  colorSample: {
    width: 40,
    height: 40,
    borderRadius: BORDER_RADIUS.md,
    marginRight: SPACING.md,
    borderWidth: 1,
    borderColor: COLORS.gray[200],
  },
  levelInfo: {
    flex: 1,
  },
  levelValue: {
    fontSize: TYPOGRAPHY.size.xl,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    color: COLORS.text,
  },
  levelName: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: COLORS.white,
    borderTopLeftRadius: BORDER_RADIUS.xxl,
    borderTopRightRadius: BORDER_RADIUS.xxl,
    padding: SPACING.xl,
    paddingBottom: SPACING.xl + 20,
    ...SHADOWS.xl,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.xl,
  },
  modalTitle: {
    fontSize: TYPOGRAPHY.size.xl,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    color: COLORS.text,
  },
  selectorSection: {
    marginBottom: SPACING.xl,
  },
  selectorLabel: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.medium,
    color: COLORS.textSecondary,
    marginBottom: SPACING.md,
  },
  integerOptions: {
    flexDirection: 'row',
    gap: SPACING.sm,
    paddingRight: SPACING.lg,
  },
  integerOption: {
    alignItems: 'center',
    padding: SPACING.sm,
    borderRadius: BORDER_RADIUS.lg,
    backgroundColor: COLORS.gray[50],
    minWidth: 70,
  },
  optionSelected: {
    backgroundColor: COLORS.primary,
  },
  integerColorSample: {
    width: 50,
    height: 50,
    borderRadius: BORDER_RADIUS.md,
    marginBottom: SPACING.xs,
    borderWidth: 1,
    borderColor: COLORS.gray[200],
  },
  integerText: {
    fontSize: TYPOGRAPHY.size.lg,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    color: COLORS.text,
  },
  integerName: {
    fontSize: TYPOGRAPHY.size.xs,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
    marginTop: SPACING.xs,
  },
  optionTextSelected: {
    color: COLORS.white,
  },
  decimalOptions: {
    flexDirection: 'row',
    gap: SPACING.md,
  },
  decimalOption: {
    flex: 1,
    alignItems: 'center',
    padding: SPACING.lg,
    borderRadius: BORDER_RADIUS.lg,
    backgroundColor: COLORS.gray[50],
  },
  decimalText: {
    fontSize: TYPOGRAPHY.size.xl,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    color: COLORS.text,
  },
  preview: {
    backgroundColor: COLORS.gray[50],
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.lg,
    marginBottom: SPACING.xl,
  },
  previewLabel: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.medium,
    color: COLORS.textSecondary,
    marginBottom: SPACING.sm,
  },
  previewValue: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.md,
  },
  previewColorSample: {
    width: 60,
    height: 60,
    borderRadius: BORDER_RADIUS.lg,
    borderWidth: 2,
    borderColor: COLORS.gray[200],
  },
  previewLevel: {
    fontSize: TYPOGRAPHY.size['2xl'],
    fontFamily: TYPOGRAPHY.fontFamily.bold,
    color: COLORS.text,
  },
  previewName: {
    flex: 1,
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
  },
  confirmButton: {
    ...COMPONENTS.button.primary,
    paddingVertical: SPACING.lg,
  },
  confirmButtonText: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    color: COLORS.white,
  },
});