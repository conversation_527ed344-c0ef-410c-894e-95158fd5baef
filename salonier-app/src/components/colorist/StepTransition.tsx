import React, { useEffect, useRef } from 'react';
import { Animated, ViewStyle } from 'react-native';

interface StepTransitionProps {
  children: React.ReactNode;
  active: boolean;
  style?: ViewStyle;
}

export default function StepTransition({ children, active, style }: StepTransitionProps) {
  const fadeAnim = useRef(new Animated.Value(active ? 1 : 0)).current;
  const translateX = useRef(new Animated.Value(active ? 0 : 50)).current;

  useEffect(() => {
    if (active) {
      // Animar entrada
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.spring(translateX, {
          toValue: 0,
          friction: 8,
          tension: 40,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      // Animar salida
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(translateX, {
          toValue: -50,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [active, fadeAnim, translateX]);

  if (!active) return null;

  return (
    <Animated.View
      style={[
        {
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          opacity: fadeAnim,
          transform: [{ translateX }],
        },
        style,
      ]}
    >
      {children}
    </Animated.View>
  );
}
