import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { COLORS, SPACING, FONT_SIZES } from '../../constants';
import { ConsultationStep } from '../../hooks/useConsultation';
import { useConsultation } from '../../hooks/useConsultation';

interface StepIndicatorProps {
  currentStep: ConsultationStep;
  elapsedTime: number;
}

export default function StepIndicator({ currentStep, elapsedTime }: StepIndicatorProps) {
  const { state } = useConsultation();
  const isCorrection = state.technique === 'correction';
  
  // Static steps - same for correction and normal coloring
  const STEPS = [
    { id: ConsultationStep.SELECT_CLIENT, label: 'Cliente', icon: '👤' },
    { id: ConsultationStep.SAFETY_CHECK, label: 'Seguridad', icon: '✅' },
    { id: ConsultationStep.HAIR_ANALYSIS, label: isCorrection ? 'Problema' : 'Análisis', icon: isCorrection ? '⚠️' : '📸' },
    { id: ConsultationStep.DESIRED_COLOR, label: 'Objetivo', icon: '🎯' },
    { id: ConsultationStep.TECHNIQUE_SELECTION, label: 'Técnica', icon: '🎨' },
    { id: ConsultationStep.FORMULATION, label: 'Fórmula', icon: '🧪' },
    { id: ConsultationStep.BRAND_CONVERSION, label: 'Marcas', icon: '🔄' },
    { id: ConsultationStep.COMPLETION, label: 'Finalizar', icon: '✨' },
  ];
  
  const currentStepIndex = STEPS.findIndex(step => step.id === currentStep);
  
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <View style={styles.container}>
      {/* Barra de progreso */}
      <View style={styles.progressContainer}>
        <View style={styles.progressBackground}>
          <View 
            style={[
              styles.progressBar, 
              { width: `${((currentStepIndex + 1) / STEPS.length) * 100}%` }
            ]} 
          />
        </View>
      </View>

      {/* Indicadores de pasos */}
      <View style={styles.stepsContainer}>
        {STEPS.map((step, index) => {
          const isActive = index === currentStepIndex;
          const isCompleted = index < currentStepIndex;

          return (
            <View key={step.id} style={styles.stepWrapper}>
              <View 
                style={[
                  styles.stepCircle,
                  isActive && styles.activeCircle,
                  isCompleted && styles.completedCircle,
                ]}
              >
                <Text style={styles.stepIcon}>{step.icon}</Text>
              </View>
              {index < STEPS.length - 1 && (
                <View 
                  style={[
                    styles.stepLine,
                    isCompleted && styles.completedLine,
                  ]} 
                />
              )}
            </View>
          );
        })}
      </View>

      {/* Información del paso actual y tiempo */}
      <View style={styles.infoContainer}>
        <Text style={styles.currentStepLabel}>
          {STEPS[currentStepIndex]?.label || ''}
        </Text>
        <View style={styles.timeContainer}>
          <Text style={styles.timeIcon}>⏱</Text>
          <Text style={styles.timeText}>{formatTime(elapsedTime)}</Text>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.white,
    paddingTop: SPACING.md,
    paddingHorizontal: SPACING.lg,
    paddingBottom: SPACING.lg,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray[200],
  },
  progressContainer: {
    marginBottom: SPACING.md,
  },
  progressBackground: {
    height: 4,
    backgroundColor: COLORS.gray[200],
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: COLORS.primary,
    borderRadius: 2,
  },
  stepsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  stepWrapper: {
    flex: 1,
    alignItems: 'center',
    position: 'relative',
  },
  stepCircle: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: COLORS.gray[200],
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  activeCircle: {
    backgroundColor: COLORS.primary,
    transform: [{ scale: 1.1 }],
  },
  completedCircle: {
    backgroundColor: COLORS.success,
  },
  stepIcon: {
    fontSize: 16,
  },
  stepLine: {
    position: 'absolute',
    top: 16,
    left: '50%',
    right: '-50%',
    height: 2,
    backgroundColor: COLORS.gray[200],
    zIndex: 0,
  },
  completedLine: {
    backgroundColor: COLORS.success,
  },
  infoContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  currentStepLabel: {
    fontSize: FONT_SIZES.md,
    fontWeight: '600',
    color: COLORS.gray[900],
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.gray[100],
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: 12,
  },
  timeIcon: {
    fontSize: 14,
    marginRight: SPACING.xs,
  },
  timeText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[700],
    fontWeight: '500',
  },
});