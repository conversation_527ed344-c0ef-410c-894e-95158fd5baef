import React from 'react';
import { View, StyleSheet } from 'react-native';
import { COLORS, SPACING, BORDER_RADIUS } from '../../constants';
import { SHADOWS } from '../../constants/design-system';
import { SkeletonLoader } from '../common/SkeletonLoader';
import Card from '../common/Card';

export default function HairAnalysisSkeleton() {
  return (
    <View style={styles.container}>
      {/* Header Skeleton */}
      <View style={styles.header}>
        <SkeletonLoader width="60%" height={32} borderRadius={8} />
        <SkeletonLoader width="80%" height={16} style={styles.subtitle} />
      </View>

      {/* Photo Section Skeleton */}
      <Card style={styles.photoCard} elevation="medium">
        <SkeletonLoader width="100%" height={200} borderRadius={BORDER_RADIUS.lg} />
        <View style={styles.photoButtons}>
          <SkeletonLoader width={120} height={40} borderRadius={20} />
          <SkeletonLoader width={120} height={40} borderRadius={20} />
        </View>
      </Card>

      {/* Analysis Parameters Skeleton */}
      <Card style={styles.analysisCard} elevation="medium">
        <SkeletonLoader width="40%" height={20} style={styles.sectionTitle} />
        <View style={styles.optionsRow}>
          <SkeletonLoader variant="circle" height={60} />
          <SkeletonLoader variant="circle" height={60} />
          <SkeletonLoader variant="circle" height={60} />
        </View>
      </Card>

      {/* Additional Fields Skeleton */}
      <Card style={styles.analysisCard} elevation="medium">
        <SkeletonLoader width="50%" height={20} style={styles.sectionTitle} />
        <SkeletonLoader lines={3} spacing={12} />
      </Card>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    padding: SPACING.lg,
    paddingBottom: SPACING.md,
  },
  subtitle: {
    marginTop: SPACING.sm,
  },
  photoCard: {
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
    padding: SPACING.lg,
    backgroundColor: COLORS.surface,
    ...SHADOWS.sm,
  },
  photoButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: SPACING.lg,
  },
  analysisCard: {
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
    padding: SPACING.lg,
    backgroundColor: COLORS.surface,
  },
  sectionTitle: {
    marginBottom: SPACING.md,
  },
  optionsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: SPACING.md,
  },
});
