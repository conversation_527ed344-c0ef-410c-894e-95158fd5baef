import React from 'react';
import {
  View,
  Text,
  StyleSheet,
} from 'react-native';
import { COLORS, SPACING, FONT_SIZES, BORDER_RADIUS } from '../../constants';
import { TYPOGRAPHY, SHADOWS, COMPONENTS } from '../../constants/design-system';
import Card from '../common/Card';
import Icon from 'react-native-vector-icons/Ionicons';

export interface ViabilityData {
  achievable_in_one_session: boolean;
  achievable_level: number;
  achievable_percentage: number;
  warnings: string[];
  requirements: string[];
  alternative_paths?: {
    description: string;
    session1: string;
    session2: string;
    total_cost?: number;
  }[];
  viability_score: 'safe' | 'caution' | 'risky' | 'not_recommended';
}

interface ViabilityAnalysisProps {
  currentLevel: number;
  desiredLevel: number;
  currentCondition?: 'healthy' | 'damaged' | 'very_healthy';
  isCorrection?: boolean;
  viabilityData?: ViabilityData;
}

export default function ViabilityAnalysis({
  currentLevel,
  desiredLevel,
  currentCondition = 'healthy',
  isCorrection = false,
  viabilityData
}: ViabilityAnalysisProps) {
  
  // Calcular viabilidad si no se proporciona
  const calculateViability = (): ViabilityData => {
    const levelDifference = desiredLevel - currentLevel;
    const isLightening = levelDifference > 0;
    const absoluteDifference = Math.abs(levelDifference);
    
    let viability: ViabilityData = {
      achievable_in_one_session: true,
      achievable_level: desiredLevel,
      achievable_percentage: 100,
      warnings: [],
      requirements: [],
      viability_score: 'safe'
    };
    
    // Análisis según diferencia de niveles
    if (isLightening) {
      if (absoluteDifference <= 2) {
        // Aclarado suave - viable
        viability.viability_score = 'safe';
        viability.requirements.push('Oxidante 20-30 vol');
      } else if (absoluteDifference <= 4) {
        // Aclarado moderado - precaución
        viability.viability_score = 'caution';
        viability.achievable_percentage = 85;
        viability.warnings.push('Aclarado significativo - posible tono cálido');
        viability.requirements.push('Oxidante 30-40 vol');
        viability.requirements.push('Posible pre-aclarado');
        
        if (currentCondition === 'damaged') {
          viability.achievable_in_one_session = false;
          viability.achievable_level = currentLevel + 2.5;
          viability.warnings.push('Cabello dañado - requiere tratamiento progresivo');
        }
      } else {
        // Aclarado extremo - riesgoso
        viability.viability_score = 'risky';
        viability.achievable_in_one_session = false;
        viability.achievable_level = currentLevel + 3;
        viability.achievable_percentage = 60;
        viability.warnings.push('Aclarado extremo - múltiples sesiones necesarias');
        viability.warnings.push('Alto riesgo de daño');
        viability.requirements.push('Tratamiento reconstructor obligatorio');
        
        viability.alternative_paths = [{
          description: 'Opción segura: 2-3 sesiones',
          session1: `Aclarar a nivel ${(currentLevel + 2.5).toFixed(1)}`,
          session2: `Alcanzar nivel ${desiredLevel.toFixed(1)}`,
          total_cost: 150
        }];
      }
    } else if (absoluteDifference > 0) {
      // Oscurecer
      viability.viability_score = 'safe';
      if (absoluteDifference > 3) {
        viability.requirements.push('Pre-pigmentación recomendada');
      }
    }
    
    // Ajustes para corrección
    if (isCorrection) {
      viability.warnings.push('Corrección de color - resultado puede variar');
      viability.achievable_percentage = Math.max(70, viability.achievable_percentage - 15);
      if (viability.viability_score === 'safe') {
        viability.viability_score = 'caution';
      }
    }
    
    return viability;
  };
  
  const viability = viabilityData || calculateViability();
  
  // Colores y iconos según viabilidad
  const getViabilityStyle = () => {
    switch (viability.viability_score) {
      case 'safe':
        return { color: COLORS.success, icon: 'checkmark-circle', text: 'Viable' };
      case 'caution':
        return { color: COLORS.warning, icon: 'alert-circle', text: 'Precaución' };
      case 'risky':
        return { color: COLORS.error, icon: 'warning', text: 'Riesgoso' };
      case 'not_recommended':
        return { color: COLORS.error, icon: 'close-circle', text: 'No recomendado' };
      default:
        return { color: COLORS.gray[600], icon: 'help-circle', text: 'Evaluando' };
    }
  };
  
  const style = getViabilityStyle();
  
  return (
    <Card style={styles.container} elevation="medium">
      {/* Header con estado */}
      <View style={[styles.header, { backgroundColor: style.color + '15' }]}>
        <Icon name={style.icon} size={24} color={style.color} />
        <Text style={[styles.headerText, { color: style.color }]}>
          {style.text} - {viability.achievable_percentage}% del objetivo
        </Text>
      </View>
      
      {/* Resumen principal */}
      <View style={styles.summary}>
        <Text style={styles.summaryTitle}>
          {viability.achievable_in_one_session 
            ? '✅ Objetivo alcanzable en 1 sesión'
            : `⚠️ Se puede llegar a nivel ${viability.achievable_level.toFixed(1)} hoy`
          }
        </Text>
        <View style={styles.levelComparison}>
          <View style={styles.levelBox}>
            <Text style={styles.levelLabel}>Actual</Text>
            <Text style={styles.levelValue}>{currentLevel.toFixed(1)}</Text>
          </View>
          <Icon name="arrow-forward" size={20} color={COLORS.gray[400]} />
          <View style={styles.levelBox}>
            <Text style={styles.levelLabel}>Objetivo</Text>
            <Text style={styles.levelValue}>{desiredLevel.toFixed(1)}</Text>
          </View>
          {!viability.achievable_in_one_session && (
            <>
              <Icon name="arrow-forward" size={20} color={COLORS.gray[400]} />
              <View style={[styles.levelBox, styles.achievableBox]}>
                <Text style={styles.levelLabel}>Hoy</Text>
                <Text style={[styles.levelValue, { color: style.color }]}>
                  {viability.achievable_level.toFixed(1)}
                </Text>
              </View>
            </>
          )}
        </View>
      </View>
      
      {/* Advertencias */}
      {viability.warnings.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>⚠️ Advertencias</Text>
          {viability.warnings.map((warning, index) => (
            <View key={index} style={styles.warningItem}>
              <Icon name="alert-circle-outline" size={16} color={COLORS.warning} />
              <Text style={styles.warningText}>{warning}</Text>
            </View>
          ))}
        </View>
      )}
      
      {/* Requisitos */}
      {viability.requirements.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>📋 Requisitos</Text>
          {viability.requirements.map((req, index) => (
            <View key={index} style={styles.requirementItem}>
              <Icon name="checkmark" size={16} color={COLORS.primary} />
              <Text style={styles.requirementText}>{req}</Text>
            </View>
          ))}
        </View>
      )}
      
      {/* Rutas alternativas */}
      {viability.alternative_paths && viability.alternative_paths.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🔄 Alternativas Recomendadas</Text>
          {viability.alternative_paths.map((path, index) => (
            <View key={index} style={styles.alternativeCard}>
              <Text style={styles.alternativeTitle}>{path.description}</Text>
              <View style={styles.sessionSteps}>
                <View style={styles.sessionStep}>
                  <Text style={styles.sessionNumber}>1</Text>
                  <Text style={styles.sessionText}>{path.session1}</Text>
                </View>
                <View style={styles.sessionStep}>
                  <Text style={styles.sessionNumber}>2</Text>
                  <Text style={styles.sessionText}>{path.session2}</Text>
                </View>
              </View>
              {path.total_cost && (
                <Text style={styles.alternativeCost}>
                  Costo estimado total: €{path.total_cost}
                </Text>
              )}
            </View>
          ))}
        </View>
      )}
    </Card>
  );
}

const styles = StyleSheet.create({
  container: {
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
    overflow: 'hidden',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.sm,
    padding: SPACING.md,
    marginHorizontal: -SPACING.lg,
    marginTop: -SPACING.lg,
    marginBottom: SPACING.lg,
  },
  headerText: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
  },
  summary: {
    marginBottom: SPACING.lg,
  },
  summaryTitle: {
    fontSize: TYPOGRAPHY.size.lg,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    color: COLORS.text,
    marginBottom: SPACING.md,
  },
  levelComparison: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: SPACING.md,
  },
  levelBox: {
    alignItems: 'center',
    padding: SPACING.md,
    backgroundColor: COLORS.gray[50],
    borderRadius: BORDER_RADIUS.lg,
    minWidth: 70,
  },
  achievableBox: {
    backgroundColor: COLORS.warning + '15',
    borderWidth: 1,
    borderColor: COLORS.warning,
    borderStyle: 'dashed',
  },
  levelLabel: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
    marginBottom: SPACING.xs,
  },
  levelValue: {
    fontSize: TYPOGRAPHY.size.xl,
    fontFamily: TYPOGRAPHY.fontFamily.bold,
    color: COLORS.text,
  },
  section: {
    marginTop: SPACING.lg,
    paddingTop: SPACING.lg,
    borderTopWidth: 1,
    borderTopColor: COLORS.gray[200],
  },
  sectionTitle: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    color: COLORS.text,
    marginBottom: SPACING.sm,
  },
  warningItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: SPACING.sm,
    marginBottom: SPACING.sm,
  },
  warningText: {
    flex: 1,
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.warning,
    lineHeight: TYPOGRAPHY.size.sm * 1.5,
  },
  requirementItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: SPACING.sm,
    marginBottom: SPACING.sm,
  },
  requirementText: {
    flex: 1,
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
    lineHeight: TYPOGRAPHY.size.sm * 1.5,
  },
  alternativeCard: {
    backgroundColor: COLORS.info + '10',
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.md,
    marginTop: SPACING.sm,
  },
  alternativeTitle: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.medium,
    color: COLORS.text,
    marginBottom: SPACING.sm,
  },
  sessionSteps: {
    gap: SPACING.sm,
  },
  sessionStep: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.sm,
  },
  sessionNumber: {
    width: 24,
    height: 24,
    borderRadius: BORDER_RADIUS.full,
    backgroundColor: COLORS.primary,
    color: COLORS.white,
    textAlign: 'center',
    lineHeight: 24,
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
  },
  sessionText: {
    flex: 1,
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
  },
  alternativeCost: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.medium,
    color: COLORS.primary,
    marginTop: SPACING.sm,
    textAlign: 'right',
  },
});