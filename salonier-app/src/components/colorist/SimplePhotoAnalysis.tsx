import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Image,
  Animated,
  Dimensions,
  Vibration,
} from 'react-native';
import { COLORS, SPACING, BORDER_RADIUS } from '../../constants';
import { TYPOGRAPHY, SHADOWS, COMPONENTS } from '../../constants/design-system';
import { usePhotoAnalysis, PhotoAnalysisData } from '../../hooks/usePhotoAnalysis';
import Card from '../common/Card';
import QuickLevelSelector from './QuickLevelSelector';
import ZoneToggle from '../photo/ZoneAnalysis/ZoneToggle';
import ZoneControls from '../photo/ZoneAnalysis/ZoneControls';
import PhotoSourceSelector from '../photo/PhotoSourceSelector';
import { Icon } from '../common/Icon';

const { width: screenWidth } = Dimensions.get('window');

interface SimplePhotoAnalysisProps {
  mode: 'current' | 'desired';
  title: string;
  subtitle?: string;
  onComplete: (data: PhotoAnalysisData) => void;
  initialData?: Partial<PhotoAnalysisData>;
  showComparison?: boolean;
  comparisonData?: any;
}

export default function SimplePhotoAnalysis({
  mode,
  title,
  subtitle,
  onComplete,
  initialData,
  showComparison,
  comparisonData,
}: SimplePhotoAnalysisProps) {
  const photoAnalysis = usePhotoAnalysis(initialData);
  const [showSourceSelector, setShowSourceSelector] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [showManualSelection, setShowManualSelection] = useState(false);
  const [analysisComplete, setAnalysisComplete] = useState(false);
  
  // Animaciones
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.9)).current;
  const successAnim = useRef(new Animated.Value(0)).current;

  const handlePhotoCapture = async () => {
    const photoUri = await photoAnalysis.capturePhoto();
    if (photoUri) {
      await photoAnalysis.addPhoto(photoUri);
      analyzePhoto();
    }
  };

  const handleGallerySelection = async () => {
    const photoUris = await photoAnalysis.pickFromGallery(false);
    if (photoUris.length > 0) {
      await photoAnalysis.addPhoto(photoUris[0]);
      analyzePhoto();
    }
  };

  const analyzePhoto = async () => {
    setIsAnalyzing(true);
    setShowSourceSelector(false);
    
    // Animación de entrada
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 8,
        tension: 40,
        useNativeDriver: true,
      }),
    ]).start();

    // Simular análisis (3 segundos)
    setTimeout(() => {
      setIsAnalyzing(false);
      setAnalysisComplete(true);
      setShowManualSelection(true);
      
      // Vibración de éxito
      Vibration.vibrate(100);
      
      // Animación de éxito
      Animated.sequence([
        Animated.timing(successAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(successAnim, {
          toValue: 0,
          duration: 200,
          delay: 500,
          useNativeDriver: true,
        }),
      ]).start();
      
      // Auto-populate with AI results
      photoAnalysis.setMode('manual');
      if (!photoAnalysis.customizeByZones) {
        // Simple analysis
        photoAnalysis.setZoneAnalysis({
          roots: { level: 6, tone: 'natural', porosity: 'medium', condition: 'healthy', grayPercentage: 20 },
          mids: { level: 6.5, tone: 'warm', porosity: 'medium', condition: 'healthy', grayPercentage: 10 },
          ends: { level: 7, tone: 'warm', porosity: 'high', condition: 'damaged', grayPercentage: 0 },
        });
      }
    }, 3000);
  };

  const handleSourceSelect = (source: 'camera' | 'gallery') => {
    if (source === 'camera') {
      handlePhotoCapture();
    } else {
      handleGallerySelection();
    }
  };

  const handleManualSelection = () => {
    setShowManualSelection(true);
    photoAnalysis.setMode('manual');
  };

  // Handle completion is done automatically when analysis is complete

  const renderAnalyzing = () => (
    <Animated.View 
      style={[
        styles.analyzingContainer,
        {
          opacity: fadeAnim,
          transform: [{ scale: scaleAnim }]
        }
      ]}
    >
      <View style={styles.analyzingContent}>
        <Text style={styles.analyzingEmoji}>🤖</Text>
        <Text style={styles.analyzingText}>Analizando con IA...</Text>
        
        <View style={styles.progressBar}>
          <Animated.View 
            style={[
              styles.progressFill,
              {
                width: fadeAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: ['0%', '100%']
                })
              }
            ]}
          />
        </View>
        
        <Text style={styles.analyzingSubtext}>
          {mode === 'current' 
            ? 'Detectando nivel, tono y estado del cabello'
            : 'Analizando el color objetivo deseado'}
        </Text>
      </View>
    </Animated.View>
  );

  const renderMainAction = () => {
    if (photoAnalysis.photos.length > 0 && !isAnalyzing && !showManualSelection) {
      // Photo taken but not analyzed
      return (
        <Card style={styles.mainCard} elevation="md">
          <Image source={{ uri: photoAnalysis.photos[0] }} style={styles.photoPreview} />
          <TouchableOpacity style={styles.retakeButton} onPress={() => {
            photoAnalysis.removePhoto(0);
            setAnalysisComplete(false);
          }}>
            <Icon name="close-circle" size={24} color={COLORS.white} />
          </TouchableOpacity>
          <TouchableOpacity style={styles.analyzeButton} onPress={analyzePhoto}>
            <Text style={styles.analyzeButtonText}>Analizar Foto</Text>
          </TouchableOpacity>
        </Card>
      );
    }

    if (showManualSelection) {
      // Manual selection mode
      return (
        <>
          {photoAnalysis.photos.length > 0 && (
            <View style={styles.photoThumbnailContainer}>
              <Image source={{ uri: photoAnalysis.photos[0] }} style={styles.photoThumbnail} />
              <TouchableOpacity 
                style={styles.removePhotoButton} 
                onPress={() => {
                  photoAnalysis.removePhoto(0);
                  setAnalysisComplete(false);
                  setShowManualSelection(false);
                }}
              >
                <Icon name="close" size={16} color={COLORS.white} />
              </TouchableOpacity>
            </View>
          )}
          
          {analysisComplete && (
            <Animated.View style={[styles.successMessage, { opacity: successAnim }]}>
              <Icon name="checkmark-circle" size={20} color={COLORS.success} />
              <Text style={styles.successText}>Análisis completado</Text>
            </Animated.View>
          )}

          <QuickLevelSelector
            initialLevel={photoAnalysis.zoneAnalysis.roots.level}
            onChange={(level) => {
              const newZoneAnalysis = {
                roots: { ...photoAnalysis.zoneAnalysis.roots, level },
                mids: { ...photoAnalysis.zoneAnalysis.mids, level: photoAnalysis.customizeByZones ? photoAnalysis.zoneAnalysis.mids.level : level },
                ends: { ...photoAnalysis.zoneAnalysis.ends, level: photoAnalysis.customizeByZones ? photoAnalysis.zoneAnalysis.ends.level : level },
              };
              photoAnalysis.setZoneAnalysis(newZoneAnalysis);
            }}
          />
        </>
      );
    }

    // Initial state - show main action button
    return (
      <Card style={styles.mainCard} elevation="medium">
        <TouchableOpacity 
          style={styles.mainActionButton}
          onPress={() => setShowSourceSelector(true)}
          activeOpacity={0.8}
        >
          <Text style={styles.mainActionEmoji}>📸</Text>
          <Text style={styles.mainActionText}>ANALIZAR CON CÁMARA</Text>
          <Text style={styles.mainActionSubtext}>
            {mode === 'current' ? 'Captura el estado actual' : 'Muestra tu color deseado'}
          </Text>
        </TouchableOpacity>
        
        <View style={styles.divider}>
          <View style={styles.dividerLine} />
          <Text style={styles.dividerText}>o</Text>
          <View style={styles.dividerLine} />
        </View>
        
        <TouchableOpacity 
          style={styles.secondaryAction}
          onPress={handleManualSelection}
        >
          <Text style={styles.secondaryActionText}>Selección manual →</Text>
        </TouchableOpacity>
      </Card>
    );
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.header}>
        <Text style={styles.title}>{title}</Text>
        {subtitle && <Text style={styles.subtitle}>{subtitle}</Text>}
      </View>

      {isAnalyzing ? renderAnalyzing() : renderMainAction()}

      {/* Zone customization */}
      {showManualSelection && (
        <>
          <ZoneToggle
            enabled={photoAnalysis.customizeByZones}
            onChange={photoAnalysis.setCustomizeByZones}
            subtitle={mode === 'current' 
              ? 'Analiza raíces, medios y puntas por separado'
              : 'Define diferentes colores por zona'}
          />

          {photoAnalysis.customizeByZones && (
            <ZoneControls
              zones={photoAnalysis.zoneAnalysis}
              onChange={photoAnalysis.setZoneAnalysis}
              mode={mode}
            />
          )}
        </>
      )}

      {/* Comparison */}
      {showComparison && comparisonData && showManualSelection && (
        <Card style={styles.comparisonCard} elevation="sm">
          <View style={styles.comparisonContent}>
            <View style={styles.comparisonItem}>
              <View style={[styles.colorCircle, { backgroundColor: '#8B6A47' }]} />
              <Text style={styles.comparisonLabel}>Actual</Text>
              <Text style={styles.comparisonValue}>Nivel {comparisonData.natural_level || 6}</Text>
            </View>
            
            <Icon name="arrow-forward" size={24} color={COLORS.gray[400]} />
            
            <View style={styles.comparisonItem}>
              <View style={[styles.colorCircle, { backgroundColor: '#D4AF37' }]} />
              <Text style={styles.comparisonLabel}>Objetivo</Text>
              <Text style={styles.comparisonValue}>Nivel {photoAnalysis.zoneAnalysis.roots.level}</Text>
            </View>
          </View>
        </Card>
      )}

      {/* Source Selector Modal */}
      {showSourceSelector && (
        <PhotoSourceSelector
          visible={showSourceSelector}
          onClose={() => setShowSourceSelector(false)}
          onSelect={handleSourceSelect}
        />
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    padding: SPACING.lg,
    paddingBottom: SPACING.md,
  },
  title: {
    fontSize: TYPOGRAPHY.size['2xl'],
    fontFamily: TYPOGRAPHY.fontFamily.bold,
    fontWeight: '700',
    color: COLORS.text,
    marginBottom: SPACING.xs,
  },
  subtitle: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
  },
  mainCard: {
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
    overflow: 'hidden',
  },
  mainActionButton: {
    alignItems: 'center',
    padding: SPACING.xl,
    paddingVertical: SPACING.xl * 1.5,
  },
  mainActionEmoji: {
    fontSize: 56,
    marginBottom: SPACING.md,
  },
  mainActionText: {
    fontSize: TYPOGRAPHY.size.xl,
    fontFamily: TYPOGRAPHY.fontFamily.bold,
    fontWeight: '700',
    color: COLORS.primary,
    marginBottom: SPACING.xs,
    letterSpacing: TYPOGRAPHY.letterSpacing.wide,
  },
  mainActionSubtext: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.xl,
    marginBottom: SPACING.md,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: COLORS.gray[200],
  },
  dividerText: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.gray[400],
    marginHorizontal: SPACING.md,
  },
  secondaryAction: {
    paddingBottom: SPACING.lg,
    alignItems: 'center',
  },
  secondaryActionText: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.medium,
    color: COLORS.textSecondary,
  },
  analyzingContainer: {
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
  },
  analyzingContent: {
    ...COMPONENTS.card.elevated,
    alignItems: 'center',
    paddingVertical: SPACING.xl * 2,
  },
  analyzingEmoji: {
    fontSize: 64,
    marginBottom: SPACING.lg,
  },
  analyzingText: {
    fontSize: TYPOGRAPHY.size.xl,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: SPACING.lg,
  },
  analyzingSubtext: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
    textAlign: 'center',
    paddingHorizontal: SPACING.xl,
  },
  progressBar: {
    width: screenWidth * 0.6,
    height: 4,
    backgroundColor: COLORS.gray[200],
    borderRadius: BORDER_RADIUS.sm,
    overflow: 'hidden',
    marginBottom: SPACING.lg,
  },
  progressFill: {
    height: '100%',
    backgroundColor: COLORS.primary,
  },
  photoPreview: {
    width: '100%',
    height: 250,
    borderRadius: BORDER_RADIUS.lg,
    marginBottom: SPACING.md,
  },
  retakeButton: {
    position: 'absolute',
    top: SPACING.md,
    right: SPACING.md,
    backgroundColor: COLORS.gray[800] + 'CC',
    borderRadius: BORDER_RADIUS.full,
    padding: SPACING.xs,
  },
  analyzeButton: {
    ...COMPONENTS.button.primary,
    marginHorizontal: SPACING.md,
    marginBottom: SPACING.md,
  },
  analyzeButtonText: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.white,
  },
  photoThumbnailContainer: {
    alignSelf: 'center',
    marginBottom: SPACING.md,
  },
  photoThumbnail: {
    width: 80,
    height: 80,
    borderRadius: BORDER_RADIUS.lg,
    borderWidth: 2,
    borderColor: COLORS.primary,
  },
  removePhotoButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: COLORS.error,
    borderRadius: BORDER_RADIUS.full,
    padding: 4,
    ...SHADOWS.sm,
  },
  successMessage: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: SPACING.xs,
    marginBottom: SPACING.md,
  },
  successText: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.medium,
    color: COLORS.success,
  },
  comparisonCard: {
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
    backgroundColor: COLORS.info + '10',
  },
  comparisonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-evenly',
  },
  comparisonItem: {
    alignItems: 'center',
  },
  colorCircle: {
    width: 60,
    height: 60,
    borderRadius: BORDER_RADIUS.full,
    marginBottom: SPACING.sm,
    borderWidth: 3,
    borderColor: COLORS.white,
    ...SHADOWS.md,
  },
  comparisonLabel: {
    fontSize: TYPOGRAPHY.size.xs,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
    marginBottom: SPACING.xs,
  },
  comparisonValue: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    fontWeight: '600',
    color: COLORS.text,
  },
});