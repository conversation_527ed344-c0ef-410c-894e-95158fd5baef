import React, { useRef, useState } from 'react';
import { View, Text, StyleSheet, Modal, TouchableOpacity, Platform } from 'react-native';
import Toast from 'react-native-toast-message';
import SignatureCanvas from 'react-native-signature-canvas';
import { COLORS, SPACING, BORDER_RADIUS } from '../../constants';
import { TYPOGRAPHY, SHADOWS, COMPONENTS } from '../../constants/design-system';
import Icon from 'react-native-vector-icons/Ionicons';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import * as MailComposer from 'expo-mail-composer';

interface DigitalSignatureProps {
  visible: boolean;
  onClose: () => void;
  onSign: (signature: string) => void;
  clientName: string;
  clientEmail?: string;
  clientPhone?: string;
}

export default function DigitalSignature({
  visible,
  onClose,
  onSign,
  clientName,
  clientEmail,
  clientPhone,
}: DigitalSignatureProps) {
  const signatureRef = useRef<SignatureCanvas>(null);
  const [isSigning, setIsSigning] = useState(false);

  const handleSignature = (signature: string) => {
    // La firma viene en formato base64
    onSign(signature);
    sendConsentDocument(signature);
  };

  const handleClear = () => {
    signatureRef.current?.clearSignature();
    setIsSigning(false);
  };

  const handleBegin = () => {
    setIsSigning(true);
  };

  const handleEnd = () => {
    signatureRef.current?.readSignature();
  };

  const sendConsentDocument = async (signatureBase64: string) => {
    try {
      // Generar contenido HTML del consentimiento
      const htmlContent = generateConsentHTML(clientName, signatureBase64);

      // Guardar como archivo temporal
      const fileName = `consentimiento_${clientName.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.html`;
      const fileUri = `${FileSystem.documentDirectory}${fileName}`;

      await FileSystem.writeAsStringAsync(fileUri, htmlContent);

      // Opciones de envío
      const sendOptions = [];

      if (clientEmail && (await MailComposer.isAvailableAsync())) {
        sendOptions.push({
          label: 'Enviar por Email',
          action: async () => {
            await MailComposer.composeAsync({
              recipients: [clientEmail],
              subject: 'Consentimiento de Coloración - Firmado',
              body: `Estimado/a ${clientName},\n\nAdjunto encontrará el consentimiento firmado para el servicio de coloración.\n\nGracias por su confianza.\n\nSaludos cordiales,\n${global.salonName || 'El Salón'}`,
              attachments: [fileUri],
            });
          },
        });
      }

      if (clientPhone) {
        sendOptions.push({
          label: 'Enviar por WhatsApp',
          action: async () => {
            // En una app real, aquí se integraría con WhatsApp Business API
            // Por ahora, compartimos el archivo
            if (await Sharing.isAvailableAsync()) {
              await Sharing.shareAsync(fileUri, {
                mimeType: 'text/html',
                dialogTitle: 'Enviar consentimiento por WhatsApp',
              });
            }
          },
        });
      }

      sendOptions.push({
        label: 'Compartir',
        action: async () => {
          if (await Sharing.isAvailableAsync()) {
            await Sharing.shareAsync(fileUri);
          }
        },
      });

      // Mostrar opciones al usuario
      Toast.show({
        type: 'success',
        text1: 'Firma Guardada',
        text2: 'Toca para enviar por WhatsApp o Email',
        position: 'top',
        visibilityTime: 5000,
        onPress: () => {
          Toast.hide();
          // Default to WhatsApp
          const whatsappOption = sendOptions.find(opt => opt.method === 'whatsapp');
          if (whatsappOption) {
            whatsappOption.action();
          }
        },
      });
    } catch (error) {
      console.error('Error al procesar el consentimiento:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'No se pudo procesar el documento. Por favor, intente nuevamente.',
        position: 'top',
      });
    }
  };

  const generateConsentHTML = (name: string, signature: string): string => {
    const currentDate = new Date().toLocaleDateString('es-ES', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
    });

    return `
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Consentimiento de Coloración</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    .header {
      text-align: center;
      margin-bottom: 30px;
      border-bottom: 2px solid #1184e3;
      padding-bottom: 20px;
    }
    .logo {
      font-size: 24px;
      font-weight: bold;
      color: #1184e3;
    }
    h1 {
      color: #131516;
      margin-top: 20px;
    }
    .content {
      margin: 30px 0;
    }
    .section {
      margin: 20px 0;
    }
    .section h3 {
      color: #1184e3;
      margin-bottom: 10px;
    }
    ul {
      margin: 10px 0;
      padding-left: 25px;
    }
    .signature-section {
      margin-top: 40px;
      border: 1px solid #ddd;
      padding: 20px;
      background-color: #f9f9f9;
      border-radius: 8px;
    }
    .signature-img {
      max-width: 300px;
      height: 150px;
      border: 1px solid #ccc;
      margin: 20px auto;
      display: block;
    }
    .footer {
      text-align: center;
      margin-top: 40px;
      padding-top: 20px;
      border-top: 1px solid #ddd;
      color: #666;
      font-size: 14px;
    }
  </style>
</head>
<body>
  <div class="header">
    <div class="logo">🎨 SALONIER</div>
    <h1>Consentimiento Informado - Servicio de Coloración</h1>
  </div>

  <div class="content">
    <div class="section">
      <p><strong>Fecha:</strong> ${currentDate}</p>
      <p><strong>Cliente:</strong> ${name}</p>
    </div>

    <div class="section">
      <h3>Información del Servicio</h3>
      <p>Por medio del presente documento, yo <strong>${name}</strong>, declaro que he sido informado/a sobre:</p>
      <ul>
        <li>Los productos químicos que se utilizarán en el proceso de coloración</li>
        <li>Los posibles riesgos y reacciones alérgicas</li>
        <li>La importancia de realizar un test de alergia 48 horas antes</li>
        <li>El procedimiento completo del servicio</li>
        <li>Los cuidados necesarios post-tratamiento</li>
      </ul>
    </div>

    <div class="section">
      <h3>Declaración de Responsabilidad</h3>
      <p>Declaro que:</p>
      <ul>
        <li>He informado sobre cualquier alergia o sensibilidad conocida</li>
        <li>No tengo lesiones o irritaciones en el cuero cabelludo</li>
        <li>Comprendo que los resultados pueden variar según las características de mi cabello</li>
        <li>Acepto seguir las recomendaciones de cuidado post-tratamiento</li>
      </ul>
    </div>

    <div class="section">
      <h3>Consentimiento</h3>
      <p>Habiendo sido debidamente informado/a y comprendiendo los riesgos asociados, 
      doy mi consentimiento para realizar el servicio de coloración bajo mi propia responsabilidad.</p>
    </div>

    <div class="signature-section">
      <h3>Firma del Cliente</h3>
      <img src="${signature}" alt="Firma del cliente" class="signature-img">
      <p style="text-align: center;">
        <strong>${name}</strong><br>
        ${currentDate}
      </p>
    </div>
  </div>

  <div class="footer">
    <p>Este documento ha sido generado digitalmente a través de Salonier App</p>
    <p>© 2025 Salonier - Todos los derechos reservados</p>
  </div>
</body>
</html>
    `;
  };

  const webStyle = `.m-signature-pad {
    box-shadow: none;
    border: none;
    background-color: white;
  }
  .m-signature-pad--body {
    border: none;
    background-color: white;
  }
  .m-signature-pad--footer {
    display: none;
    margin: 0px;
  }
  body,html {
    width: 100%;
    height: 100%;
    background-color: white;
  }`;

  return (
    <Modal visible={visible} animationType="slide" transparent={true} onRequestClose={onClose}>
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.title}>Firma Digital</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Icon name="close" size={24} color={COLORS.gray[600]} />
            </TouchableOpacity>
          </View>

          {/* Instructions */}
          <View style={styles.instructions}>
            <Icon name="create-outline" size={20} color={COLORS.primary} />
            <Text style={styles.instructionText}>
              Por favor, que el cliente firme con su dedo en el área inferior
            </Text>
          </View>

          {/* Signature Canvas */}
          <View style={styles.signatureContainer}>
            <SignatureCanvas
              ref={signatureRef}
              onOK={handleSignature}
              onBegin={handleBegin}
              onEnd={handleEnd}
              descriptionText=""
              clearText="Limpiar"
              confirmText="Confirmar"
              webStyle={webStyle}
              backgroundColor="white"
              penColor={COLORS.text}
              minWidth={2}
              maxWidth={3}
            />
            {!isSigning && (
              <View style={styles.signaturePlaceholder}>
                <Icon name="brush-outline" size={48} color={COLORS.gray[300]} />
                <Text style={styles.placeholderText}>Firma aquí</Text>
              </View>
            )}
          </View>

          {/* Client Info */}
          <View style={styles.clientInfo}>
            <Text style={styles.clientName}>{clientName}</Text>
            <Text style={styles.dateText}>
              {new Date().toLocaleDateString('es-ES', {
                day: 'numeric',
                month: 'long',
                year: 'numeric',
              })}
            </Text>
          </View>

          {/* Action Buttons */}
          <View style={styles.actionButtons}>
            <TouchableOpacity style={[styles.button, styles.clearButton]} onPress={handleClear}>
              <Icon name="refresh" size={20} color={COLORS.gray[600]} />
              <Text style={styles.clearButtonText}>Limpiar</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.button, styles.confirmButton]}
              onPress={handleEnd}
              disabled={!isSigning}
            >
              <Icon name="checkmark" size={20} color={COLORS.white} />
              <Text style={styles.confirmButtonText}>Confirmar Firma</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.lg,
  },
  modalContent: {
    backgroundColor: COLORS.white,
    borderRadius: BORDER_RADIUS.xxl,
    width: '100%',
    maxWidth: 500,
    maxHeight: '90%',
    ...SHADOWS.xl,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.xl,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray[200],
  },
  title: {
    fontSize: TYPOGRAPHY.size.xl,
    fontFamily: TYPOGRAPHY.fontFamily.bold,
    color: COLORS.text,
  },
  closeButton: {
    padding: SPACING.xs,
  },
  instructions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.sm,
    paddingHorizontal: SPACING.xl,
    paddingVertical: SPACING.md,
    backgroundColor: COLORS.info + '10',
  },
  instructionText: {
    flex: 1,
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.text,
  },
  signatureContainer: {
    height: 250,
    marginHorizontal: SPACING.xl,
    marginVertical: SPACING.lg,
    borderRadius: BORDER_RADIUS.lg,
    borderWidth: 2,
    borderColor: COLORS.gray[300],
    borderStyle: 'dashed',
    overflow: 'hidden',
    backgroundColor: COLORS.white,
    position: 'relative',
  },
  signaturePlaceholder: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.white,
    pointerEvents: 'none',
  },
  placeholderText: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.gray[400],
    marginTop: SPACING.sm,
  },
  clientInfo: {
    alignItems: 'center',
    paddingHorizontal: SPACING.xl,
    marginBottom: SPACING.lg,
  },
  clientName: {
    fontSize: TYPOGRAPHY.size.lg,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    color: COLORS.text,
    marginBottom: SPACING.xs,
  },
  dateText: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: SPACING.md,
    paddingHorizontal: SPACING.xl,
    paddingBottom: SPACING.xl,
  },
  button: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: SPACING.sm,
    paddingVertical: SPACING.md,
    borderRadius: BORDER_RADIUS.full,
  },
  clearButton: {
    backgroundColor: COLORS.surface,
    ...SHADOWS.sm,
  },
  clearButtonText: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.medium,
    color: COLORS.text,
  },
  confirmButton: {
    backgroundColor: COLORS.primary,
    ...SHADOWS.md,
  },
  confirmButtonText: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    color: COLORS.white,
  },
});
