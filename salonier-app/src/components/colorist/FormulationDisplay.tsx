import React from 'react';
import {
  View,
  Text,
  StyleSheet,
} from 'react-native';
import { COLORS, SPACING, FONT_SIZES, BORDER_RADIUS } from '../../constants';
import { TYPOGRAPHY, SHADOWS, COMPONENTS } from '../../constants/design-system';
import Card from '../common/Card';
import Icon from 'react-native-vector-icons/Ionicons';
import { ColorFormulation } from '../../types';

interface FormulationDisplayProps {
  formulation: ColorFormulation;
  brandName?: string;
  isCorrection?: boolean;
  serviceCost?: number | null;
}

export default function FormulationDisplay({
  formulation,
  brandName,
  isCorrection = false,
  serviceCost
}: FormulationDisplayProps) {
  
  // Separar productos principales de oxidante
  const mainProducts = formulation.formula.products.filter(p => !p.name.toLowerCase().includes('oxidante'));
  const developer = formulation.formula.products.find(p => p.name.toLowerCase().includes('oxidante'));
  
  // Calcular proporción de mezcla
  const totalMainProducts = mainProducts.reduce((sum, p) => sum + p.amount, 0);
  const developerAmount = developer?.amount || 0;
  const mixRatio = developerAmount > 0 ? `1:${(developerAmount / totalMainProducts).toFixed(1)}` : '1:1';
  
  // Calcular margen de beneficio
  const calculateMargin = () => {
    const cost = serviceCost || formulation.total_cost;
    const margin = ((formulation.suggested_price - cost) / formulation.suggested_price) * 100;
    return margin.toFixed(0);
  };
  
  const getMarginColor = () => {
    const margin = parseInt(calculateMargin());
    if (margin >= 70) return COLORS.success;
    if (margin >= 50) return COLORS.warning;
    return COLORS.error;
  };
  
  return (
    <Card style={styles.container} elevation="medium">
      {/* Header */}
      <View style={styles.header}>
        <View>
          <Text style={styles.title}>Fórmula Profesional</Text>
          {brandName && (
            <Text style={styles.brandName}>{brandName}</Text>
          )}
        </View>
        {isCorrection && (
          <View style={styles.correctionBadge}>
            <Icon name="construct" size={16} color={COLORS.warning} />
            <Text style={styles.correctionText}>Correctiva</Text>
          </View>
        )}
      </View>
      
      {/* Visual Formula Display */}
      <View style={styles.formulaVisual}>
        {/* Main Products */}
        <View style={styles.productsSection}>
          {mainProducts.map((product, index) => (
            <View key={index} style={styles.productRow}>
              <View style={[styles.colorIndicator, { backgroundColor: getProductColor(product.name) }]} />
              <View style={styles.productInfo}>
                <Text style={styles.productName}>{product.name}</Text>
                <Text style={styles.productAmount}>{product.amount} {product.unit}</Text>
              </View>
              {index < mainProducts.length - 1 && (
                <View style={styles.plusSign}>
                  <Icon name="add" size={16} color={COLORS.gray[400]} />
                </View>
              )}
            </View>
          ))}
        </View>
        
        {/* Mix Arrow */}
        {developer && (
          <View style={styles.mixArrow}>
            <Icon name="add-circle-outline" size={24} color={COLORS.primary} />
          </View>
        )}
        
        {/* Developer */}
        {developer && (
          <View style={styles.developerSection}>
            <Icon name="water" size={20} color={COLORS.gray[600]} />
            <View style={styles.productInfo}>
              <Text style={styles.productName}>{developer.name}</Text>
              <Text style={styles.productAmount}>{developer.amount} {developer.unit}</Text>
            </View>
          </View>
        )}
      </View>
      
      {/* Mix Ratio Visual */}
      <View style={styles.ratioSection}>
        <Text style={styles.ratioLabel}>Proporción de Mezcla</Text>
        <View style={styles.ratioVisual}>
          <View style={[styles.ratioBar, { flex: 1, backgroundColor: COLORS.primary }]} />
          <View style={[styles.ratioBar, { flex: parseFloat(mixRatio.split(':')[1]), backgroundColor: COLORS.gray[300] }]} />
        </View>
        <Text style={styles.ratioValue}>{mixRatio}</Text>
      </View>
      
      {/* Application Info */}
      <View style={styles.applicationInfo}>
        <View style={styles.infoRow}>
          <View style={styles.infoItem}>
            <Icon name="time-outline" size={20} color={COLORS.gray[600]} />
            <Text style={styles.infoLabel}>Tiempo</Text>
            <Text style={styles.infoValue}>{formulation.processing_time} min</Text>
          </View>
          
          <View style={styles.infoDivider} />
          
          <View style={styles.infoItem}>
            <Icon name="brush-outline" size={20} color={COLORS.gray[600]} />
            <Text style={styles.infoLabel}>Técnica</Text>
            <Text style={styles.infoValue}>
              {formulation.technique === 'global' ? 'Global' :
               formulation.technique === 'zones' ? 'Por zonas' : 
               formulation.technique || 'Estándar'}
            </Text>
          </View>
        </View>
      </View>
      
      {/* Cost & Margin */}
      <View style={styles.costSection}>
        <View style={styles.costRow}>
          <View style={styles.costItem}>
            <Text style={styles.costLabel}>Costo</Text>
            <Text style={styles.costValue}>€{(serviceCost || formulation.total_cost).toFixed(2)}</Text>
          </View>
          
          <Icon name="arrow-forward" size={16} color={COLORS.gray[400]} />
          
          <View style={styles.costItem}>
            <Text style={styles.costLabel}>Precio Sugerido</Text>
            <Text style={styles.priceValue}>€{formulation.suggested_price.toFixed(2)}</Text>
          </View>
          
          <View style={[styles.marginBadge, { backgroundColor: getMarginColor() + '15' }]}>
            <Text style={[styles.marginText, { color: getMarginColor() }]}>
              {calculateMargin()}% margen
            </Text>
          </View>
        </View>
      </View>
      
      {/* Notes if any */}
      {formulation.notes && (
        <View style={styles.notesSection}>
          <Icon name="information-circle-outline" size={20} color={COLORS.info} />
          <Text style={styles.notesText}>{formulation.notes}</Text>
        </View>
      )}
    </Card>
  );
}

// Helper function to get color based on product name
function getProductColor(productName: string): string {
  const name = productName.toLowerCase();
  if (name.includes('rojo') || name.includes('red')) return '#DC143C';
  if (name.includes('cobrizo') || name.includes('copper')) return '#B87333';
  if (name.includes('dorado') || name.includes('gold')) return '#FFD700';
  if (name.includes('ceniza') || name.includes('ash')) return '#A8A8A8';
  if (name.includes('violeta') || name.includes('violet')) return '#8B7AB8';
  if (name.includes('caoba') || name.includes('mahogany')) return '#C04000';
  if (name.includes('beige')) return '#F5DEB3';
  if (name.includes('natural')) return '#8B6A47';
  
  // Detectar por número de nivel
  const levelMatch = name.match(/\d+/);
  if (levelMatch) {
    const level = parseInt(levelMatch[0]);
    if (level <= 3) return '#2F1F0F';
    if (level <= 5) return '#5D4037';
    if (level <= 7) return '#A0826D';
    if (level <= 9) return '#D4A574';
  }
  
  return '#8B6A47'; // Default brown
}

const styles = StyleSheet.create({
  container: {
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  title: {
    fontSize: TYPOGRAPHY.size.lg,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    color: COLORS.text,
  },
  brandName: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
    marginTop: SPACING.xs,
  },
  correctionBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.xs,
    backgroundColor: COLORS.warning + '15',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.full,
  },
  correctionText: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.medium,
    color: COLORS.warning,
  },
  formulaVisual: {
    backgroundColor: COLORS.gray[50],
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.lg,
    marginBottom: SPACING.lg,
  },
  productsSection: {
    marginBottom: SPACING.md,
  },
  productRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  colorIndicator: {
    width: 32,
    height: 32,
    borderRadius: BORDER_RADIUS.md,
    marginRight: SPACING.md,
    borderWidth: 1,
    borderColor: COLORS.gray[200],
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.medium,
    color: COLORS.text,
  },
  productAmount: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
  },
  plusSign: {
    marginLeft: SPACING.sm,
  },
  mixArrow: {
    alignItems: 'center',
    marginVertical: SPACING.sm,
  },
  developerSection: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.white,
    borderRadius: BORDER_RADIUS.md,
    padding: SPACING.md,
    borderWidth: 1,
    borderColor: COLORS.gray[200],
  },
  ratioSection: {
    marginBottom: SPACING.lg,
  },
  ratioLabel: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.medium,
    color: COLORS.textSecondary,
    marginBottom: SPACING.sm,
  },
  ratioVisual: {
    flexDirection: 'row',
    height: 8,
    borderRadius: BORDER_RADIUS.xs,
    overflow: 'hidden',
    marginBottom: SPACING.xs,
  },
  ratioBar: {
    height: '100%',
  },
  ratioValue: {
    fontSize: TYPOGRAPHY.size.lg,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    color: COLORS.text,
    textAlign: 'center',
  },
  applicationInfo: {
    backgroundColor: COLORS.gray[50],
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.md,
    marginBottom: SPACING.lg,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  infoItem: {
    flex: 1,
    alignItems: 'center',
  },
  infoDivider: {
    width: 1,
    height: 40,
    backgroundColor: COLORS.gray[200],
    marginHorizontal: SPACING.md,
  },
  infoLabel: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
    marginTop: SPACING.xs,
  },
  infoValue: {
    fontSize: TYPOGRAPHY.size.base,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    color: COLORS.text,
    marginTop: SPACING.xs,
  },
  costSection: {
    borderTopWidth: 1,
    borderTopColor: COLORS.gray[200],
    paddingTop: SPACING.lg,
  },
  costRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  costItem: {
    alignItems: 'center',
  },
  costLabel: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
  },
  costValue: {
    fontSize: TYPOGRAPHY.size.lg,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    color: COLORS.text,
    marginTop: SPACING.xs,
  },
  priceValue: {
    fontSize: TYPOGRAPHY.size.lg,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
    color: COLORS.primary,
    marginTop: SPACING.xs,
  },
  marginBadge: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.full,
  },
  marginText: {
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.semibold,
  },
  notesSection: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: SPACING.sm,
    marginTop: SPACING.lg,
    padding: SPACING.md,
    backgroundColor: COLORS.info + '10',
    borderRadius: BORDER_RADIUS.md,
  },
  notesText: {
    flex: 1,
    fontSize: TYPOGRAPHY.size.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.text,
    lineHeight: TYPOGRAPHY.size.sm * 1.5,
  },
});