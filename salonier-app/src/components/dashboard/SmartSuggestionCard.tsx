import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
} from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { COLORS, SPACING, FONT_SIZES, BORDER_RADIUS } from '../../constants';
import { SmartSuggestion } from '../../services/smartSuggestionService';
import Card from '../common/Card';

interface SmartSuggestionCardProps {
  suggestion: SmartSuggestion;
  onAction: () => void;
  onDismiss: () => void;
}

export default function SmartSuggestionCard({ 
  suggestion, 
  onAction, 
  onDismiss 
}: SmartSuggestionCardProps) {
  const scaleAnim = React.useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
      tension: 50,
      friction: 8,
    }).start();
  }, []);

  return (
    <Animated.View 
      style={[
        styles.container,
        {
          transform: [{ scale: scaleAnim }],
        }
      ]}
    >
      <Card style={styles.card} elevation="high">
        <TouchableOpacity
          style={styles.dismissButton}
          onPress={onDismiss}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <MaterialCommunityIcons 
            name="close" 
            size={20} 
            color={COLORS.gray[400]} 
          />
        </TouchableOpacity>

        <View style={styles.header}>
          <View 
            style={[
              styles.iconContainer,
              { backgroundColor: suggestion.color + '20' }
            ]}
          >
            <MaterialCommunityIcons 
              name={suggestion.icon as any} 
              size={24} 
              color={suggestion.color} 
            />
          </View>
          <View style={styles.headerText}>
            <Text style={styles.title}>{suggestion.title}</Text>
            <Text style={styles.description}>{suggestion.description}</Text>
          </View>
        </View>

        {suggestion.benefit && (
          <View style={styles.benefitContainer}>
            <MaterialCommunityIcons 
              name="star" 
              size={16} 
              color={suggestion.color} 
            />
            <Text style={styles.benefitText}>{suggestion.benefit}</Text>
          </View>
        )}

        <TouchableOpacity
          style={[
            styles.actionButton,
            { backgroundColor: suggestion.color }
          ]}
          onPress={onAction}
        >
          <Text style={styles.actionText}>{suggestion.action.label}</Text>
          <MaterialCommunityIcons 
            name="arrow-right" 
            size={20} 
            color={COLORS.white} 
          />
        </TouchableOpacity>
      </Card>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginHorizontal: SPACING.lg,
    marginTop: SPACING.lg,
  },
  card: {
    padding: SPACING.lg,
    position: 'relative',
  },
  dismissButton: {
    position: 'absolute',
    top: SPACING.sm,
    right: SPACING.sm,
    padding: SPACING.xs,
  },
  header: {
    flexDirection: 'row',
    gap: SPACING.md,
    marginBottom: SPACING.md,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: BORDER_RADIUS.md,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerText: {
    flex: 1,
    paddingRight: SPACING.xl,
  },
  title: {
    fontSize: FONT_SIZES.lg,
    fontWeight: '600',
    color: COLORS.gray[900],
    marginBottom: SPACING.xs,
  },
  description: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
    lineHeight: FONT_SIZES.sm * 1.4,
  },
  benefitContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.xs,
    backgroundColor: COLORS.gray[50],
    padding: SPACING.sm,
    borderRadius: BORDER_RADIUS.sm,
    marginBottom: SPACING.md,
  },
  benefitText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[700],
    fontWeight: '500',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: SPACING.sm,
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
  },
  actionText: {
    fontSize: FONT_SIZES.md,
    fontWeight: '600',
    color: COLORS.white,
  },
});