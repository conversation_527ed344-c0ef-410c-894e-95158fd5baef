import React, { useState } from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
} from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { dataService } from '../../services/dataService';
import { FormInput, EmailInput, PhoneInput } from '../common/FormInput';
import { useFormValidation, commonValidations } from '../../hooks/useFormValidation';
import { ErrorMessage } from '../common/ErrorMessage';
import { COMPONENTS, TYPOGRAPHY, SHADOWS } from '../../constants/design-system';
import { COLORS, SPACING, BORDER_RADIUS } from '../../constants';

interface CreateClientModalProps {
  visible: boolean;
  onClose: () => void;
  onClientCreated: (client: any) => void;
  userId: string;
}

export const CreateClientModal: React.FC<CreateClientModalProps> = ({
  visible,
  onClose,
  onClientCreated,
  userId,
}) => {
  const [loading, setLoading] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  const { values, errors, touched, isValid, handleChange, handleBlur, validate, reset } =
    useFormValidation(
      {
        full_name: '',
        phone: '',
        email: '',
        allergies: '',
        preferences: '',
      },
      {
        full_name: commonValidations.name,
        email: { ...commonValidations.email, required: false },
        phone: commonValidations.phone,
      }
    );

  const handleSubmit = async () => {
    setSubmitError(null);

    if (!validate()) {
      return;
    }

    setLoading(true);
    try {
      const newClient = {
        ...values,
        user_id: userId,
        allergies: values.allergies
          ? values.allergies
              .split(',')
              .map((a: string) => a.trim())
              .filter((a: string) => a)
          : [],
      };

      const createdClient = await dataService.clients.create({
        ...newClient,
        fullName: values.full_name,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      } as any);
      onClientCreated(createdClient);
      reset();
      onClose();
    } catch (error) {
      setSubmitError('No se pudo crear el cliente. Por favor, intenta de nuevo.');
      console.error('Error creating client:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    reset();
    setSubmitError(null);
    onClose();
  };

  return (
    <Modal visible={visible} animationType="slide" transparent={true} onRequestClose={handleClose}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.container}
      >
        <View style={styles.modalContent}>
          <View style={styles.header}>
            <Text style={styles.title}>Nuevo Cliente</Text>
            <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
              <MaterialCommunityIcons name="close" size={24} color="#666" />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.form} showsVerticalScrollIndicator={false}>
            {submitError && (
              <ErrorMessage
                message={submitError}
                type="error"
                onDismiss={() => setSubmitError(null)}
                style={{ marginBottom: 16 }}
              />
            )}

            <FormInput
              label="Nombre Completo"
              value={values.full_name}
              onChangeText={handleChange('full_name')}
              onBlur={handleBlur('full_name')}
              placeholder="Ej: María García"
              error={touched.full_name ? errors.full_name : undefined}
              required
              icon="person"
            />

            <PhoneInput
              label="Teléfono"
              value={values.phone}
              onChangeText={handleChange('phone')}
              onBlur={handleBlur('phone')}
              placeholder="Ej: +34 600 000 000"
              error={touched.phone ? errors.phone : undefined}
            />

            <EmailInput
              label="Email"
              value={values.email}
              onChangeText={handleChange('email')}
              onBlur={handleBlur('email')}
              placeholder="Ej: <EMAIL>"
              error={touched.email ? errors.email : undefined}
            />

            <FormInput
              label="Alergias"
              value={values.allergies}
              onChangeText={handleChange('allergies')}
              placeholder="Separa múltiples alergias con comas"
              hint="Ej: PPD, Amoniaco, Látex"
              multiline
              numberOfLines={2}
              icon="alert-circle"
            />

            <FormInput
              label="Preferencias"
              value={values.preferences}
              onChangeText={handleChange('preferences')}
              placeholder="Notas sobre preferencias del cliente"
              multiline
              numberOfLines={3}
              icon="note-text"
            />
          </ScrollView>

          <View style={styles.footer}>
            <TouchableOpacity style={[styles.button, styles.cancelButton]} onPress={handleClose}>
              <Text style={styles.cancelButtonText}>Cancelar</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.button,
                styles.submitButton,
                (!isValid || loading) && styles.disabledButton,
              ]}
              onPress={handleSubmit}
              disabled={!isValid || loading}
            >
              {loading ? (
                <ActivityIndicator size="small" color="#fff" />
              ) : (
                <Text style={styles.submitButtonText}>Crear Cliente</Text>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: COMPONENTS.modal.overlay.backgroundColor,
  },
  modalContent: {
    ...COMPONENTS.modal.content,
    borderTopLeftRadius: BORDER_RADIUS.xxl,
    borderTopRightRadius: BORDER_RADIUS.xxl,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
    maxHeight: '90%',
    padding: 0,
    ...SHADOWS.xl,
  },
  header: {
    ...COMPONENTS.header.base,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.lg,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surface,
  },
  title: {
    ...COMPONENTS.header.title,
    fontSize: TYPOGRAPHY.size['2xl'],
  },
  closeButton: {
    padding: SPACING.xs,
  },
  form: {
    padding: SPACING.lg,
  },
  inputGroup: {
    marginBottom: SPACING.lg,
  },
  label: {
    fontSize: TYPOGRAPHY.size.sm,
    fontWeight: '500' as const,
    color: COLORS.textSecondary,
    marginBottom: SPACING.sm,
  },
  input: {
    ...COMPONENTS.input.base,
  },
  textArea: {
    minHeight: 60,
    textAlignVertical: 'top',
  },
  hint: {
    fontSize: TYPOGRAPHY.size.xs,
    color: COLORS.textTertiary,
    marginTop: SPACING.xs,
  },
  footer: {
    flexDirection: 'row',
    padding: SPACING.lg,
    borderTopWidth: 1,
    borderTopColor: COLORS.surface,
    gap: SPACING.md,
  },
  button: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cancelButton: {
    ...COMPONENTS.button.ghost,
    backgroundColor: COLORS.surface,
  },
  submitButton: {
    ...COMPONENTS.button.primary,
  },
  disabledButton: {
    opacity: 0.6,
  },
  cancelButtonText: {
    color: COLORS.textSecondary,
    fontSize: TYPOGRAPHY.size.base,
    fontWeight: '500' as const,
  },
  submitButtonText: {
    color: COLORS.white,
    fontSize: TYPOGRAPHY.size.base,
    fontWeight: '600' as const,
  },
});
