import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { BottomTabBarProps } from '@react-navigation/bottom-tabs';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { COLORS } from '../../constants';
import { COMPONENTS } from '../../constants/design-system';

export const CustomTabBar: React.FC<BottomTabBarProps> = ({
  state,
  descriptors,
  navigation,
}) => {
  const insets = useSafeAreaInsets();

  return (
    <View style={[styles.container, { paddingBottom: insets.bottom }]}>
      <View style={styles.content}>
        {state.routes.map((route, index) => {
          const { options } = descriptors[route.key];
          const label =
            options.tabBarLabel !== undefined
              ? options.tabBarLabel
              : options.title !== undefined
              ? options.title
              : route.name;

          const isFocused = state.index === index;

          const onPress = () => {
            const event = navigation.emit({
              type: 'tabPress',
              target: route.key,
              canPreventDefault: true,
            });

            if (!isFocused && !event.defaultPrevented) {
              navigation.navigate(route.name);
            }
          };

          const onLongPress = () => {
            navigation.emit({
              type: 'tabLongPress',
              target: route.key,
            });
          };

          const color = isFocused
            ? COMPONENTS.bottomTab.icon.activeColor
            : COMPONENTS.bottomTab.icon.color;

          return (
            <TouchableOpacity
              key={index}
              accessibilityRole="button"
              accessibilityState={isFocused ? { selected: true } : {}}
              accessibilityLabel={options.tabBarAccessibilityLabel}
              testID={options.tabBarTestID}
              onPress={onPress}
              onLongPress={onLongPress}
              style={styles.tab}
            >
              {options.tabBarIcon && (
                <View style={styles.iconContainer}>
                  {options.tabBarIcon({ focused: isFocused, color, size: COMPONENTS.bottomTab.icon.size })}
                </View>
              )}
              <Text
                style={[
                  styles.label,
                  {
                    color,
                    fontWeight: isFocused ? '600' : '400',
                  },
                ]}
              >
                {label as string}
              </Text>
            </TouchableOpacity>
          );
        })}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    ...COMPONENTS.bottomTab.bar,
  },
  content: {
    flexDirection: 'row',
    height: 56,
  },
  tab: {
    ...COMPONENTS.bottomTab.item,
  },
  iconContainer: {
    marginBottom: 2,
  },
  label: {
    fontSize: COMPONENTS.bottomTab.label.fontSize,
    textAlign: 'center',
  },
});