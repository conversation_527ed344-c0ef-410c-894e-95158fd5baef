import React, { useState, useEffect } from 'react';
import {
  Modal,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import Toast from 'react-native-toast-message';
// import DateTimePicker from '@react-native-community/datetimepicker';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { COLORS, SPACING, BORDER_RADIUS } from '../../constants';
import { COMPONENTS, TYPOGRAPHY, SHADOWS } from '../../constants/design-system';
import { dataService } from '../../services/dataService';
import { Client } from '../../types';
import { useNotifications } from '../../hooks/useNotifications';

interface CreateAppointmentModalProps {
  visible: boolean;
  onClose: () => void;
  onAppointmentCreated: (appointment: any) => void;
  selectedDate?: Date;
  appointment?: any;
  prefilledData?: any;
}

// Servicios por defecto si el usuario no tiene configurados
const DEFAULT_SERVICE_TYPES = [
  { label: 'Coloración', value: 'Coloración', duration: 120, color: '#7C3AED', category: 'color' },
  { label: 'Corte', value: 'Corte', duration: 60, color: '#3B82F6', category: 'cut' },
  {
    label: 'Tratamiento',
    value: 'Tratamiento',
    duration: 90,
    color: '#10B981',
    category: 'treatment',
  },
  { label: 'Mechas', value: 'Mechas', duration: 180, color: '#F59E0B', category: 'color' },
  { label: 'Peinado', value: 'Peinado', duration: 45, color: '#EC4899', category: 'styling' },
  { label: 'Otros', value: 'Otros', duration: 60, color: '#6B7280', category: 'styling' },
];

const TIME_SLOTS = Array.from({ length: 26 }, (_, i) => {
  const hour = Math.floor(i / 2) + 8;
  const minutes = i % 2 === 0 ? '00' : '30';
  return `${hour.toString().padStart(2, '0')}:${minutes}`;
});

export const CreateAppointmentModal: React.FC<CreateAppointmentModalProps> = ({
  visible,
  onClose,
  onAppointmentCreated,
  selectedDate = new Date(),
  appointment,
  prefilledData,
}) => {
  const [clients, setClients] = useState<Client[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredClients, setFilteredClients] = useState<Client[]>([]);
  const [showClientPicker, setShowClientPicker] = useState(false);
  const [loading, setLoading] = useState(false);
  const [userServices, setUserServices] = useState<any[]>([]);
  const { scheduleAppointmentReminder } = useNotifications();

  const [formData, setFormData] = useState({
    client_id: '',
    client_name: '',
    service_type: 'Coloración',
    date: selectedDate,
    time: '10:00',
    duration: 120,
    notes: '',
    status: 'scheduled',
  });

  useEffect(() => {
    if (appointment) {
      setFormData({
        ...appointment,
        date: new Date(appointment.date),
      });
    } else if (prefilledData) {
      // Usar datos pre-poblados desde CompletionStep
      const serviceTypes = userServices.length > 0 ? userServices : DEFAULT_SERVICE_TYPES;
      const service = serviceTypes.find(s => s.value === (prefilledData.service || 'Coloración'));
      setFormData({
        client_id: prefilledData.client?.id || '',
        client_name: prefilledData.client?.name || '',
        service_type: prefilledData.service || 'Coloración',
        date: prefilledData.date || selectedDate,
        time: '10:00',
        duration: service?.duration || 120,
        notes: prefilledData.notes || '',
        status: 'scheduled',
      });
      setSearchQuery(prefilledData.client?.name || '');
    } else {
      setFormData({
        client_id: '',
        client_name: '',
        service_type: 'Coloración',
        date: selectedDate,
        time: '10:00',
        duration: 120,
        notes: '',
        status: 'scheduled',
      });
    }
  }, [appointment, selectedDate, prefilledData]);

  useEffect(() => {
    loadClients();
    loadUserServices();
  }, []);

  useEffect(() => {
    if (searchQuery) {
      const filtered = clients.filter(client =>
        client.full_name.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredClients(filtered);
    } else {
      setFilteredClients(clients);
    }
  }, [searchQuery, clients]);

  const loadClients = async () => {
    try {
      const clientsData = await dataService.clients.getAll('1');
      if (clientsData) {
        setClients(clientsData as any);
        setFilteredClients(clientsData as any);
      }
    } catch (error) {
      console.error('Error loading clients:', error);
    }
  };

  const loadUserServices = async () => {
    try {
      // TODO: Cargar servicios del usuario actual
      const userData = await dataService.auth.getCurrentUser();
      if (userData?.custom_services && userData.custom_services.length > 0) {
        // Convertir servicios del usuario al formato esperado
        const services = (userData.custom_services || []) as any[];
        const formattedServices = services
          .filter(s => s.is_active)
          .map(s => ({
            label: s.name,
            value: s.name,
            duration: s.duration_minutes,
            color: s.color || '#6B7280',
            category: s.category,
          }));
        setUserServices(formattedServices);
      }
    } catch (error) {
      console.error('Error loading user services:', error);
    }
  };

  const handleServiceTypeChange = (serviceType: string) => {
    const serviceTypes = userServices.length > 0 ? userServices : DEFAULT_SERVICE_TYPES;
    const service = serviceTypes.find(s => s.value === serviceType);
    if (service) {
      setFormData({
        ...formData,
        service_type: serviceType,
        duration: service.duration,
      });
    }
  };

  const handleClientSelect = (client: Client) => {
    setFormData({
      ...formData,
      client_id: client.id,
      client_name: client.full_name,
    });
    setSearchQuery(client.full_name);
    setShowClientPicker(false);
  };

  const handleSubmit = async () => {
    if (!formData.client_id) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Selecciona un cliente',
        position: 'top',
      });
      return;
    }

    if (!formData.time) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Selecciona una hora',
        position: 'top',
      });
      return;
    }

    setLoading(true);
    try {
      const serviceTypes = userServices.length > 0 ? userServices : DEFAULT_SERVICE_TYPES;
      const service = serviceTypes.find(s => s.value === formData.service_type);
      const appointmentData = {
        ...formData,
        date: formData.date.toISOString().split('T')[0],
        color: service?.color || '#6B7280',
        id: appointment?.id || Date.now().toString(),
      };

      onAppointmentCreated(appointmentData);

      // Schedule notification reminder
      try {
        const userData = await dataService.auth.getCurrentUser();
        const reminderMinutes = userData?.notification_preferences?.reminderTimeMinutes || 30;

        if (userData?.notification_preferences?.appointmentReminder) {
          const appointmentDate = new Date(appointmentData.date);
          const [hours, minutes] = formData.time.split(':');
          appointmentDate.setHours(parseInt(hours), parseInt(minutes), 0, 0);

          await scheduleAppointmentReminder(
            {
              id: appointmentData.id,
              user_id: userData.id,
              client_id: formData.client_id,
              service_id: formData.service_type,
              date: appointmentDate.toISOString(),
              duration: formData.duration,
              status: 'scheduled',
              notes: formData.notes,
              reminder_sent: false,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            },
            reminderMinutes
          );
        }
      } catch (error) {
        console.error('Error scheduling appointment reminder:', error);
      }

      resetForm();
      onClose();
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'No se pudo crear la cita',
        position: 'top',
      });
      console.error('Error creating appointment:', error);
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      client_id: '',
      client_name: '',
      service_type: 'Coloración',
      date: selectedDate,
      time: '10:00',
      duration: 120,
      notes: '',
      status: 'scheduled',
    });
    setSearchQuery('');
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  return (
    <Modal visible={visible} animationType="slide" transparent={true} onRequestClose={handleClose}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.container}
      >
        <View style={styles.modalContent}>
          <View style={styles.header}>
            <Text style={styles.title}>{appointment ? 'Editar Cita' : 'Nueva Cita'}</Text>
            <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
              <MaterialCommunityIcons name="close" size={24} color="#666" />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.form} showsVerticalScrollIndicator={false}>
            {/* Cliente */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Cliente *</Text>
              <TouchableOpacity style={styles.input} onPress={() => setShowClientPicker(true)}>
                <Text style={[styles.inputText, !formData.client_name && styles.placeholderText]}>
                  {formData.client_name || 'Seleccionar cliente'}
                </Text>
                <MaterialCommunityIcons name="chevron-down" size={20} color={COLORS.gray[400]} />
              </TouchableOpacity>
            </View>

            {/* Tipo de servicio */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Servicio *</Text>
              <View style={styles.serviceGrid}>
                {(userServices.length > 0 ? userServices : DEFAULT_SERVICE_TYPES).map(service => (
                  <TouchableOpacity
                    key={service.value}
                    style={[
                      styles.serviceOption,
                      formData.service_type === service.value && styles.selectedService,
                      { borderColor: service.color },
                    ]}
                    onPress={() => handleServiceTypeChange(service.value)}
                  >
                    <View style={[styles.serviceColor, { backgroundColor: service.color }]} />
                    <Text
                      style={[
                        styles.serviceText,
                        formData.service_type === service.value && styles.selectedServiceText,
                      ]}
                    >
                      {service.label}
                    </Text>
                    <Text style={styles.serviceDuration}>{service.duration} min</Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Fecha */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Fecha *</Text>
              <TouchableOpacity style={styles.input}>
                <MaterialCommunityIcons name="calendar" size={20} color={COLORS.gray[600]} />
                <Text style={styles.inputText}>
                  {formData.date.toLocaleDateString('es-ES', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                  })}
                </Text>
              </TouchableOpacity>
            </View>

            {/* Hora */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Hora *</Text>
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                style={styles.timeSlots}
              >
                {TIME_SLOTS.map(time => (
                  <TouchableOpacity
                    key={time}
                    style={[styles.timeSlot, formData.time === time && styles.selectedTimeSlot]}
                    onPress={() => setFormData({ ...formData, time })}
                  >
                    <Text
                      style={[
                        styles.timeSlotText,
                        formData.time === time && styles.selectedTimeSlotText,
                      ]}
                    >
                      {time}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>

            {/* Duración */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Duración</Text>
              <View style={styles.durationContainer}>
                <TouchableOpacity
                  style={styles.durationButton}
                  onPress={() =>
                    setFormData({
                      ...formData,
                      duration: Math.max(30, formData.duration - 30),
                    })
                  }
                >
                  <MaterialCommunityIcons name="minus" size={20} color={COLORS.gray[600]} />
                </TouchableOpacity>
                <Text style={styles.durationText}>
                  {Math.floor(formData.duration / 60)}h {formData.duration % 60}min
                </Text>
                <TouchableOpacity
                  style={styles.durationButton}
                  onPress={() =>
                    setFormData({
                      ...formData,
                      duration: formData.duration + 30,
                    })
                  }
                >
                  <MaterialCommunityIcons name="plus" size={20} color={COLORS.gray[600]} />
                </TouchableOpacity>
              </View>
            </View>

            {/* Notas */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Notas</Text>
              <TextInput
                style={[styles.input, styles.textArea]}
                value={formData.notes}
                onChangeText={text => setFormData({ ...formData, notes: text })}
                placeholder="Notas adicionales..."
                placeholderTextColor="#999"
                multiline
                numberOfLines={3}
              />
            </View>
          </ScrollView>

          <View style={styles.footer}>
            <TouchableOpacity style={[styles.button, styles.cancelButton]} onPress={handleClose}>
              <Text style={styles.cancelButtonText}>Cancelar</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.button, styles.submitButton, loading && styles.disabledButton]}
              onPress={handleSubmit}
              disabled={loading}
            >
              <Text style={styles.submitButtonText}>
                {loading ? 'Guardando...' : appointment ? 'Actualizar' : 'Crear Cita'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Client Picker Modal */}
        {showClientPicker && (
          <Modal
            visible={showClientPicker}
            animationType="slide"
            transparent={true}
            onRequestClose={() => setShowClientPicker(false)}
          >
            <View style={styles.pickerContainer}>
              <View style={styles.pickerContent}>
                <View style={styles.pickerHeader}>
                  <Text style={styles.pickerTitle}>Seleccionar Cliente</Text>
                  <TouchableOpacity onPress={() => setShowClientPicker(false)}>
                    <MaterialCommunityIcons name="close" size={24} color="#666" />
                  </TouchableOpacity>
                </View>

                <TextInput
                  style={styles.searchInput}
                  placeholder="Buscar cliente..."
                  value={searchQuery}
                  onChangeText={setSearchQuery}
                  autoFocus
                />

                <ScrollView style={styles.clientList}>
                  {filteredClients.map(client => (
                    <TouchableOpacity
                      key={client.id}
                      style={styles.clientItem}
                      onPress={() => handleClientSelect(client)}
                    >
                      <Text style={styles.clientName}>{client.full_name}</Text>
                      {client.phone && <Text style={styles.clientPhone}>{client.phone}</Text>}
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              </View>
            </View>
          </Modal>
        )}
      </KeyboardAvoidingView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: COMPONENTS.modal.overlay.backgroundColor,
  },
  modalContent: {
    ...COMPONENTS.modal.content,
    borderTopLeftRadius: BORDER_RADIUS.xxl,
    borderTopRightRadius: BORDER_RADIUS.xxl,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
    maxHeight: '90%',
    padding: 0,
    ...SHADOWS.xl,
  },
  header: {
    ...COMPONENTS.header.base,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.lg,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surface,
  },
  title: {
    ...COMPONENTS.header.title,
    fontSize: TYPOGRAPHY.size['2xl'],
  },
  closeButton: {
    padding: SPACING.xs,
  },
  form: {
    padding: SPACING.lg,
  },
  inputGroup: {
    marginBottom: SPACING.lg,
  },
  label: {
    fontSize: TYPOGRAPHY.size.sm,
    fontWeight: '500' as const,
    color: COLORS.textSecondary,
    marginBottom: SPACING.sm,
  },
  input: {
    ...COMPONENTS.input.base,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  inputText: {
    fontSize: TYPOGRAPHY.size.base,
    color: COLORS.text,
    flex: 1,
  },
  placeholderText: {
    color: COLORS.textTertiary,
  },
  textArea: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  serviceGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.md,
  },
  serviceOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: SPACING.md,
    borderWidth: 2,
    borderRadius: BORDER_RADIUS.lg,
    borderColor: COLORS.gray[200],
    backgroundColor: COLORS.surface,
    minWidth: '47%',
  },
  selectedService: {
    backgroundColor: COLORS.accent,
    borderColor: COLORS.secondary,
  },
  serviceColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  serviceText: {
    fontSize: TYPOGRAPHY.size.sm,
    color: COLORS.textSecondary,
    flex: 1,
  },
  selectedServiceText: {
    fontWeight: '600' as const,
    color: COLORS.text,
  },
  serviceDuration: {
    fontSize: TYPOGRAPHY.size.xs,
    color: COLORS.textTertiary,
  },
  timeSlots: {
    flexDirection: 'row',
  },
  timeSlot: {
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.md,
    borderWidth: 1,
    borderColor: COLORS.gray[200],
    borderRadius: BORDER_RADIUS.full,
    marginRight: SPACING.sm,
    backgroundColor: COLORS.surface,
  },
  selectedTimeSlot: {
    backgroundColor: COLORS.secondary,
    borderColor: COLORS.secondary,
  },
  timeSlotText: {
    fontSize: TYPOGRAPHY.size.sm,
    color: COLORS.textSecondary,
  },
  selectedTimeSlotText: {
    color: COLORS.white,
    fontWeight: '500' as const,
  },
  durationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: SPACING.lg,
  },
  durationButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: COLORS.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  durationText: {
    fontSize: TYPOGRAPHY.size.base,
    fontWeight: '500' as const,
    color: COLORS.text,
    minWidth: 80,
    textAlign: 'center',
  },
  footer: {
    flexDirection: 'row',
    padding: SPACING.lg,
    borderTopWidth: 1,
    borderTopColor: COLORS.surface,
    gap: SPACING.md,
  },
  button: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cancelButton: {
    ...COMPONENTS.button.ghost,
    backgroundColor: COLORS.surface,
  },
  submitButton: {
    ...COMPONENTS.button.primary,
  },
  disabledButton: {
    opacity: 0.6,
  },
  cancelButtonText: {
    color: COLORS.textSecondary,
    fontSize: TYPOGRAPHY.size.base,
    fontWeight: '500' as const,
  },
  submitButtonText: {
    color: COLORS.white,
    fontSize: TYPOGRAPHY.size.base,
    fontWeight: '600' as const,
  },
  pickerContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: COMPONENTS.modal.overlay.backgroundColor,
  },
  pickerContent: {
    backgroundColor: COLORS.white,
    borderTopLeftRadius: BORDER_RADIUS.xxl,
    borderTopRightRadius: BORDER_RADIUS.xxl,
    height: '70%',
  },
  pickerHeader: {
    ...COMPONENTS.header.base,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.lg,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surface,
  },
  pickerTitle: {
    ...COMPONENTS.header.title,
    fontSize: TYPOGRAPHY.size.lg,
  },
  searchInput: {
    ...COMPONENTS.input.base,
    margin: SPACING.lg,
    marginBottom: SPACING.md,
  },
  clientList: {
    flex: 1,
    paddingHorizontal: SPACING.lg,
  },
  clientItem: {
    paddingVertical: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surface,
  },
  clientName: {
    fontSize: TYPOGRAPHY.size.base,
    fontWeight: '500' as const,
    color: COLORS.text,
  },
  clientPhone: {
    fontSize: TYPOGRAPHY.size.sm,
    color: COLORS.textSecondary,
    marginTop: SPACING.xs,
  },
});
