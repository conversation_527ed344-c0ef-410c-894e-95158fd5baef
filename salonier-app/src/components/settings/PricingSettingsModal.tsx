import React, { useState } from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  TextInput,
} from 'react-native';
import Toast from 'react-native-toast-message';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import Slider from '@react-native-community/slider';
import { COLORS, SPACING, FONT_SIZES, BORDER_RADIUS, SHADOWS } from '../../constants';
import { COMPONENTS, TYPOGRAPHY } from '../../constants/design-system';
import Card from '../common/Card';

interface PricingSettings {
  default_markup_percentage: number;
  round_to_nearest: number;
  include_tax_in_price: boolean;
  discount_policy: 'none' | 'loyalty' | 'custom';
  max_discount_percentage: number;
  minimum_service_price: number;
}

interface PricingSettingsModalProps {
  visible: boolean;
  onClose: () => void;
  onSave: (settings: PricingSettings) => void;
}

const ROUND_TO_OPTIONS = [0, 1, 5, 10];

export const PricingSettingsModal: React.FC<PricingSettingsModalProps> = ({
  visible,
  onClose,
  onSave,
}) => {
  const [settings, setSettings] = useState<PricingSettings>({
    default_markup_percentage: 300,
    round_to_nearest: 5,
    include_tax_in_price: true,
    discount_policy: 'none',
    max_discount_percentage: 20,
    minimum_service_price: 15,
  });

  const handleSave = () => {
    if (settings.minimum_service_price < 0) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'El precio mínimo no puede ser negativo',
      });
      return;
    }

    onSave(settings);
    Toast.show({
      type: 'success',
      text1: 'Éxito',
      text2: 'Configuración de precios guardada',
    });
    onClose();
  };

  const formatPrice = (price: number): string => {
    return `${price.toFixed(2)}€`;
  };

  const calculateExample = (): { cost: number; final: number } => {
    const cost = 10;
    const withMarkup = cost * (1 + settings.default_markup_percentage / 100);
    let final = withMarkup;

    if (settings.round_to_nearest > 0) {
      final = Math.round(final / settings.round_to_nearest) * settings.round_to_nearest;
    }

    final = Math.max(final, settings.minimum_service_price);

    return { cost, final };
  };

  const example = calculateExample();

  return (
    <Modal visible={visible} animationType="slide" onRequestClose={onClose}>
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Text style={styles.cancelText}>Cancelar</Text>
          </TouchableOpacity>
          <Text style={styles.title}>Precios y Márgenes</Text>
          <TouchableOpacity onPress={handleSave} style={styles.saveButton}>
            <Text style={styles.saveText}>Guardar</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <Card style={styles.section}>
            <Text style={styles.sectionTitle}>
              <MaterialCommunityIcons name="percent" size={20} color={COLORS.primary} /> Margen de
              Ganancia
            </Text>

            <View style={styles.sliderContainer}>
              <Text style={styles.label}>Margen por defecto</Text>
              <View style={styles.sliderRow}>
                <Text style={styles.sliderValue}>0%</Text>
                <Slider
                  style={styles.slider}
                  minimumValue={0}
                  maximumValue={500}
                  step={10}
                  value={settings.default_markup_percentage}
                  onValueChange={value =>
                    setSettings({ ...settings, default_markup_percentage: value })
                  }
                  minimumTrackTintColor={COLORS.primary}
                  maximumTrackTintColor={COLORS.gray[400]}
                />
                <Text style={styles.sliderValue}>500%</Text>
              </View>
              <Text style={styles.currentValue}>{settings.default_markup_percentage}%</Text>
            </View>

            <View style={styles.exampleBox}>
              <Text style={styles.exampleTitle}>Ejemplo:</Text>
              <Text style={styles.exampleText}>
                Costo: {formatPrice(example.cost)} → Precio final: {formatPrice(example.final)}
              </Text>
            </View>
          </Card>

          <Card style={styles.section}>
            <Text style={styles.sectionTitle}>
              <MaterialCommunityIcons name="cash-multiple" size={20} color={COLORS.primary} />{' '}
              Redondeo de Precios
            </Text>

            <Text style={styles.label}>Redondear a múltiplos de:</Text>
            <View style={styles.optionsRow}>
              {ROUND_TO_OPTIONS.map(option => (
                <TouchableOpacity
                  key={option}
                  style={[
                    styles.option,
                    settings.round_to_nearest === option && styles.optionSelected,
                  ]}
                  onPress={() => setSettings({ ...settings, round_to_nearest: option })}
                >
                  <Text
                    style={[
                      styles.optionText,
                      settings.round_to_nearest === option && styles.optionTextSelected,
                    ]}
                  >
                    {option === 0 ? 'Sin redondeo' : `${option}€`}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </Card>

          <Card style={styles.section}>
            <Text style={styles.sectionTitle}>
              <MaterialCommunityIcons name="tag-outline" size={20} color={COLORS.primary} /> Precio
              Mínimo
            </Text>

            <View style={styles.inputRow}>
              <Text style={styles.label}>Precio mínimo por servicio</Text>
              <TextInput
                style={styles.priceInput}
                value={settings.minimum_service_price.toString()}
                onChangeText={text => {
                  const value = parseFloat(text) || 0;
                  setSettings({ ...settings, minimum_service_price: value });
                }}
                keyboardType="decimal-pad"
                placeholder="0"
              />
              <Text style={styles.currency}>€</Text>
            </View>
          </Card>

          <Card style={styles.section}>
            <Text style={styles.sectionTitle}>
              <MaterialCommunityIcons name="sale" size={20} color={COLORS.primary} /> Política de
              Descuentos
            </Text>

            <View style={styles.discountOptions}>
              <TouchableOpacity
                style={[
                  styles.discountOption,
                  settings.discount_policy === 'none' && styles.discountOptionSelected,
                ]}
                onPress={() => setSettings({ ...settings, discount_policy: 'none' })}
              >
                <MaterialCommunityIcons
                  name={settings.discount_policy === 'none' ? 'radiobox-marked' : 'radiobox-blank'}
                  size={24}
                  color={settings.discount_policy === 'none' ? COLORS.primary : COLORS.gray[500]}
                />
                <Text style={styles.discountOptionText}>Sin descuentos</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.discountOption,
                  settings.discount_policy === 'loyalty' && styles.discountOptionSelected,
                ]}
                onPress={() => setSettings({ ...settings, discount_policy: 'loyalty' })}
              >
                <MaterialCommunityIcons
                  name={
                    settings.discount_policy === 'loyalty' ? 'radiobox-marked' : 'radiobox-blank'
                  }
                  size={24}
                  color={settings.discount_policy === 'loyalty' ? COLORS.primary : COLORS.gray[500]}
                />
                <Text style={styles.discountOptionText}>Descuentos por fidelidad</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.discountOption,
                  settings.discount_policy === 'custom' && styles.discountOptionSelected,
                ]}
                onPress={() => setSettings({ ...settings, discount_policy: 'custom' })}
              >
                <MaterialCommunityIcons
                  name={
                    settings.discount_policy === 'custom' ? 'radiobox-marked' : 'radiobox-blank'
                  }
                  size={24}
                  color={settings.discount_policy === 'custom' ? COLORS.primary : COLORS.gray[500]}
                />
                <Text style={styles.discountOptionText}>Descuentos personalizados</Text>
              </TouchableOpacity>
            </View>

            {settings.discount_policy !== 'none' && (
              <View style={styles.maxDiscountContainer}>
                <Text style={styles.label}>Descuento máximo permitido</Text>
                <View style={styles.sliderRow}>
                  <Text style={styles.sliderValue}>0%</Text>
                  <Slider
                    style={styles.slider}
                    minimumValue={0}
                    maximumValue={50}
                    step={5}
                    value={settings.max_discount_percentage}
                    onValueChange={value =>
                      setSettings({ ...settings, max_discount_percentage: value })
                    }
                    minimumTrackTintColor={COLORS.primary}
                    maximumTrackTintColor={COLORS.gray[400]}
                  />
                  <Text style={styles.sliderValue}>50%</Text>
                </View>
                <Text style={styles.currentValue}>{settings.max_discount_percentage}%</Text>
              </View>
            )}
          </Card>

          <View style={styles.infoBox}>
            <MaterialCommunityIcons name="information" size={20} color={COLORS.primary} />
            <Text style={styles.infoText}>
              Estos valores se aplicarán por defecto a nuevos servicios. Puedes ajustar precios
              individualmente en cada servicio.
            </Text>
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    ...COMPONENTS.header.base,
    paddingHorizontal: SPACING.md,
    paddingTop: 60,
    paddingBottom: SPACING.md,
    backgroundColor: COLORS.white,
    ...SHADOWS.sm,
  },
  closeButton: {
    padding: SPACING.xs,
  },
  cancelText: {
    color: COLORS.gray[500],
    fontSize: FONT_SIZES.md,
  },
  title: {
    ...COMPONENTS.header.title,
    fontSize: TYPOGRAPHY.size.lg,
  },
  saveButton: {
    padding: SPACING.xs,
  },
  saveText: {
    color: COLORS.primary,
    fontSize: TYPOGRAPHY.size.base,
    fontWeight: '600' as const,
  },
  content: {
    flex: 1,
    padding: SPACING.md,
  },
  section: {
    ...COMPONENTS.card.base,
    marginBottom: SPACING.md,
  },
  sectionTitle: {
    fontSize: TYPOGRAPHY.size.base,
    fontWeight: '600' as const,
    color: COLORS.text,
    marginBottom: SPACING.md,
  },
  label: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
    marginBottom: SPACING.xs,
  },
  sliderContainer: {
    marginVertical: SPACING.sm,
  },
  sliderRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: SPACING.xs,
  },
  slider: {
    flex: 1,
    height: 40,
    marginHorizontal: SPACING.sm,
  },
  sliderValue: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.gray[600],
    width: 40,
    textAlign: 'center',
  },
  currentValue: {
    fontSize: FONT_SIZES.xl,
    fontWeight: 'bold',
    color: COLORS.primary,
    textAlign: 'center',
    marginTop: SPACING.xs,
  },
  exampleBox: {
    backgroundColor: COLORS.surface,
    padding: SPACING.sm,
    borderRadius: BORDER_RADIUS.lg,
    marginTop: SPACING.sm,
  },
  exampleTitle: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.gray[600],
    marginBottom: SPACING.xs,
  },
  exampleText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[900],
    fontWeight: '500',
  },
  optionsRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.sm,
  },
  option: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: COMPONENTS.button.primary.borderRadius,
    borderWidth: 1,
    borderColor: COLORS.gray[300],
    backgroundColor: COLORS.white,
  },
  optionSelected: {
    borderColor: COLORS.secondary,
    backgroundColor: COLORS.accent,
  },
  optionText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[900],
  },
  optionTextSelected: {
    color: COLORS.secondary,
    fontWeight: '600' as const,
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  priceInput: {
    flex: 1,
    height: 40,
    ...COMPONENTS.input.base,
    borderWidth: 1,
    borderColor: COLORS.gray[300],
    marginLeft: SPACING.sm,
    textAlign: 'right',
  },
  currency: {
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[900],
    marginLeft: SPACING.xs,
  },
  discountOptions: {
    gap: SPACING.sm,
  },
  discountOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.sm,
    gap: SPACING.sm,
  },
  discountOptionSelected: {
    opacity: 1,
  },
  discountOptionText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[900],
  },
  maxDiscountContainer: {
    marginTop: SPACING.md,
    paddingTop: SPACING.md,
    borderTopWidth: 1,
    borderTopColor: COLORS.gray[300],
  },
  infoBox: {
    flexDirection: 'row',
    backgroundColor: COLORS.accent,
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.lg,
    marginTop: SPACING.sm,
    marginBottom: SPACING.xl,
    gap: SPACING.sm,
  },
  infoText: {
    flex: 1,
    fontSize: FONT_SIZES.sm,
    color: COLORS.primary,
    lineHeight: 20,
  },
});
