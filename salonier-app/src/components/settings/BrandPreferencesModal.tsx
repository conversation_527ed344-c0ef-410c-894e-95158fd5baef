import React, { useState, useEffect } from 'react';
import {
  Modal,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
} from 'react-native';
import Toast from 'react-native-toast-message';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { COLORS, SPACING, FONT_SIZES, BORDER_RADIUS, SHADOWS } from '../../constants';
import { COMPONENTS, TYPOGRAPHY } from '../../constants/design-system';
import { Brand } from '../../types';
import Card from '../common/Card';

interface BrandPreferencesModalProps {
  visible: boolean;
  onClose: () => void;
  preferredBrands: Brand[];
  onSave: (brands: Brand[]) => void;
}

// Importar las mismas marcas que usa el conversor
const BRANDS_DATA = {
  "L'Oréal": {
    lines: ['<PERSON><PERSON><PERSON>', 'INOA', 'Dia Light', '<PERSON>a Rich<PERSON>e', 'Cover 5'],
    description: 'Sistema decimal X.YZ',
  },
  Wella: {
    lines: ['Koleston Perfect', 'Illumina Color', 'Color Touch', 'Blondor'],
    description: 'Sistema X/YZ con barra',
  },
  Schwarzkopf: {
    lines: ['Igora Royal', 'Igora Vibrance', 'BlondMe', 'Essensity'],
    description: 'Sistema X-YZ con guión',
  },
  Revlon: {
    lines: ['Revlonissimo', 'Young Color', 'Nutri Color'],
    description: 'Sistema decimal',
  },
  Alfaparf: {
    lines: ['Evolution', 'Color Wear', 'Precious Nature'],
    description: 'Sistema decimal',
  },
  Salerm: {
    lines: ['Salermvison', 'Salerm Color', 'HD Colors'],
    description: 'Sistema decimal',
  },
  Matrix: {
    lines: ['SoColor', 'Color Sync', 'Light Master'],
    description: 'Sistema alfanumérico',
  },
  Redken: {
    lines: ['Shades EQ', 'Color Fusion', 'Chromatics'],
    description: 'Sistema alfanumérico',
  },
  Goldwell: {
    lines: ['Topchic', 'Colorance', 'Nectaya'],
    description: 'Sistema X|Y con barra',
  },
  Joico: {
    lines: ['Vero K-PAK', 'LumiShine', 'Color Intensity'],
    description: 'Sistema alfanumérico',
  },
};

export const BrandPreferencesModal: React.FC<BrandPreferencesModalProps> = ({
  visible,
  onClose,
  preferredBrands,
  onSave,
}) => {
  const [localBrands, setLocalBrands] = useState<Brand[]>(preferredBrands);
  const [searchQuery, setSearchQuery] = useState('');
  const [showAddBrand, setShowAddBrand] = useState(false);
  const [allBrands, setAllBrands] = useState<Brand[]>([]);
  const [selectedLines, setSelectedLines] = useState<{ [brandId: string]: string[] }>({});

  useEffect(() => {
    loadAllBrands();
    // Inicializar líneas seleccionadas
    const lines: { [brandId: string]: string[] } = {};
    preferredBrands.forEach(brand => {
      lines[brand.id] = brand.product_lines || [];
    });
    setSelectedLines(lines);
  }, [preferredBrands]);

  const loadAllBrands = async () => {
    try {
      // Por ahora usamos los datos hardcodeados
      const brands: Brand[] = Object.keys(BRANDS_DATA).map((name, index) => ({
        id: `brand_${index + 1}`,
        name,
        country: 'ES',
        is_premium: true,
        created_at: new Date().toISOString(),
        product_lines: BRANDS_DATA[name as keyof typeof BRANDS_DATA].lines,
      }));
      setAllBrands(brands);
    } catch (error) {
      console.error('Error loading brands:', error);
    }
  };

  const handleAddBrand = (brand: Brand) => {
    if (localBrands.some(b => b.id === brand.id)) {
      Toast.show({
        type: 'warning',
        text1: 'Marca ya añadida',
        text2: 'Esta marca ya está en tu lista de preferidas',
      });
      return;
    }
    setLocalBrands([...localBrands, brand]);
    setSelectedLines({ ...selectedLines, [brand.id]: [] });
  };

  const handleRemoveBrand = (brandId: string) => {
    setLocalBrands(localBrands.filter(b => b.id !== brandId));
    const newSelectedLines = { ...selectedLines };
    delete newSelectedLines[brandId];
    setSelectedLines(newSelectedLines);
    Toast.show({
      type: 'success',
      text1: 'Marca eliminada',
      text2: 'La marca ha sido eliminada de tus preferidas',
    });
  };

  const handleToggleLine = (brandId: string, line: string) => {
    const currentLines = selectedLines[brandId] || [];
    const newLines = currentLines.includes(line)
      ? currentLines.filter(l => l !== line)
      : [...currentLines, line];

    setSelectedLines({ ...selectedLines, [brandId]: newLines });
  };

  const handleSave = () => {
    // Actualizar las marcas con las líneas seleccionadas
    const brandsWithLines = localBrands.map(brand => ({
      ...brand,
      product_lines: selectedLines[brand.id] || [],
    }));
    onSave(brandsWithLines);
    onClose();
  };

  const filteredBrands = allBrands.filter(
    brand =>
      brand.name.toLowerCase().includes(searchQuery.toLowerCase()) &&
      !localBrands.some(b => b.id === brand.id)
  );

  const moveBrandUp = (index: number) => {
    if (index === 0) return;
    const newBrands = [...localBrands];
    [newBrands[index - 1], newBrands[index]] = [newBrands[index], newBrands[index - 1]];
    setLocalBrands(newBrands);
  };

  const moveBrandDown = (index: number) => {
    if (index === localBrands.length - 1) return;
    const newBrands = [...localBrands];
    [newBrands[index], newBrands[index + 1]] = [newBrands[index + 1], newBrands[index]];
    setLocalBrands(newBrands);
  };

  if (showAddBrand) {
    return (
      <Modal
        visible={visible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowAddBrand(false)}
      >
        <View style={styles.container}>
          <View style={styles.modalContent}>
            <View style={styles.header}>
              <TouchableOpacity onPress={() => setShowAddBrand(false)}>
                <MaterialCommunityIcons name="arrow-left" size={24} color={COLORS.gray[700]} />
              </TouchableOpacity>
              <Text style={styles.title}>Añadir Marca</Text>
              <View style={{ width: 24 }} />
            </View>

            <View style={styles.searchContainer}>
              <MaterialCommunityIcons name="magnify" size={20} color={COLORS.gray[400]} />
              <TextInput
                style={styles.searchInput}
                placeholder="Buscar marca..."
                placeholderTextColor={COLORS.gray[400]}
                value={searchQuery}
                onChangeText={setSearchQuery}
                autoCorrect={false}
              />
              {searchQuery.length > 0 && (
                <TouchableOpacity onPress={() => setSearchQuery('')}>
                  <MaterialCommunityIcons name="close-circle" size={20} color={COLORS.gray[400]} />
                </TouchableOpacity>
              )}
            </View>

            <ScrollView style={styles.brandList}>
              {filteredBrands.map(brand => (
                <TouchableOpacity
                  key={brand.id}
                  style={styles.brandItem}
                  onPress={() => {
                    handleAddBrand(brand);
                    setShowAddBrand(false);
                    setSearchQuery('');
                  }}
                >
                  <Text style={styles.brandName}>{brand.name}</Text>
                  <MaterialCommunityIcons name="plus" size={20} color={COLORS.primary} />
                </TouchableOpacity>
              ))}
              {filteredBrands.length === 0 && (
                <View style={styles.emptySearch}>
                  <Text style={styles.emptySearchText}>No se encontraron marcas</Text>
                </View>
              )}
            </ScrollView>
          </View>
        </View>
      </Modal>
    );
  }

  return (
    <Modal visible={visible} animationType="slide" transparent={true} onRequestClose={onClose}>
      <View style={styles.container}>
        <View style={styles.modalContent}>
          <View style={styles.header}>
            <TouchableOpacity onPress={onClose}>
              <MaterialCommunityIcons name="close" size={24} color={COLORS.gray[700]} />
            </TouchableOpacity>
            <Text style={styles.title}>Marcas Preferidas</Text>
            <TouchableOpacity onPress={handleSave}>
              <Text style={styles.saveText}>Guardar</Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.content}>
            {localBrands.length === 0 && (
              <View style={styles.emptyState}>
                <MaterialCommunityIcons name="palette-outline" size={48} color={COLORS.gray[400]} />
                <Text style={styles.emptyText}>No tienes marcas preferidas</Text>
                <Text style={styles.emptySubtext}>
                  Añade las marcas con las que trabajas habitualmente
                </Text>
              </View>
            )}

            {localBrands.map((brand, index) => (
              <Card key={brand.id} style={styles.brandCard} elevation="low">
                <View style={styles.brandHeader}>
                  <View style={styles.brandInfo}>
                    <Text style={styles.brandTitle}>{brand.name}</Text>
                    {selectedLines[brand.id]?.length > 0 && (
                      <Text style={styles.linesCount}>
                        {selectedLines[brand.id].length} líneas seleccionadas
                      </Text>
                    )}
                  </View>
                  <View style={styles.brandActions}>
                    <TouchableOpacity
                      onPress={() => moveBrandUp(index)}
                      disabled={index === 0}
                      style={styles.moveButton}
                    >
                      <MaterialCommunityIcons
                        name="chevron-up"
                        size={20}
                        color={index === 0 ? COLORS.gray[300] : COLORS.gray[600]}
                      />
                    </TouchableOpacity>
                    <TouchableOpacity
                      onPress={() => moveBrandDown(index)}
                      disabled={index === localBrands.length - 1}
                      style={styles.moveButton}
                    >
                      <MaterialCommunityIcons
                        name="chevron-down"
                        size={20}
                        color={
                          index === localBrands.length - 1 ? COLORS.gray[300] : COLORS.gray[600]
                        }
                      />
                    </TouchableOpacity>
                    <TouchableOpacity
                      onPress={() => handleRemoveBrand(brand.id)}
                      style={styles.removeButton}
                    >
                      <MaterialCommunityIcons name="close" size={20} color={COLORS.error} />
                    </TouchableOpacity>
                  </View>
                </View>

                {brand.product_lines && brand.product_lines.length > 0 && (
                  <View style={styles.linesSection}>
                    <Text style={styles.linesSectionTitle}>Líneas específicas (opcional):</Text>
                    <View style={styles.linesGrid}>
                      {brand.product_lines.map(line => (
                        <TouchableOpacity
                          key={line}
                          style={[
                            styles.lineChip,
                            selectedLines[brand.id]?.includes(line) && styles.lineChipSelected,
                          ]}
                          onPress={() => handleToggleLine(brand.id, line)}
                        >
                          <Text
                            style={[
                              styles.lineText,
                              selectedLines[brand.id]?.includes(line) && styles.lineTextSelected,
                            ]}
                          >
                            {line}
                          </Text>
                          {selectedLines[brand.id]?.includes(line) && (
                            <MaterialCommunityIcons name="check" size={16} color={COLORS.white} />
                          )}
                        </TouchableOpacity>
                      ))}
                    </View>
                    <Text style={styles.linesHint}>
                      Si no seleccionas ninguna, se incluirán todas las líneas
                    </Text>
                  </View>
                )}
              </Card>
            ))}

            <TouchableOpacity style={styles.addButton} onPress={() => setShowAddBrand(true)}>
              <MaterialCommunityIcons name="plus-circle" size={24} color={COLORS.primary} />
              <Text style={styles.addButtonText}>Añadir Marca</Text>
            </TouchableOpacity>

            <View style={styles.infoBox}>
              <MaterialCommunityIcons name="information" size={20} color={COLORS.info} />
              <Text style={styles.infoText}>
                Las marcas aparecerán en este orden en la formulación y el conversor. Puedes
                reordenarlas con las flechas.
              </Text>
            </View>
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COMPONENTS.modal.overlay.backgroundColor,
    justifyContent: 'flex-end',
  },
  modalContent: {
    ...COMPONENTS.modal.content,
    borderTopLeftRadius: BORDER_RADIUS.xxl,
    borderTopRightRadius: BORDER_RADIUS.xxl,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
    maxHeight: '90%',
    padding: 0,
  },
  header: {
    ...COMPONENTS.header.base,
    padding: SPACING.lg,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray[100],
  },
  title: {
    ...COMPONENTS.header.title,
    fontSize: TYPOGRAPHY.size.lg,
  },
  saveText: {
    fontSize: TYPOGRAPHY.size.base,
    fontWeight: '600' as const,
    color: COLORS.primary,
  },
  content: {
    padding: SPACING.lg,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: SPACING.xl * 2,
  },
  emptyText: {
    fontSize: FONT_SIZES.lg,
    fontWeight: '600',
    color: COLORS.gray[900],
    marginTop: SPACING.md,
  },
  emptySubtext: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
    marginTop: SPACING.xs,
    textAlign: 'center',
  },
  brandCard: {
    ...COMPONENTS.card.base,
    marginBottom: SPACING.md,
  },
  brandHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  brandInfo: {
    flex: 1,
  },
  brandTitle: {
    fontSize: TYPOGRAPHY.size.base,
    fontWeight: '600' as const,
    color: COLORS.text,
  },
  linesCount: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
    marginTop: 2,
  },
  brandActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.xs,
  },
  moveButton: {
    padding: SPACING.xs,
  },
  removeButton: {
    padding: SPACING.xs,
    marginLeft: SPACING.xs,
  },
  linesSection: {
    marginTop: SPACING.md,
    paddingTop: SPACING.md,
    borderTopWidth: 1,
    borderTopColor: COLORS.gray[100],
  },
  linesSectionTitle: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
    marginBottom: SPACING.sm,
  },
  linesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.sm,
  },
  lineChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: COMPONENTS.button.primary.borderRadius,
    backgroundColor: COLORS.surface,
    gap: SPACING.xs,
  },
  lineChipSelected: {
    backgroundColor: COLORS.secondary,
  },
  lineText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[700],
  },
  lineTextSelected: {
    color: COLORS.white,
  },
  linesHint: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.gray[500],
    marginTop: SPACING.sm,
    fontStyle: 'italic',
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    ...COMPONENTS.button.secondary,
    paddingVertical: SPACING.md,
    gap: SPACING.sm,
    marginTop: SPACING.md,
  },
  addButtonText: {
    fontSize: COMPONENTS.button.secondary.fontSize,
    fontWeight: COMPONENTS.button.secondary.fontWeight,
    color: COMPONENTS.button.secondary.color,
  },
  infoBox: {
    flexDirection: 'row',
    backgroundColor: COLORS.info + '10',
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.lg,
    marginTop: SPACING.lg,
    gap: SPACING.sm,
  },
  infoText: {
    flex: 1,
    fontSize: FONT_SIZES.sm,
    color: COLORS.info,
    lineHeight: 20,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.surface,
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.md,
    paddingHorizontal: SPACING.md,
    borderRadius: BORDER_RADIUS.lg,
  },
  searchInput: {
    flex: 1,
    paddingVertical: SPACING.sm,
    marginLeft: SPACING.sm,
    fontSize: TYPOGRAPHY.size.base,
    color: COLORS.text,
  },
  brandList: {
    padding: SPACING.lg,
  },
  brandItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.md,
    backgroundColor: COLORS.surface,
    borderRadius: BORDER_RADIUS.lg,
    marginBottom: SPACING.sm,
  },
  brandName: {
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[900],
  },
  emptySearch: {
    alignItems: 'center',
    paddingVertical: SPACING.xl,
  },
  emptySearchText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[500],
  },
});
