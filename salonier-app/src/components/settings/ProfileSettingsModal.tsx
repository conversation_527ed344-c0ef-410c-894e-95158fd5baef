import React, { useState, useEffect } from 'react';
import {
  Modal,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  Image,
} from 'react-native';
import Toast from 'react-native-toast-message';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { useCameraPermissions } from 'expo-camera';
import { COLORS, SPACING, FONT_SIZES, BORDER_RADIUS, SHADOWS } from '../../constants';
import { COMPONENTS, TYPOGRAPHY } from '../../constants/design-system';
import { User } from '../../types';

interface ProfileSettingsModalProps {
  visible: boolean;
  onClose: () => void;
  user: User;
  onSave: (updatedUser: Partial<User>) => void;
}

const SPECIALTIES = [
  'Coloración',
  'Mechas',
  'Balayage',
  'Corrección de color',
  'Decoloración',
  'Tratamie<PERSON>s',
  'Corte',
  'Peinados',
  'Alisados',
  'Extensiones',
];

export const ProfileSettingsModal: React.FC<ProfileSettingsModalProps> = ({
  visible,
  onClose,
  user,
  onSave,
}) => {
  const [cameraPermission, requestCameraPermission] = useCameraPermissions();

  const [formData, setFormData] = useState({
    full_name: user.full_name || '',
    email: user.email || '',
    phone: user.phone || '',
    salon_name: user.salon_name || '',
    specialties: user.specialties || [],
    profile_image: user.profile_image || '',
  });

  useEffect(() => {
    setFormData({
      full_name: user.full_name || '',
      email: user.email || '',
      phone: user.phone || '',
      salon_name: user.salon_name || '',
      specialties: user.specialties || [],
      profile_image: user.profile_image || '',
    });
  }, [user]);

  const handlePickImage = async () => {
    const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();

    if (permissionResult.granted === false) {
      Toast.show({
        type: 'warning',
        text1: 'Permiso denegado',
        text2: 'Necesitas dar permisos para acceder a tus fotos',
        position: 'top',
      });
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });

    if (!result.canceled) {
      setFormData({ ...formData, profile_image: result.assets[0].uri });
    }
  };

  const handleTakePhoto = async () => {
    if (!cameraPermission?.granted) {
      const permissionResult = await requestCameraPermission();
      if (!permissionResult.granted) {
        Toast.show({
          type: 'warning',
          text1: 'Permiso denegado',
          text2: 'Necesitas dar permisos para usar la cámara',
          position: 'top',
        });
        return;
      }
    }

    const result = await ImagePicker.launchCameraAsync({
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });

    if (!result.canceled) {
      setFormData({ ...formData, profile_image: result.assets[0].uri });
    }
  };

  const handleToggleSpecialty = (specialty: string) => {
    const currentSpecialties = formData.specialties || [];
    const newSpecialties = currentSpecialties.includes(specialty)
      ? currentSpecialties.filter(s => s !== specialty)
      : [...currentSpecialties, specialty];

    setFormData({ ...formData, specialties: newSpecialties });
  };

  const handleSave = () => {
    if (!formData.full_name.trim()) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'El nombre es requerido',
        position: 'top',
      });
      return;
    }

    if (!formData.email.trim()) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'El email es requerido',
        position: 'top',
      });
      return;
    }

    // Validar formato de email básico
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'El formato del email no es válido',
        position: 'top',
      });
      return;
    }

    onSave(formData);
    onClose();
  };

  const showImageOptions = () => {
    // TODO: Implement action sheet modal
    Toast.show({
      type: 'info',
      text1: 'Cambiar foto',
      text2: 'Toca aquí para tomar foto o elegir de galería',
      position: 'bottom',
      visibilityTime: 3000,
      onPress: () => {
        Toast.hide();
        handlePickImage();
      },
    });
  };

  return (
    <Modal visible={visible} animationType="slide" transparent={true} onRequestClose={onClose}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.container}
      >
        <View style={styles.modalContent}>
          <View style={styles.header}>
            <TouchableOpacity onPress={onClose}>
              <MaterialCommunityIcons name="close" size={24} color={COLORS.gray[700]} />
            </TouchableOpacity>
            <Text style={styles.title}>Editar Perfil</Text>
            <TouchableOpacity onPress={handleSave}>
              <Text style={styles.saveText}>Guardar</Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
            {/* Foto de perfil */}
            <View style={styles.profileImageSection}>
              <TouchableOpacity onPress={showImageOptions} style={styles.imageContainer}>
                {formData.profile_image ? (
                  <Image source={{ uri: formData.profile_image }} style={styles.profileImage} />
                ) : (
                  <View style={styles.placeholderImage}>
                    <MaterialCommunityIcons name="account" size={60} color={COLORS.gray[400]} />
                  </View>
                )}
                <View style={styles.editBadge}>
                  <MaterialCommunityIcons name="camera" size={16} color={COLORS.white} />
                </View>
              </TouchableOpacity>
            </View>

            {/* Información personal */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Información Personal</Text>

              <View style={styles.inputGroup}>
                <Text style={styles.label}>Nombre completo *</Text>
                <TextInput
                  style={styles.input}
                  value={formData.full_name}
                  onChangeText={text => setFormData({ ...formData, full_name: text })}
                  placeholder="Tu nombre completo"
                  placeholderTextColor={COLORS.gray[400]}
                />
              </View>

              <View style={styles.inputGroup}>
                <Text style={styles.label}>Email *</Text>
                <TextInput
                  style={styles.input}
                  value={formData.email}
                  onChangeText={text => setFormData({ ...formData, email: text })}
                  placeholder="<EMAIL>"
                  placeholderTextColor={COLORS.gray[400]}
                  keyboardType="email-address"
                  autoCapitalize="none"
                />
              </View>

              <View style={styles.inputGroup}>
                <Text style={styles.label}>Teléfono</Text>
                <TextInput
                  style={styles.input}
                  value={formData.phone}
                  onChangeText={text => setFormData({ ...formData, phone: text })}
                  placeholder="+34 600 000 000"
                  placeholderTextColor={COLORS.gray[400]}
                  keyboardType="phone-pad"
                />
              </View>
            </View>

            {/* Información profesional */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Información Profesional</Text>

              <View style={styles.inputGroup}>
                <Text style={styles.label}>Nombre del salón</Text>
                <TextInput
                  style={styles.input}
                  value={formData.salon_name}
                  onChangeText={text => setFormData({ ...formData, salon_name: text })}
                  placeholder="Nombre de tu salón o negocio"
                  placeholderTextColor={COLORS.gray[400]}
                />
              </View>

              <View style={styles.inputGroup}>
                <Text style={styles.label}>Especialidades</Text>
                <Text style={styles.hint}>Selecciona tus áreas de especialización</Text>
                <View style={styles.specialtiesGrid}>
                  {SPECIALTIES.map(specialty => (
                    <TouchableOpacity
                      key={specialty}
                      style={[
                        styles.specialtyChip,
                        formData.specialties?.includes(specialty) && styles.specialtyChipSelected,
                      ]}
                      onPress={() => handleToggleSpecialty(specialty)}
                    >
                      <Text
                        style={[
                          styles.specialtyText,
                          formData.specialties?.includes(specialty) && styles.specialtyTextSelected,
                        ]}
                      >
                        {specialty}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            </View>

            {/* Información de la cuenta */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Información de la Cuenta</Text>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Rol:</Text>
                <Text style={styles.infoValue}>
                  {user.role === 'stylist'
                    ? 'Estilista'
                    : user.role === 'salon_owner'
                      ? 'Dueño de Salón'
                      : 'Admin'}
                </Text>
              </View>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Miembro desde:</Text>
                <Text style={styles.infoValue}>
                  {new Date(user.created_at).toLocaleDateString('es-ES', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                  })}
                </Text>
              </View>
            </View>
          </ScrollView>
        </View>
      </KeyboardAvoidingView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COMPONENTS.modal.overlay.backgroundColor,
    justifyContent: 'flex-end',
  },
  modalContent: {
    ...COMPONENTS.modal.content,
    borderTopLeftRadius: BORDER_RADIUS.xxl,
    borderTopRightRadius: BORDER_RADIUS.xxl,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
    maxHeight: '90%',
    padding: 0,
    ...SHADOWS.xl,
  },
  header: {
    ...COMPONENTS.header.base,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.lg,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surface,
  },
  title: {
    ...COMPONENTS.header.title,
    fontSize: TYPOGRAPHY.size['2xl'],
  },
  saveText: {
    fontSize: TYPOGRAPHY.size.base,
    fontWeight: '600' as const,
    color: COLORS.secondary,
  },
  content: {
    padding: SPACING.lg,
  },
  profileImageSection: {
    alignItems: 'center',
    marginBottom: SPACING.xl,
  },
  imageContainer: {
    position: 'relative',
  },
  profileImage: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: COLORS.surface,
  },
  placeholderImage: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: COLORS.surface,
    alignItems: 'center',
    justifyContent: 'center',
  },
  editBadge: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: COLORS.secondary,
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 3,
    borderColor: COLORS.white,
  },
  section: {
    marginBottom: SPACING.xl,
  },
  sectionTitle: {
    fontSize: TYPOGRAPHY.size.lg,
    fontWeight: '600' as const,
    color: COLORS.text,
    marginBottom: SPACING.md,
  },
  inputGroup: {
    marginBottom: SPACING.md,
  },
  label: {
    fontSize: TYPOGRAPHY.size.sm,
    fontWeight: '500' as const,
    color: COLORS.textSecondary,
    marginBottom: SPACING.xs,
  },
  input: {
    ...COMPONENTS.input.base,
  },
  hint: {
    fontSize: TYPOGRAPHY.size.xs,
    color: COLORS.textTertiary,
    marginBottom: SPACING.sm,
  },
  specialtiesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.sm,
  },
  specialtyChip: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.full,
    backgroundColor: COLORS.surface,
    borderWidth: 2,
    borderColor: COLORS.gray[200],
  },
  specialtyChipSelected: {
    backgroundColor: COLORS.accent,
    borderColor: COLORS.secondary,
  },
  specialtyText: {
    fontSize: TYPOGRAPHY.size.sm,
    color: COLORS.textSecondary,
  },
  specialtyTextSelected: {
    color: COLORS.primary,
    fontWeight: '600' as const,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surface,
  },
  infoLabel: {
    fontSize: TYPOGRAPHY.size.sm,
    color: COLORS.textSecondary,
  },
  infoValue: {
    fontSize: TYPOGRAPHY.size.sm,
    color: COLORS.text,
    fontWeight: '500' as const,
  },
});
