import React, { useState, useEffect } from 'react';
import { Modal, View, Text, TouchableOpacity, ScrollView, StyleSheet, Switch } from 'react-native';
import Toast from 'react-native-toast-message';
import DateTimePicker from '@react-native-community/datetimepicker';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { COLORS, SPACING, FONT_SIZES, BORDER_RADIUS, SHADOWS } from '../../constants';
import { COMPONENTS, TYPOGRAPHY } from '../../constants/design-system';
import { BusinessHours, DaySchedule } from '../../types';
import Card from '../common/Card';

interface BusinessHoursModalProps {
  visible: boolean;
  onClose: () => void;
  businessHours?: BusinessHours;
  onSave: (hours: BusinessHours) => void;
}

const DAYS = [
  { key: 'monday', label: 'Lunes' },
  { key: 'tuesday', label: '<PERSON><PERSON>' },
  { key: 'wednesday', label: 'Miércoles' },
  { key: 'thursday', label: 'Jueves' },
  { key: 'friday', label: 'Viernes' },
  { key: 'saturday', label: 'S<PERSON>bado' },
  { key: 'sunday', label: 'Domingo' },
];

const DEFAULT_HOURS: BusinessHours = {
  monday: { is_open: true, open_time: '09:00', close_time: '19:00' },
  tuesday: { is_open: true, open_time: '09:00', close_time: '19:00' },
  wednesday: { is_open: true, open_time: '09:00', close_time: '19:00' },
  thursday: { is_open: true, open_time: '09:00', close_time: '19:00' },
  friday: { is_open: true, open_time: '09:00', close_time: '19:00' },
  saturday: { is_open: true, open_time: '09:00', close_time: '14:00' },
  sunday: { is_open: false },
  buffer_time_minutes: 15,
};

export const BusinessHoursModal: React.FC<BusinessHoursModalProps> = ({
  visible,
  onClose,
  businessHours = DEFAULT_HOURS,
  onSave,
}) => {
  const [hours, setHours] = useState<BusinessHours>(businessHours);
  const [showTimePicker, setShowTimePicker] = useState<{
    day: string;
    type: 'open' | 'close' | 'break_start' | 'break_end';
  } | null>(null);
  const [selectedTime, setSelectedTime] = useState(new Date());

  useEffect(() => {
    setHours(businessHours || DEFAULT_HOURS);
  }, [businessHours]);

  const handleToggleDay = (day: keyof BusinessHours) => {
    if (day === 'buffer_time_minutes') return;

    setHours({
      ...hours,
      [day]: {
        ...hours[day],
        is_open: !(hours[day] as DaySchedule).is_open,
      } as DaySchedule,
    });
  };

  const handleTimeSelect = (
    day: string,
    type: 'open' | 'close' | 'break_start' | 'break_end',
    time: string
  ) => {
    setShowTimePicker({ day, type });
    // Parse existing time or use default
    const [hour, minute] = time ? time.split(':') : ['09', '00'];
    const date = new Date();
    date.setHours(parseInt(hour), parseInt(minute));
    setSelectedTime(date);
  };

  const handleTimeChange = (event: any, selectedDate?: Date) => {
    if (event.type === 'set' && selectedDate && showTimePicker) {
      const time = selectedDate.toTimeString().slice(0, 5);
      const { day, type } = showTimePicker;

      setHours({
        ...hours,
        [day]: {
          ...(hours[day as keyof BusinessHours] as DaySchedule),
          [type]: time,
        } as DaySchedule,
      });
    }
    setShowTimePicker(null);
  };

  const handleCopyHours = (fromDay: keyof BusinessHours, toDay: keyof BusinessHours) => {
    if (fromDay === 'buffer_time_minutes' || toDay === 'buffer_time_minutes') return;

    setHours({
      ...hours,
      [toDay]: { ...(hours[fromDay] as DaySchedule) },
    });
  };

  const handleSave = () => {
    // Validar horarios
    for (const day of DAYS) {
      const schedule = hours[day.key as keyof BusinessHours] as DaySchedule;
      if (schedule.is_open && schedule.open_time && schedule.close_time) {
        if (schedule.open_time >= schedule.close_time) {
          Toast.show({
            type: 'error',
            text1: 'Error',
            text2: `El horario de cierre debe ser posterior al de apertura en ${day.label}`,
          });
          return;
        }
      }
    }

    onSave(hours);
    onClose();
  };

  const formatTime = (time?: string) => {
    if (!time) return '--:--';
    return time;
  };

  const handleBufferTimeChange = (change: number) => {
    const newValue = hours.buffer_time_minutes + change;
    if (newValue >= 0 && newValue <= 60) {
      setHours({ ...hours, buffer_time_minutes: newValue });
    }
  };

  return (
    <Modal visible={visible} animationType="slide" transparent={true} onRequestClose={onClose}>
      <View style={styles.container}>
        <View style={styles.modalContent}>
          <View style={styles.header}>
            <TouchableOpacity onPress={onClose}>
              <MaterialCommunityIcons name="close" size={24} color={COLORS.gray[700]} />
            </TouchableOpacity>
            <Text style={styles.title}>Horario de Trabajo</Text>
            <TouchableOpacity onPress={handleSave}>
              <Text style={styles.saveText}>Guardar</Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.content}>
            {/* Horario por día */}
            {DAYS.map(day => {
              const schedule = hours[day.key as keyof BusinessHours] as DaySchedule;
              const isOpen = schedule.is_open;

              return (
                <Card key={day.key} style={styles.dayCard} elevation="low">
                  <View style={styles.dayHeader}>
                    <Text style={[styles.dayName, !isOpen && styles.dayNameClosed]}>
                      {day.label}
                    </Text>
                    <Switch
                      value={isOpen}
                      onValueChange={() => handleToggleDay(day.key as keyof BusinessHours)}
                      trackColor={{ false: COLORS.gray[300], true: COLORS.primary }}
                      thumbColor={COLORS.white}
                    />
                  </View>

                  {isOpen && (
                    <>
                      <View style={styles.timeRow}>
                        <View style={styles.timeGroup}>
                          <Text style={styles.timeLabel}>Apertura</Text>
                          <TouchableOpacity
                            style={styles.timeButton}
                            onPress={() =>
                              handleTimeSelect(day.key, 'open', schedule.open_time || '09:00')
                            }
                          >
                            <MaterialCommunityIcons
                              name="clock-outline"
                              size={20}
                              color={COLORS.gray[600]}
                            />
                            <Text style={styles.timeText}>{formatTime(schedule.open_time)}</Text>
                          </TouchableOpacity>
                        </View>

                        <MaterialCommunityIcons
                          name="arrow-right"
                          size={20}
                          color={COLORS.gray[400]}
                        />

                        <View style={styles.timeGroup}>
                          <Text style={styles.timeLabel}>Cierre</Text>
                          <TouchableOpacity
                            style={styles.timeButton}
                            onPress={() =>
                              handleTimeSelect(day.key, 'close', schedule.close_time || '19:00')
                            }
                          >
                            <MaterialCommunityIcons
                              name="clock-outline"
                              size={20}
                              color={COLORS.gray[600]}
                            />
                            <Text style={styles.timeText}>{formatTime(schedule.close_time)}</Text>
                          </TouchableOpacity>
                        </View>
                      </View>

                      {day.key !== 'sunday' && (
                        <TouchableOpacity
                          style={styles.copyButton}
                          onPress={() => {
                            const nextDayIndex = DAYS.findIndex(d => d.key === day.key) + 1;
                            if (nextDayIndex < DAYS.length) {
                              handleCopyHours(
                                day.key as keyof BusinessHours,
                                DAYS[nextDayIndex].key as keyof BusinessHours
                              );
                            }
                          }}
                        >
                          <MaterialCommunityIcons
                            name="content-copy"
                            size={16}
                            color={COLORS.primary}
                          />
                          <Text style={styles.copyButtonText}>Copiar al siguiente día</Text>
                        </TouchableOpacity>
                      )}
                    </>
                  )}
                </Card>
              );
            })}

            {/* Tiempo buffer entre citas */}
            <Card style={styles.bufferCard} elevation="low">
              <Text style={styles.bufferTitle}>Tiempo entre citas</Text>
              <Text style={styles.bufferSubtitle}>
                Tiempo mínimo entre el final de una cita y el inicio de la siguiente
              </Text>
              <View style={styles.bufferControl}>
                <TouchableOpacity
                  style={styles.bufferButton}
                  onPress={() => handleBufferTimeChange(-5)}
                >
                  <MaterialCommunityIcons name="minus" size={20} color={COLORS.gray[600]} />
                </TouchableOpacity>
                <Text style={styles.bufferValue}>{hours.buffer_time_minutes} min</Text>
                <TouchableOpacity
                  style={styles.bufferButton}
                  onPress={() => handleBufferTimeChange(5)}
                >
                  <MaterialCommunityIcons name="plus" size={20} color={COLORS.gray[600]} />
                </TouchableOpacity>
              </View>
            </Card>

            {/* Acciones rápidas */}
            <View style={styles.quickActions}>
              <TouchableOpacity
                style={styles.quickActionButton}
                onPress={() => {
                  // Aplicar horario de lunes a viernes
                  const mondaySchedule = hours.monday;
                  ['tuesday', 'wednesday', 'thursday', 'friday'].forEach(day => {
                    setHours(prev => ({
                      ...prev,
                      [day]: { ...mondaySchedule },
                    }));
                  });
                  Toast.show({
                    type: 'success',
                    text1: 'Aplicado',
                    text2: 'Horario de lunes aplicado a días laborables',
                  });
                }}
              >
                <MaterialCommunityIcons name="calendar-sync" size={20} color={COLORS.primary} />
                <Text style={styles.quickActionText}>Aplicar Lun-Vie</Text>
              </TouchableOpacity>
            </View>
          </ScrollView>
        </View>

        {showTimePicker && (
          <DateTimePicker
            value={selectedTime}
            mode="time"
            is24Hour={true}
            display="spinner"
            onChange={handleTimeChange}
          />
        )}
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COMPONENTS.modal.overlay.backgroundColor,
    justifyContent: 'flex-end',
  },
  modalContent: {
    ...COMPONENTS.modal.content,
    borderTopLeftRadius: BORDER_RADIUS.xxl,
    borderTopRightRadius: BORDER_RADIUS.xxl,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
    maxHeight: '90%',
    padding: 0,
  },
  header: {
    ...COMPONENTS.header.base,
    padding: SPACING.lg,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray[100],
  },
  title: {
    ...COMPONENTS.header.title,
    fontSize: TYPOGRAPHY.size.lg,
  },
  saveText: {
    fontSize: TYPOGRAPHY.size.base,
    fontWeight: '600' as const,
    color: COLORS.primary,
  },
  content: {
    padding: SPACING.lg,
  },
  dayCard: {
    ...COMPONENTS.card.base,
    marginBottom: SPACING.md,
  },
  dayHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  dayName: {
    fontSize: TYPOGRAPHY.size.base,
    fontWeight: '600' as const,
    color: COLORS.text,
  },
  dayNameClosed: {
    color: COLORS.gray[500],
  },
  timeRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: SPACING.sm,
  },
  timeGroup: {
    flex: 1,
    alignItems: 'center',
  },
  timeLabel: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.gray[600],
    marginBottom: SPACING.xs,
  },
  timeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.surface,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.lg,
    gap: SPACING.xs,
  },
  timeText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[900],
    fontWeight: '500',
  },
  copyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: SPACING.sm,
    paddingVertical: SPACING.xs,
    gap: SPACING.xs,
  },
  copyButtonText: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.primary,
  },
  bufferCard: {
    ...COMPONENTS.card.base,
    marginTop: SPACING.lg,
    padding: SPACING.lg,
  },
  bufferTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: '600',
    color: COLORS.gray[900],
    marginBottom: SPACING.xs,
  },
  bufferSubtitle: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
    marginBottom: SPACING.md,
  },
  bufferControl: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: SPACING.lg,
  },
  bufferButton: {
    width: 40,
    height: 40,
    borderRadius: COMPONENTS.button.primary.borderRadius,
    backgroundColor: COLORS.surface,
    alignItems: 'center',
    justifyContent: 'center',
  },
  bufferValue: {
    fontSize: FONT_SIZES.lg,
    fontWeight: '600',
    color: COLORS.gray[900],
    minWidth: 60,
    textAlign: 'center',
  },
  quickActions: {
    marginTop: SPACING.xl,
    paddingTop: SPACING.lg,
    borderTopWidth: 1,
    borderTopColor: COLORS.gray[100],
  },
  quickActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.accent,
    paddingVertical: SPACING.md,
    borderRadius: COMPONENTS.button.primary.borderRadius,
    gap: SPACING.sm,
  },
  quickActionText: {
    fontSize: TYPOGRAPHY.size.base,
    fontWeight: '500' as const,
    color: COLORS.primary,
  },
});
