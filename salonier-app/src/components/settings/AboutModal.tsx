import React from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Linking,
} from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { COLORS, SPACING, FONT_SIZES, SHADOWS } from '../../constants';
import Card from '../common/Card';

interface AboutModalProps {
  visible: boolean;
  onClose: () => void;
}

export const AboutModal: React.FC<AboutModalProps> = ({
  visible,
  onClose,
}) => {
  const handleOpenLink = (url: string) => {
    Linking.openURL(url);
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <MaterialCommunityIcons name="arrow-left" size={24} color={COLORS.gray[900]} />
          </TouchableOpacity>
          <Text style={styles.title}>Acerca de Salonier</Text>
          <View style={styles.placeholder} />
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <Card style={styles.logoSection}>
            <View style={styles.logoContainer}>
              <MaterialCommunityIcons name="hair-dryer" size={60} color={COLORS.primary} />
            </View>
            <Text style={styles.appName}>Salonier</Text>
            <Text style={styles.version}>Versión 1.0.0</Text>
            <Text style={styles.tagline}>El asistente IA para coloristas profesionales</Text>
          </Card>

          <Card style={styles.section}>
            <Text style={styles.sectionTitle}>✨ Nuestra misión</Text>
            <Text style={styles.text}>
              Salonier nace de la pasión por combinar el arte de la coloración profesional 
              con la tecnología más avanzada. Creemos que cada colorista merece herramientas 
              que potencien su creatividad y precisión.
              {'\n\n'}
              Nuestra misión es simplificar el trabajo diario de los profesionales del color, 
              permitiéndoles enfocarse en lo que mejor saben hacer: crear belleza.
            </Text>
          </Card>

          <Card style={styles.section}>
            <Text style={styles.sectionTitle}>🚀 Características principales</Text>
            <View style={styles.feature}>
              <MaterialCommunityIcons name="brain" size={20} color={COLORS.primary} />
              <Text style={styles.featureText}>
                Análisis de color con IA avanzada
              </Text>
            </View>
            <View style={styles.feature}>
              <MaterialCommunityIcons name="shield-check" size={20} color={COLORS.primary} />
              <Text style={styles.featureText}>
                Privacidad garantizada con tecnología de parches
              </Text>
            </View>
            <View style={styles.feature}>
              <MaterialCommunityIcons name="sync" size={20} color={COLORS.primary} />
              <Text style={styles.featureText}>
                Conversión inteligente entre más de 100 marcas
              </Text>
            </View>
            <View style={styles.feature}>
              <MaterialCommunityIcons name="clock-fast" size={20} color={COLORS.primary} />
              <Text style={styles.featureText}>
                Consultas completas en menos de 8 minutos
              </Text>
            </View>
          </Card>

          <Card style={styles.section}>
            <Text style={styles.sectionTitle}>👥 El equipo</Text>
            <Text style={styles.text}>
              Salonier es desarrollado por un equipo apasionado de profesionales del color, 
              diseñadores y desarrolladores que trabajan juntos para crear la mejor experiencia posible.
              {'\n\n'}
              Agradecemos especialmente a todos los coloristas que han probado y dado feedback 
              durante el desarrollo. ¡Esta app es por y para vosotros!
            </Text>
          </Card>

          <Card style={styles.section}>
            <Text style={styles.sectionTitle}>📱 Síguenos</Text>
            <View style={styles.socialLinks}>
              <TouchableOpacity 
                style={styles.socialButton}
                onPress={() => handleOpenLink('https://instagram.com/salonierapp')}
              >
                <MaterialCommunityIcons name="instagram" size={24} color="#E4405F" />
                <Text style={styles.socialText}>Instagram</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.socialButton}
                onPress={() => handleOpenLink('https://youtube.com/@salonierapp')}
              >
                <MaterialCommunityIcons name="youtube" size={24} color="#FF0000" />
                <Text style={styles.socialText}>YouTube</Text>
              </TouchableOpacity>

              <TouchableOpacity 
                style={styles.socialButton}
                onPress={() => handleOpenLink('https://salonier.app')}
              >
                <MaterialCommunityIcons name="web" size={24} color={COLORS.primary} />
                <Text style={styles.socialText}>Web</Text>
              </TouchableOpacity>
            </View>
          </Card>

          <Card style={styles.section}>
            <Text style={styles.sectionTitle}>⚖️ Legal</Text>
            <TouchableOpacity 
              style={styles.legalLink}
              onPress={() => handleOpenLink('https://salonier.app/terminos')}
            >
              <Text style={styles.legalText}>Términos y condiciones</Text>
              <MaterialCommunityIcons name="chevron-right" size={20} color={COLORS.gray[600]} />
            </TouchableOpacity>
            <TouchableOpacity 
              style={styles.legalLink}
              onPress={() => handleOpenLink('https://salonier.app/privacidad')}
            >
              <Text style={styles.legalText}>Política de privacidad</Text>
              <MaterialCommunityIcons name="chevron-right" size={20} color={COLORS.gray[600]} />
            </TouchableOpacity>
            <TouchableOpacity 
              style={styles.legalLink}
              onPress={() => handleOpenLink('https://salonier.app/licencias')}
            >
              <Text style={styles.legalText}>Licencias de código abierto</Text>
              <MaterialCommunityIcons name="chevron-right" size={20} color={COLORS.gray[600]} />
            </TouchableOpacity>
          </Card>

          <View style={styles.footer}>
            <Text style={styles.footerText}>
              Hecho con ❤️ en España
            </Text>
            <Text style={styles.copyright}>
              © 2025 Salonier. Todos los derechos reservados.
            </Text>
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.md,
    paddingTop: 60,
    paddingBottom: SPACING.md,
    backgroundColor: COLORS.white,
    ...SHADOWS.sm,
  },
  closeButton: {
    padding: SPACING.xs,
  },
  title: {
    fontSize: FONT_SIZES.lg,
    fontWeight: 'bold',
    color: COLORS.gray[900],
    flex: 1,
    textAlign: 'center',
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: SPACING.md,
  },
  logoSection: {
    alignItems: 'center',
    paddingVertical: SPACING.xl,
    marginBottom: SPACING.md,
  },
  logoContainer: {
    width: 100,
    height: 100,
    borderRadius: 20,
    backgroundColor: COLORS.gray[200],
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: SPACING.md,
  },
  appName: {
    fontSize: FONT_SIZES.xxl,
    fontWeight: 'bold',
    color: COLORS.gray[900],
  },
  version: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
    marginTop: SPACING.xs,
  },
  tagline: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
    marginTop: SPACING.sm,
    textAlign: 'center',
    paddingHorizontal: SPACING.lg,
  },
  section: {
    marginBottom: SPACING.md,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: '600',
    color: COLORS.gray[900],
    marginBottom: SPACING.sm,
  },
  text: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
    lineHeight: 22,
  },
  feature: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.sm,
    marginBottom: SPACING.sm,
  },
  featureText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[900],
    flex: 1,
  },
  socialLinks: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: SPACING.sm,
  },
  socialButton: {
    alignItems: 'center',
    gap: SPACING.xs,
  },
  socialText: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.gray[600],
  },
  legalLink: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.sm,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray[300],
  },
  legalText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[900],
  },
  footer: {
    alignItems: 'center',
    paddingVertical: SPACING.xl,
    marginBottom: SPACING.xl,
  },
  footerText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
  },
  copyright: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.gray[600],
    marginTop: SPACING.xs,
  },
});