import React from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
} from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { COLORS, SPACING, FONT_SIZES, SHADOWS } from '../../constants';
import Card from '../common/Card';

interface PrivacyModalProps {
  visible: boolean;
  onClose: () => void;
}

export const PrivacyModal: React.FC<PrivacyModalProps> = ({
  visible,
  onClose,
}) => {
  return (
    <Modal
      visible={visible}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <MaterialCommunityIcons name="arrow-left" size={24} color={COLORS.gray[900]} />
          </TouchableOpacity>
          <Text style={styles.title}>Privacidad y Seguridad</Text>
          <View style={styles.placeholder} />
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <Card style={styles.section}>
            <View style={styles.sectionHeader}>
              <MaterialCommunityIcons name="shield-check" size={24} color={COLORS.primary} />
              <Text style={styles.sectionTitle}>Tu privacidad es nuestra prioridad</Text>
            </View>
            <Text style={styles.text}>
              En Salonier nos tomamos muy en serio la protección de tus datos y los de tus clientes. 
              Toda la información está encriptada y nunca compartimos datos personales con terceros.
            </Text>
          </Card>

          <Card style={styles.section}>
            <Text style={styles.sectionTitle}>🔐 Protección de datos</Text>
            <Text style={styles.text}>
              • Encriptación de extremo a extremo para datos sensibles{'\n'}
              • Cumplimiento con GDPR y normativas locales{'\n'}
              • Copias de seguridad automáticas diarias{'\n'}
              • Acceso con autenticación segura
            </Text>
          </Card>

          <Card style={styles.section}>
            <Text style={styles.sectionTitle}>📸 Privacidad de fotos</Text>
            <Text style={styles.text}>
              Nuestro sistema de análisis de color está diseñado con la privacidad en mente:
              {'\n\n'}
              • NO almacenamos fotos completas de clientes{'\n'}
              • Solo extraemos pequeños parches de color del cabello{'\n'}
              • La detección facial automática protege la identidad{'\n'}
              • Las fotos se procesan localmente cuando es posible
            </Text>
          </Card>

          <Card style={styles.section}>
            <Text style={styles.sectionTitle}>🔒 Seguridad de la cuenta</Text>
            <Text style={styles.text}>
              • Contraseñas encriptadas con algoritmos seguros{'\n'}
              • Opción de autenticación de dos factores{'\n'}
              • Cierre de sesión automático por inactividad{'\n'}
              • Registro de actividad para detectar accesos no autorizados
            </Text>
          </Card>

          <Card style={styles.section}>
            <Text style={styles.sectionTitle}>👥 Datos de clientes</Text>
            <Text style={styles.text}>
              • Los datos de tus clientes son solo tuyos{'\n'}
              • Puedes exportar toda la información en cualquier momento{'\n'}
              • Derecho al olvido: elimina datos permanentemente{'\n'}
              • Control total sobre qué información compartes
            </Text>
          </Card>

          <Card style={styles.section}>
            <Text style={styles.sectionTitle}>📱 Permisos de la app</Text>
            <Text style={styles.text}>
              Solo solicitamos los permisos estrictamente necesarios:
              {'\n\n'}
              • <Text style={styles.bold}>Cámara</Text>: Para análisis de color{'\n'}
              • <Text style={styles.bold}>Almacenamiento</Text>: Para guardar fotos temporalmente{'\n'}
              • <Text style={styles.bold}>Notificaciones</Text>: Para recordatorios de citas (opcional)
            </Text>
          </Card>

          <View style={styles.footer}>
            <Text style={styles.footerText}>
              Para más información, consulta nuestra{' '}
              <Text style={styles.link}>Política de Privacidad completa</Text>
              {' '}o contacta con <NAME_EMAIL>
            </Text>
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.md,
    paddingTop: 60,
    paddingBottom: SPACING.md,
    backgroundColor: COLORS.white,
    ...SHADOWS.sm,
  },
  closeButton: {
    padding: SPACING.xs,
  },
  title: {
    fontSize: FONT_SIZES.lg,
    fontWeight: 'bold',
    color: COLORS.gray[900],
    flex: 1,
    textAlign: 'center',
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: SPACING.md,
  },
  section: {
    marginBottom: SPACING.md,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.sm,
    marginBottom: SPACING.sm,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: '600',
    color: COLORS.gray[900],
    marginBottom: SPACING.sm,
  },
  text: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
    lineHeight: 22,
  },
  bold: {
    fontWeight: '600',
    color: COLORS.gray[900],
  },
  link: {
    color: COLORS.primary,
    textDecorationLine: 'underline',
  },
  footer: {
    padding: SPACING.lg,
    marginBottom: SPACING.xl,
  },
  footerText: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.gray[600],
    textAlign: 'center',
    lineHeight: 18,
  },
});