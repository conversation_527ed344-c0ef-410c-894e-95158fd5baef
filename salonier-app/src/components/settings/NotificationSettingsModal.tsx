import React, { useState, useEffect } from 'react';
import { Modal, View, Text, TouchableOpacity, StyleSheet, ScrollView, Switch } from 'react-native';
import Toast from 'react-native-toast-message';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { COLORS, SPACING, FONT_SIZES, BORDER_RADIUS } from '../../constants';
import { User } from '../../types';
import { dataService } from '../../services/dataService';
import { useNotifications } from '../../hooks/useNotifications';
import { openSystemNotificationSettings } from '../../utils/notifications';

interface NotificationSettingsModalProps {
  visible: boolean;
  onClose: () => void;
  currentUser: User | null;
  onUpdate: (updates: Partial<User>) => void;
}

const REMINDER_OPTIONS = [
  { label: '15 minutos', value: 15 },
  { label: '30 minutos', value: 30 },
  { label: '1 hora', value: 60 },
  { label: '2 horas', value: 120 },
  { label: '1 día', value: 1440 },
];

export const NotificationSettingsModal: React.FC<NotificationSettingsModalProps> = ({
  visible,
  onClose,
  currentUser,
  onUpdate,
}) => {
  const { hasPermission, requestPermission } = useNotifications();
  const [preferences, setPreferences] = useState({
    appointment_reminder: false,
    reminder_time_minutes: 30,
    low_stock_alert: false,
    client_retouch_reminder: false,
    notification_method: 'push' as 'push' | 'email' | 'sms' | 'whatsapp',
  });
  const [showReminderPicker, setShowReminderPicker] = useState(false);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (currentUser?.notification_preferences) {
      setPreferences(currentUser.notification_preferences);
    }
  }, [currentUser]);

  const handleToggleNotification = async (key: keyof typeof preferences) => {
    if (!hasPermission && key !== 'notification_method' && key !== 'reminder_time_minutes') {
      const granted = await requestPermission();
      if (!granted) {
        Toast.show({
          type: 'warning',
          text1: 'Permisos Necesarios',
          text2: 'Toca para ir a ajustes y activar notificaciones',
          position: 'top',
          visibilityTime: 4000,
          onPress: () => {
            Toast.hide();
            openSystemNotificationSettings();
          },
        });
        return;
      }
    }

    const newValue = !preferences[key as keyof typeof preferences];
    setPreferences(prev => ({
      ...prev,
      [key]: newValue,
    }));
  };

  const handleSave = async () => {
    setLoading(true);
    try {
      const updates: Partial<User> = {
        notification_preferences: preferences,
      };

      await dataService.users.updatePreferences(currentUser!.id, {
        appointmentReminder: preferences.appointment_reminder,
        reminderTimeMinutes: preferences.reminder_time_minutes,
        lowStockAlert: preferences.low_stock_alert,
        clientRetouchReminder: preferences.client_retouch_reminder,
        notificationMethod: preferences.notification_method,
      } as any);
      onUpdate(updates);

      Toast.show({
        type: 'success',
        text1: 'Éxito',
        text2: 'Preferencias de notificaciones actualizadas',
        position: 'top',
      });
      onClose();
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'No se pudieron guardar las preferencias',
        position: 'top',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal visible={visible} animationType="slide" transparent={true} onRequestClose={onClose}>
      <View style={styles.container}>
        <View style={styles.content}>
          <View style={styles.header}>
            <Text style={styles.title}>Notificaciones</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <MaterialCommunityIcons name="close" size={24} color={COLORS.gray[600]} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.form} showsVerticalScrollIndicator={false}>
            {!hasPermission && (
              <View style={styles.permissionAlert}>
                <MaterialCommunityIcons name="bell-off" size={24} color={COLORS.warning} />
                <Text style={styles.permissionText}>
                  Las notificaciones están desactivadas. Actívalas para recibir recordatorios
                  importantes.
                </Text>
                <TouchableOpacity style={styles.enableButton} onPress={requestPermission}>
                  <Text style={styles.enableButtonText}>Activar</Text>
                </TouchableOpacity>
              </View>
            )}

            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Recordatorios de Citas</Text>

              <View style={styles.settingRow}>
                <View style={styles.settingInfo}>
                  <Text style={styles.settingLabel}>Recordatorio de citas</Text>
                  <Text style={styles.settingDescription}>
                    Recibe una notificación antes de cada cita
                  </Text>
                </View>
                <Switch
                  value={preferences.appointment_reminder}
                  onValueChange={() => handleToggleNotification('appointment_reminder')}
                  trackColor={{ false: COLORS.gray[300], true: COLORS.primary }}
                  thumbColor={COLORS.white}
                />
              </View>

              {preferences.appointment_reminder && (
                <TouchableOpacity
                  style={styles.timeSelector}
                  onPress={() => setShowReminderPicker(true)}
                >
                  <Text style={styles.timeSelectorLabel}>Avisar con</Text>
                  <View style={styles.timeSelectorValue}>
                    <Text style={styles.timeSelectorText}>
                      {
                        REMINDER_OPTIONS.find(
                          opt => opt.value === preferences.reminder_time_minutes
                        )?.label
                      }
                    </Text>
                    <MaterialCommunityIcons
                      name="chevron-down"
                      size={20}
                      color={COLORS.gray[600]}
                    />
                  </View>
                </TouchableOpacity>
              )}
            </View>

            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Inventario</Text>

              <View style={styles.settingRow}>
                <View style={styles.settingInfo}>
                  <Text style={styles.settingLabel}>Alertas de stock bajo</Text>
                  <Text style={styles.settingDescription}>
                    Notificación cuando un producto está por debajo del mínimo
                  </Text>
                </View>
                <Switch
                  value={preferences.low_stock_alert}
                  onValueChange={() => handleToggleNotification('low_stock_alert')}
                  trackColor={{ false: COLORS.gray[300], true: COLORS.primary }}
                  thumbColor={COLORS.white}
                />
              </View>
            </View>

            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Clientes</Text>

              <View style={styles.settingRow}>
                <View style={styles.settingInfo}>
                  <Text style={styles.settingLabel}>Recordatorio de retoque</Text>
                  <Text style={styles.settingDescription}>
                    Aviso cuando un cliente necesita retoque (6 semanas)
                  </Text>
                </View>
                <Switch
                  value={preferences.client_retouch_reminder}
                  onValueChange={() => handleToggleNotification('client_retouch_reminder')}
                  trackColor={{ false: COLORS.gray[300], true: COLORS.primary }}
                  thumbColor={COLORS.white}
                />
              </View>
            </View>

            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Método de Notificación</Text>

              <TouchableOpacity
                style={[
                  styles.methodOption,
                  preferences.notification_method === 'push' && styles.methodOptionSelected,
                ]}
                onPress={() => setPreferences(prev => ({ ...prev, notification_method: 'push' }))}
              >
                <MaterialCommunityIcons
                  name="cellphone"
                  size={24}
                  color={
                    preferences.notification_method === 'push' ? COLORS.primary : COLORS.gray[600]
                  }
                />
                <Text
                  style={[
                    styles.methodText,
                    preferences.notification_method === 'push' && styles.methodTextSelected,
                  ]}
                >
                  Notificaciones Push
                </Text>
                {preferences.notification_method === 'push' && (
                  <MaterialCommunityIcons name="check" size={20} color={COLORS.primary} />
                )}
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.methodOption,
                  preferences.notification_method === 'whatsapp' && styles.methodOptionSelected,
                ]}
                onPress={() =>
                  setPreferences(prev => ({ ...prev, notification_method: 'whatsapp' }))
                }
              >
                <MaterialCommunityIcons
                  name="whatsapp"
                  size={24}
                  color={
                    preferences.notification_method === 'whatsapp'
                      ? COLORS.primary
                      : COLORS.gray[600]
                  }
                />
                <Text
                  style={[
                    styles.methodText,
                    preferences.notification_method === 'whatsapp' && styles.methodTextSelected,
                  ]}
                >
                  WhatsApp (Próximamente)
                </Text>
                {preferences.notification_method === 'whatsapp' && (
                  <MaterialCommunityIcons name="check" size={20} color={COLORS.primary} />
                )}
              </TouchableOpacity>
            </View>
          </ScrollView>

          <View style={styles.footer}>
            <TouchableOpacity style={[styles.button, styles.cancelButton]} onPress={onClose}>
              <Text style={styles.cancelButtonText}>Cancelar</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.button, styles.saveButton]}
              onPress={handleSave}
              disabled={loading}
            >
              <Text style={styles.saveButtonText}>{loading ? 'Guardando...' : 'Guardar'}</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Reminder Time Picker Modal */}
        {showReminderPicker && (
          <Modal
            visible={showReminderPicker}
            animationType="slide"
            transparent={true}
            onRequestClose={() => setShowReminderPicker(false)}
          >
            <View style={styles.pickerContainer}>
              <View style={styles.pickerContent}>
                <View style={styles.pickerHeader}>
                  <Text style={styles.pickerTitle}>Tiempo de recordatorio</Text>
                  <TouchableOpacity onPress={() => setShowReminderPicker(false)}>
                    <MaterialCommunityIcons name="close" size={24} color={COLORS.gray[600]} />
                  </TouchableOpacity>
                </View>
                {REMINDER_OPTIONS.map(option => (
                  <TouchableOpacity
                    key={option.value}
                    style={[
                      styles.pickerOption,
                      preferences.reminder_time_minutes === option.value &&
                        styles.pickerOptionSelected,
                    ]}
                    onPress={() => {
                      setPreferences(prev => ({ ...prev, reminder_time_minutes: option.value }));
                      setShowReminderPicker(false);
                    }}
                  >
                    <Text
                      style={[
                        styles.pickerOptionText,
                        preferences.reminder_time_minutes === option.value &&
                          styles.pickerOptionTextSelected,
                      ]}
                    >
                      {option.label}
                    </Text>
                    {preferences.reminder_time_minutes === option.value && (
                      <MaterialCommunityIcons name="check" size={20} color={COLORS.primary} />
                    )}
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          </Modal>
        )}
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  content: {
    backgroundColor: COLORS.white,
    borderTopLeftRadius: BORDER_RADIUS.xl,
    borderTopRightRadius: BORDER_RADIUS.xl,
    maxHeight: '90%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.lg,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray[200],
  },
  title: {
    fontSize: FONT_SIZES.xl,
    fontWeight: 'bold',
    color: COLORS.gray[900],
  },
  closeButton: {
    padding: SPACING.xs,
  },
  form: {
    padding: SPACING.lg,
  },
  permissionAlert: {
    backgroundColor: COLORS.warning + '20',
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    marginBottom: SPACING.lg,
    alignItems: 'center',
    gap: SPACING.sm,
  },
  permissionText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[700],
    textAlign: 'center',
  },
  enableButton: {
    backgroundColor: COLORS.warning,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.sm,
  },
  enableButtonText: {
    color: COLORS.white,
    fontWeight: '600',
  },
  section: {
    marginBottom: SPACING.xl,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: '600',
    color: COLORS.gray[900],
    marginBottom: SPACING.md,
  },
  settingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.sm,
  },
  settingInfo: {
    flex: 1,
    marginRight: SPACING.md,
  },
  settingLabel: {
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[900],
    marginBottom: SPACING.xs,
  },
  settingDescription: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
  },
  timeSelector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: COLORS.gray[100],
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    marginTop: SPACING.md,
  },
  timeSelectorLabel: {
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[700],
  },
  timeSelectorValue: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.xs,
  },
  timeSelectorText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[900],
    fontWeight: '500',
  },
  methodOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    borderWidth: 1,
    borderColor: COLORS.gray[300],
    marginBottom: SPACING.sm,
    gap: SPACING.md,
  },
  methodOptionSelected: {
    borderColor: COLORS.primary,
    backgroundColor: COLORS.primary + '10',
  },
  methodText: {
    flex: 1,
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[700],
  },
  methodTextSelected: {
    color: COLORS.primary,
    fontWeight: '500',
  },
  footer: {
    flexDirection: 'row',
    padding: SPACING.lg,
    borderTopWidth: 1,
    borderTopColor: COLORS.gray[200],
    gap: SPACING.md,
  },
  button: {
    flex: 1,
    paddingVertical: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: COLORS.gray[100],
  },
  saveButton: {
    backgroundColor: COLORS.primary,
  },
  cancelButtonText: {
    color: COLORS.gray[700],
    fontWeight: '600',
  },
  saveButtonText: {
    color: COLORS.white,
    fontWeight: '600',
  },
  pickerContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  pickerContent: {
    backgroundColor: COLORS.white,
    borderTopLeftRadius: BORDER_RADIUS.xl,
    borderTopRightRadius: BORDER_RADIUS.xl,
    paddingBottom: SPACING.xl,
  },
  pickerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.lg,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray[200],
  },
  pickerTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: '600',
    color: COLORS.gray[900],
  },
  pickerOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
  },
  pickerOptionSelected: {
    backgroundColor: COLORS.gray[100],
  },
  pickerOptionText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[700],
  },
  pickerOptionTextSelected: {
    color: COLORS.primary,
    fontWeight: '500',
  },
});
