import React from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Linking,
} from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { COLORS, SPACING, FONT_SIZES, BORDER_RADIUS, SHADOWS } from '../../constants';
import Card from '../common/Card';

interface HelpModalProps {
  visible: boolean;
  onClose: () => void;
}

export const HelpModal: React.FC<HelpModalProps> = ({
  visible,
  onClose,
}) => {
  const handleContact = (method: 'whatsapp' | 'email') => {
    if (method === 'whatsapp') {
      Linking.openURL('https://wa.me/34600000000?text=Hola,%20necesito%20ayuda%20con%20Salonier');
    } else {
      Linking.openURL('mailto:<EMAIL>?subject=Ayuda%20con%20Salonier');
    }
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <MaterialCommunityIcons name="arrow-left" size={24} color={COLORS.gray[900]} />
          </TouchableOpacity>
          <Text style={styles.title}>Ayuda y Soporte</Text>
          <View style={styles.placeholder} />
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <Card style={styles.section}>
            <View style={styles.sectionHeader}>
              <MaterialCommunityIcons name="lifebuoy" size={24} color={COLORS.primary} />
              <Text style={styles.sectionTitle}>¿Cómo podemos ayudarte?</Text>
            </View>
            <Text style={styles.text}>
              Estamos aquí para ayudarte a sacar el máximo provecho de Salonier. 
              Encuentra respuestas rápidas o contacta con nuestro equipo de soporte.
            </Text>
          </Card>

          <Card style={styles.section}>
            <Text style={styles.sectionTitle}>📚 Preguntas frecuentes</Text>
            
            <View style={styles.faqItem}>
              <Text style={styles.faqQuestion}>¿Cómo analizo el color del cabello?</Text>
              <Text style={styles.faqAnswer}>
                Ve a "Nueva Consulta", selecciona un cliente y sigue el flujo paso a paso. 
                Puedes usar la cámara o seleccionar manualmente los colores.
              </Text>
            </View>

            <View style={styles.faqItem}>
              <Text style={styles.faqQuestion}>¿Puedo usar mis propias marcas?</Text>
              <Text style={styles.faqAnswer}>
                ¡Por supuesto! En Ajustes → Marcas Preferidas puedes configurar las marcas 
                y líneas que uses habitualmente.
              </Text>
            </View>

            <View style={styles.faqItem}>
              <Text style={styles.faqQuestion}>¿Cómo convierto entre marcas?</Text>
              <Text style={styles.faqAnswer}>
                Usa la Calculadora de Conversión. Selecciona la marca origen, ingresa el código 
                y elige la marca destino. La IA te dará las mejores equivalencias.
              </Text>
            </View>

            <View style={styles.faqItem}>
              <Text style={styles.faqQuestion}>¿Es seguro el análisis de fotos?</Text>
              <Text style={styles.faqAnswer}>
                Totalmente. Solo extraemos pequeños parches de color del cabello. 
                Nunca almacenamos fotos completas ni rostros de clientes.
              </Text>
            </View>
          </Card>

          <Card style={styles.section}>
            <Text style={styles.sectionTitle}>💬 Contacta con nosotros</Text>
            
            <TouchableOpacity 
              style={styles.contactButton}
              onPress={() => handleContact('whatsapp')}
            >
              <MaterialCommunityIcons name="whatsapp" size={24} color={COLORS.success} />
              <View style={styles.contactTextContainer}>
                <Text style={styles.contactTitle}>WhatsApp</Text>
                <Text style={styles.contactSubtitle}>Respuesta en menos de 2 horas</Text>
              </View>
              <MaterialCommunityIcons name="chevron-right" size={24} color={COLORS.gray[600]} />
            </TouchableOpacity>

            <TouchableOpacity 
              style={styles.contactButton}
              onPress={() => handleContact('email')}
            >
              <MaterialCommunityIcons name="email" size={24} color={COLORS.primary} />
              <View style={styles.contactTextContainer}>
                <Text style={styles.contactTitle}>Email</Text>
                <Text style={styles.contactSubtitle}><EMAIL></Text>
              </View>
              <MaterialCommunityIcons name="chevron-right" size={24} color={COLORS.gray[600]} />
            </TouchableOpacity>
          </Card>

          <Card style={styles.section}>
            <Text style={styles.sectionTitle}>🎥 Tutoriales</Text>
            <Text style={styles.text}>
              Visita nuestro canal de YouTube para ver tutoriales paso a paso sobre:
              {'\n\n'}
              • Primeros pasos con Salonier{'\n'}
              • Análisis avanzado de color{'\n'}
              • Gestión eficiente de clientes{'\n'}
              • Trucos y consejos profesionales
            </Text>
            <TouchableOpacity style={styles.button}>
              <Text style={styles.buttonText}>Ver tutoriales</Text>
            </TouchableOpacity>
          </Card>

          <Card style={styles.section}>
            <Text style={styles.sectionTitle}>🐛 Reportar un problema</Text>
            <Text style={styles.text}>
              Si encuentras algún error o algo no funciona como esperabas, 
              por favor háznoslo saber. Tu feedback nos ayuda a mejorar.
            </Text>
            <TouchableOpacity style={styles.buttonSecondary}>
              <Text style={styles.buttonSecondaryText}>Reportar problema</Text>
            </TouchableOpacity>
          </Card>
        </ScrollView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.md,
    paddingTop: 60,
    paddingBottom: SPACING.md,
    backgroundColor: COLORS.white,
    ...SHADOWS.sm,
  },
  closeButton: {
    padding: SPACING.xs,
  },
  title: {
    fontSize: FONT_SIZES.lg,
    fontWeight: 'bold',
    color: COLORS.gray[900],
    flex: 1,
    textAlign: 'center',
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: SPACING.md,
  },
  section: {
    marginBottom: SPACING.md,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.sm,
    marginBottom: SPACING.sm,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: '600',
    color: COLORS.gray[900],
    marginBottom: SPACING.sm,
  },
  text: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
    lineHeight: 22,
  },
  faqItem: {
    marginBottom: SPACING.md,
    paddingBottom: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray[300],
  },
  faqQuestion: {
    fontSize: FONT_SIZES.sm,
    fontWeight: '600',
    color: COLORS.gray[900],
    marginBottom: SPACING.xs,
  },
  faqAnswer: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
    lineHeight: 20,
  },
  contactButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    backgroundColor: COLORS.background,
    marginBottom: SPACING.sm,
  },
  contactTextContainer: {
    flex: 1,
    marginLeft: SPACING.sm,
  },
  contactTitle: {
    fontSize: FONT_SIZES.sm,
    fontWeight: '600',
    color: COLORS.gray[900],
  },
  contactSubtitle: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.gray[600],
    marginTop: 2,
  },
  button: {
    backgroundColor: COLORS.primary,
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.lg,
    borderRadius: BORDER_RADIUS.md,
    alignItems: 'center',
    marginTop: SPACING.sm,
  },
  buttonText: {
    color: COLORS.white,
    fontSize: FONT_SIZES.sm,
    fontWeight: '600',
  },
  buttonSecondary: {
    borderWidth: 1,
    borderColor: COLORS.primary,
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.lg,
    borderRadius: BORDER_RADIUS.md,
    alignItems: 'center',
    marginTop: SPACING.sm,
  },
  buttonSecondaryText: {
    color: COLORS.primary,
    fontSize: FONT_SIZES.sm,
    fontWeight: '600',
  },
});