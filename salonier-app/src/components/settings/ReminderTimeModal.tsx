import React, { useState } from 'react';
import { Modal, View, Text, TouchableOpacity, ScrollView, StyleSheet, Switch } from 'react-native';
import Toast from 'react-native-toast-message';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { COLORS, SPACING, FONT_SIZES, BORDER_RADIUS, SHADOWS } from '../../constants';
import Card from '../common/Card';

interface ReminderSettings {
  enabled: boolean;
  time_before_appointment: number; // en minutos
  send_confirmation: boolean;
  send_followup: boolean;
  followup_days_after: number;
  message_template: string;
}

interface ReminderTimeModalProps {
  visible: boolean;
  onClose: () => void;
  onSave: (settings: ReminderSettings) => void;
}

const REMINDER_OPTIONS = [
  { value: 30, label: '30 minutos antes' },
  { value: 60, label: '1 hora antes' },
  { value: 120, label: '2 horas antes' },
  { value: 1440, label: '1 día antes' },
  { value: 2880, label: '2 días antes' },
];

const DEFAULT_MESSAGE =
  'Hola {nombre}! Te recordamos tu cita de {servicio} mañana a las {hora}. ¡Te esperamos! 💇‍♀️';

export const ReminderTimeModal: React.FC<ReminderTimeModalProps> = ({
  visible,
  onClose,
  onSave,
}) => {
  const [settings, setSettings] = useState<ReminderSettings>({
    enabled: true,
    time_before_appointment: 1440, // 1 día por defecto
    send_confirmation: true,
    send_followup: false,
    followup_days_after: 7,
    message_template: DEFAULT_MESSAGE,
  });

  const handleSave = () => {
    onSave(settings);
    Toast.show({
      type: 'success',
      text1: 'Éxito',
      text2: 'Configuración de recordatorios guardada',
    });
    onClose();
  };

  // Removed unused formatTime function

  return (
    <Modal visible={visible} animationType="slide" onRequestClose={onClose}>
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Text style={styles.cancelText}>Cancelar</Text>
          </TouchableOpacity>
          <Text style={styles.title}>Recordatorios</Text>
          <TouchableOpacity onPress={handleSave} style={styles.saveButton}>
            <Text style={styles.saveText}>Guardar</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <Card style={styles.section}>
            <View style={styles.switchRow}>
              <View style={styles.switchLeft}>
                <MaterialCommunityIcons name="bell-ring" size={24} color={COLORS.primary} />
                <View style={styles.switchTextContainer}>
                  <Text style={styles.switchTitle}>Recordatorios activos</Text>
                  <Text style={styles.switchSubtitle}>
                    Enviar recordatorios automáticos por WhatsApp
                  </Text>
                </View>
              </View>
              <Switch
                value={settings.enabled}
                onValueChange={value => setSettings({ ...settings, enabled: value })}
                trackColor={{ false: COLORS.gray[400], true: COLORS.primary }}
              />
            </View>
          </Card>

          {settings.enabled && (
            <>
              <Card style={styles.section}>
                <Text style={styles.sectionTitle}>
                  <MaterialCommunityIcons name="clock-outline" size={20} color={COLORS.primary} />{' '}
                  Tiempo de anticipación
                </Text>

                <View style={styles.optionsContainer}>
                  {REMINDER_OPTIONS.map(option => (
                    <TouchableOpacity
                      key={option.value}
                      style={[
                        styles.option,
                        settings.time_before_appointment === option.value && styles.optionSelected,
                      ]}
                      onPress={() =>
                        setSettings({ ...settings, time_before_appointment: option.value })
                      }
                    >
                      <MaterialCommunityIcons
                        name={
                          settings.time_before_appointment === option.value
                            ? 'radiobox-marked'
                            : 'radiobox-blank'
                        }
                        size={24}
                        color={
                          settings.time_before_appointment === option.value
                            ? COLORS.primary
                            : COLORS.gray[500]
                        }
                      />
                      <Text
                        style={[
                          styles.optionText,
                          settings.time_before_appointment === option.value &&
                            styles.optionTextSelected,
                        ]}
                      >
                        {option.label}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </Card>

              <Card style={styles.section}>
                <Text style={styles.sectionTitle}>
                  <MaterialCommunityIcons name="message-text" size={20} color={COLORS.primary} />{' '}
                  Tipos de mensajes
                </Text>

                <View style={styles.messageTypeRow}>
                  <View style={styles.messageTypeLeft}>
                    <MaterialCommunityIcons name="check-circle" size={20} color={COLORS.success} />
                    <View style={styles.messageTypeTextContainer}>
                      <Text style={styles.messageTypeTitle}>Confirmación de cita</Text>
                      <Text style={styles.messageTypeSubtitle}>
                        Enviar cuando se agenda una nueva cita
                      </Text>
                    </View>
                  </View>
                  <Switch
                    value={settings.send_confirmation}
                    onValueChange={value => setSettings({ ...settings, send_confirmation: value })}
                    trackColor={{ false: COLORS.gray[400], true: COLORS.primary }}
                  />
                </View>

                <View style={styles.divider} />

                <View style={styles.messageTypeRow}>
                  <View style={styles.messageTypeLeft}>
                    <MaterialCommunityIcons name="heart" size={20} color={COLORS.secondary} />
                    <View style={styles.messageTypeTextContainer}>
                      <Text style={styles.messageTypeTitle}>Mensaje de seguimiento</Text>
                      <Text style={styles.messageTypeSubtitle}>
                        Preguntar cómo va el color después del servicio
                      </Text>
                    </View>
                  </View>
                  <Switch
                    value={settings.send_followup}
                    onValueChange={value => setSettings({ ...settings, send_followup: value })}
                    trackColor={{ false: COLORS.gray[400], true: COLORS.primary }}
                  />
                </View>

                {settings.send_followup && (
                  <View style={styles.followupDaysContainer}>
                    <Text style={styles.label}>Días después del servicio:</Text>
                    <View style={styles.daysOptions}>
                      {[3, 7, 14, 30].map(days => (
                        <TouchableOpacity
                          key={days}
                          style={[
                            styles.dayOption,
                            settings.followup_days_after === days && styles.dayOptionSelected,
                          ]}
                          onPress={() => setSettings({ ...settings, followup_days_after: days })}
                        >
                          <Text
                            style={[
                              styles.dayOptionText,
                              settings.followup_days_after === days && styles.dayOptionTextSelected,
                            ]}
                          >
                            {days}d
                          </Text>
                        </TouchableOpacity>
                      ))}
                    </View>
                  </View>
                )}
              </Card>

              <Card style={styles.section}>
                <Text style={styles.sectionTitle}>
                  <MaterialCommunityIcons name="text" size={20} color={COLORS.primary} /> Vista
                  previa del mensaje
                </Text>

                <View style={styles.previewBox}>
                  <Text style={styles.previewText}>
                    {settings.message_template
                      .replace('{nombre}', 'María')
                      .replace('{servicio}', 'Coloración')
                      .replace('{hora}', '10:00')}
                  </Text>
                </View>

                <Text style={styles.hint}>
                  Variables disponibles: {'{nombre}'}, {'{servicio}'}, {'{hora}'}
                </Text>
              </Card>
            </>
          )}

          <View style={styles.infoBox}>
            <MaterialCommunityIcons name="information" size={20} color={COLORS.primary} />
            <Text style={styles.infoText}>
              Los recordatorios se enviarán automáticamente por WhatsApp al número registrado del
              cliente. Asegúrate de tener WhatsApp Business configurado.
            </Text>
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.md,
    paddingTop: 60,
    paddingBottom: SPACING.md,
    backgroundColor: COLORS.white,
    ...SHADOWS.sm,
  },
  closeButton: {
    padding: SPACING.xs,
  },
  cancelText: {
    color: COLORS.gray[500],
    fontSize: FONT_SIZES.md,
  },
  title: {
    fontSize: FONT_SIZES.lg,
    fontWeight: 'bold',
    color: COLORS.gray[900],
  },
  saveButton: {
    padding: SPACING.xs,
  },
  saveText: {
    color: COLORS.primary,
    fontSize: FONT_SIZES.md,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: SPACING.md,
  },
  section: {
    marginBottom: SPACING.md,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: '600',
    color: COLORS.gray[900],
    marginBottom: SPACING.md,
  },
  switchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  switchLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: SPACING.sm,
  },
  switchTextContainer: {
    flex: 1,
  },
  switchTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: '600',
    color: COLORS.gray[900],
  },
  switchSubtitle: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
    marginTop: 2,
  },
  optionsContainer: {
    gap: SPACING.xs,
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.sm,
    gap: SPACING.sm,
  },
  optionSelected: {
    opacity: 1,
  },
  optionText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[900],
  },
  optionTextSelected: {
    color: COLORS.primary,
    fontWeight: '600',
  },
  messageTypeRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: SPACING.sm,
  },
  messageTypeLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: SPACING.sm,
  },
  messageTypeTextContainer: {
    flex: 1,
  },
  messageTypeTitle: {
    fontSize: FONT_SIZES.sm,
    fontWeight: '500',
    color: COLORS.gray[900],
  },
  messageTypeSubtitle: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.gray[600],
    marginTop: 2,
  },
  divider: {
    height: 1,
    backgroundColor: COLORS.gray[300],
    marginVertical: SPACING.sm,
  },
  followupDaysContainer: {
    marginTop: SPACING.md,
    paddingLeft: SPACING.xl + SPACING.md,
  },
  label: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
    marginBottom: SPACING.sm,
  },
  daysOptions: {
    flexDirection: 'row',
    gap: SPACING.sm,
  },
  dayOption: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.sm,
    borderWidth: 1,
    borderColor: COLORS.gray[300],
    backgroundColor: COLORS.white,
  },
  dayOptionSelected: {
    borderColor: COLORS.primary,
    backgroundColor: COLORS.gray[200],
  },
  dayOptionText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[900],
  },
  dayOptionTextSelected: {
    color: COLORS.primary,
    fontWeight: '600',
  },
  previewBox: {
    backgroundColor: COLORS.background,
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    borderWidth: 1,
    borderColor: COLORS.gray[300],
  },
  previewText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[900],
    lineHeight: 20,
  },
  hint: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.gray[600],
    marginTop: SPACING.sm,
    fontStyle: 'italic',
  },
  infoBox: {
    flexDirection: 'row',
    backgroundColor: COLORS.gray[200],
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    marginTop: SPACING.sm,
    marginBottom: SPACING.xl,
    gap: SPACING.sm,
  },
  infoText: {
    flex: 1,
    fontSize: FONT_SIZES.sm,
    color: COLORS.primary,
    lineHeight: 20,
  },
});
