import React, { useState, useEffect } from 'react';
import {
  Modal,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import Toast from 'react-native-toast-message';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { COLORS, SPACING, FONT_SIZES, BORDER_RADIUS, SHADOWS } from '../../constants';
import { COMPONENTS, TYPOGRAPHY } from '../../constants/design-system';
import { Service } from '../../types';
import Card from '../common/Card';

interface ServicesSettingsModalProps {
  visible: boolean;
  onClose: () => void;
  services: Service[];
  onSave: (services: Service[]) => void;
}

const SERVICE_CATEGORIES = [
  { value: 'color', label: 'Coloración', icon: 'palette' },
  { value: 'cut', label: 'Corte', icon: 'scissors-cutting' },
  { value: 'treatment', label: 'Tratamiento', icon: 'spa' },
  { value: 'styling', label: 'Peinado', icon: 'hair-dryer' },
];

const PRESET_COLORS = [
  '#7C3AED', // Purple
  '#3B82F6', // Blue
  '#10B981', // Green
  '#F59E0B', // Orange
  '#EC4899', // Pink
  '#6B7280', // Gray
  '#EF4444', // Red
  '#8B5CF6', // Violet
];

const DEFAULT_SERVICES: Partial<Service>[] = [
  { name: 'Coloración Global', category: 'color', duration_minutes: 120, base_price: 60 },
  { name: 'Mechas', category: 'color', duration_minutes: 180, base_price: 80 },
  { name: 'Balayage', category: 'color', duration_minutes: 150, base_price: 100 },
  { name: 'Corte', category: 'cut', duration_minutes: 60, base_price: 30 },
  { name: 'Corte y Peinado', category: 'cut', duration_minutes: 90, base_price: 45 },
  { name: 'Tratamiento Keratina', category: 'treatment', duration_minutes: 120, base_price: 80 },
  { name: 'Tratamiento Hidratación', category: 'treatment', duration_minutes: 60, base_price: 40 },
  { name: 'Peinado Evento', category: 'styling', duration_minutes: 60, base_price: 50 },
];

export const ServicesSettingsModal: React.FC<ServicesSettingsModalProps> = ({
  visible,
  onClose,
  services,
  onSave,
}) => {
  const [localServices, setLocalServices] = useState<Service[]>(services);
  const [editingService, setEditingService] = useState<Service | null>(null);
  const [showServiceForm, setShowServiceForm] = useState(false);
  const [formData, setFormData] = useState<Partial<Service>>({
    name: '',
    category: 'color',
    duration_minutes: 60,
    base_price: 0,
    is_active: true,
    color: PRESET_COLORS[0],
  });

  useEffect(() => {
    setLocalServices(services);
  }, [services]);

  const handleAddService = () => {
    setEditingService(null);
    setFormData({
      name: '',
      category: 'color',
      duration_minutes: 60,
      base_price: 0,
      is_active: true,
      color: PRESET_COLORS[0],
    });
    setShowServiceForm(true);
  };

  const handleEditService = (service: Service) => {
    setEditingService(service);
    setFormData({
      name: service.name,
      category: service.category,
      duration_minutes: service.duration_minutes,
      base_price: service.base_price,
      is_active: service.is_active,
      color: service.color || PRESET_COLORS[0],
    });
    setShowServiceForm(true);
  };

  const handleSaveService = () => {
    if (!formData.name?.trim()) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'El nombre del servicio es requerido',
      });
      return;
    }

    if (editingService) {
      // Editar servicio existente
      const updatedServices = localServices.map(service =>
        service.id === editingService.id ? { ...service, ...formData } : service
      );
      setLocalServices(updatedServices);
    } else {
      // Crear nuevo servicio
      const newService: Service = {
        id: Date.now().toString(),
        user_id: '1', // TODO: get from current user
        name: formData.name,
        category: formData.category as 'color' | 'cut' | 'treatment' | 'styling',
        duration_minutes: formData.duration_minutes || 60,
        base_price: formData.base_price || 0,
        is_active: formData.is_active !== false,
        color: formData.color,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };
      setLocalServices([...localServices, newService]);
    }

    setShowServiceForm(false);
    resetForm();
  };

  // const handleDeleteService = (serviceId: string) => {
  //   Alert.alert(
  //     'Eliminar Servicio',
  //     '¿Estás seguro de eliminar este servicio?',
  //     [
  //       { text: 'Cancelar', style: 'cancel' },
  //       {
  //         text: 'Eliminar',
  //         style: 'destructive',
  //         onPress: () => {
  //           setLocalServices(localServices.filter(s => s.id !== serviceId));
  //         },
  //       },
  //     ]
  //   );
  // };

  const handleToggleService = (serviceId: string) => {
    const updatedServices = localServices.map(service =>
      service.id === serviceId ? { ...service, is_active: !service.is_active } : service
    );
    setLocalServices(updatedServices);
  };

  const handleSave = () => {
    onSave(localServices);
    onClose();
  };

  const resetForm = () => {
    setFormData({
      name: '',
      category: 'color',
      duration_minutes: 60,
      base_price: 0,
      is_active: true,
      color: PRESET_COLORS[0],
    });
    setEditingService(null);
  };

  const handleAddPresetService = (preset: Partial<Service>) => {
    const newService: Service = {
      id: Date.now().toString(),
      user_id: '1',
      name: preset.name!,
      category: preset.category as 'color' | 'cut' | 'treatment' | 'styling',
      duration_minutes: preset.duration_minutes!,
      base_price: preset.base_price!,
      is_active: true,
      color: PRESET_COLORS[Math.floor(Math.random() * PRESET_COLORS.length)],
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
    setLocalServices([...localServices, newService]);
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours === 0) return `${mins} min`;
    if (mins === 0) return `${hours} h`;
    return `${hours} h ${mins} min`;
  };

  if (showServiceForm) {
    return (
      <Modal
        visible={visible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowServiceForm(false)}
      >
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.container}
        >
          <View style={styles.modalContent}>
            <View style={styles.header}>
              <TouchableOpacity onPress={() => setShowServiceForm(false)}>
                <MaterialCommunityIcons name="arrow-left" size={24} color={COLORS.gray[700]} />
              </TouchableOpacity>
              <Text style={styles.title}>
                {editingService ? 'Editar Servicio' : 'Nuevo Servicio'}
              </Text>
              <TouchableOpacity onPress={handleSaveService}>
                <Text style={styles.saveText}>Guardar</Text>
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.form}>
              <View style={styles.formGroup}>
                <Text style={styles.label}>Nombre del servicio</Text>
                <TextInput
                  style={styles.input}
                  value={formData.name}
                  onChangeText={text => setFormData({ ...formData, name: text })}
                  placeholder="Ej: Coloración Global"
                  placeholderTextColor={COLORS.gray[400]}
                />
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.label}>Categoría</Text>
                <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                  <View style={styles.categoryContainer}>
                    {SERVICE_CATEGORIES.map(cat => (
                      <TouchableOpacity
                        key={cat.value}
                        style={[
                          styles.categoryChip,
                          formData.category === cat.value && styles.categoryChipSelected,
                        ]}
                        onPress={() => setFormData({ ...formData, category: cat.value as any })}
                      >
                        <MaterialCommunityIcons
                          name={cat.icon as any}
                          size={20}
                          color={formData.category === cat.value ? COLORS.white : COLORS.gray[600]}
                        />
                        <Text
                          style={[
                            styles.categoryText,
                            formData.category === cat.value && styles.categoryTextSelected,
                          ]}
                        >
                          {cat.label}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                </ScrollView>
              </View>

              <View style={styles.row}>
                <View style={[styles.formGroup, { flex: 1, marginRight: SPACING.sm }]}>
                  <Text style={styles.label}>Duración</Text>
                  <View style={styles.durationContainer}>
                    <TextInput
                      style={[styles.input, styles.durationInput]}
                      value={formData.duration_minutes?.toString()}
                      onChangeText={text =>
                        setFormData({ ...formData, duration_minutes: parseInt(text) || 0 })
                      }
                      keyboardType="numeric"
                      placeholder="60"
                    />
                    <Text style={styles.durationUnit}>minutos</Text>
                  </View>
                </View>

                <View style={[styles.formGroup, { flex: 1, marginLeft: SPACING.sm }]}>
                  <Text style={styles.label}>Precio base</Text>
                  <View style={styles.priceContainer}>
                    <Text style={styles.currencySymbol}>€</Text>
                    <TextInput
                      style={[styles.input, styles.priceInput]}
                      value={formData.base_price?.toString()}
                      onChangeText={text =>
                        setFormData({ ...formData, base_price: parseFloat(text) || 0 })
                      }
                      keyboardType="decimal-pad"
                      placeholder="0"
                    />
                  </View>
                </View>
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.label}>Color en calendario</Text>
                <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                  <View style={styles.colorContainer}>
                    {PRESET_COLORS.map(color => (
                      <TouchableOpacity
                        key={color}
                        style={[
                          styles.colorChip,
                          { backgroundColor: color },
                          formData.color === color && styles.colorChipSelected,
                        ]}
                        onPress={() => setFormData({ ...formData, color })}
                      >
                        {formData.color === color && (
                          <MaterialCommunityIcons name="check" size={20} color={COLORS.white} />
                        )}
                      </TouchableOpacity>
                    ))}
                  </View>
                </ScrollView>
              </View>
            </ScrollView>
          </View>
        </KeyboardAvoidingView>
      </Modal>
    );
  }

  return (
    <Modal visible={visible} animationType="slide" transparent={true} onRequestClose={onClose}>
      <View style={styles.container}>
        <View style={styles.modalContent}>
          <View style={styles.header}>
            <TouchableOpacity onPress={onClose}>
              <MaterialCommunityIcons name="close" size={24} color={COLORS.gray[700]} />
            </TouchableOpacity>
            <Text style={styles.title}>Mis Servicios</Text>
            <TouchableOpacity onPress={handleSave}>
              <Text style={styles.saveText}>Hecho</Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.content}>
            {localServices.length === 0 && (
              <View style={styles.emptyState}>
                <MaterialCommunityIcons
                  name="scissors-cutting"
                  size={48}
                  color={COLORS.gray[400]}
                />
                <Text style={styles.emptyText}>No tienes servicios configurados</Text>
                <Text style={styles.emptySubtext}>
                  Añade los servicios que ofreces para gestionar mejor tus citas
                </Text>
              </View>
            )}

            {localServices.map(service => (
              <Card key={service.id} style={styles.serviceCard} elevation="low">
                <TouchableOpacity
                  style={styles.serviceContent}
                  onPress={() => handleEditService(service)}
                >
                  <View style={styles.serviceLeft}>
                    <View
                      style={[
                        styles.serviceColor,
                        { backgroundColor: service.color || COLORS.gray[400] },
                      ]}
                    />
                    <View style={styles.serviceInfo}>
                      <Text
                        style={[
                          styles.serviceName,
                          !service.is_active && styles.serviceNameInactive,
                        ]}
                      >
                        {service.name}
                      </Text>
                      <View style={styles.serviceDetails}>
                        <Text style={styles.serviceDetail}>
                          {formatDuration(service.duration_minutes)}
                        </Text>
                        <Text style={styles.serviceDetail}>•</Text>
                        <Text style={styles.serviceDetail}>€{service.base_price}</Text>
                        <Text style={styles.serviceDetail}>•</Text>
                        <Text style={styles.serviceDetail}>
                          {SERVICE_CATEGORIES.find(c => c.value === service.category)?.label}
                        </Text>
                      </View>
                    </View>
                  </View>
                  <View style={styles.serviceActions}>
                    <TouchableOpacity
                      style={styles.toggleButton}
                      onPress={() => handleToggleService(service.id)}
                    >
                      <MaterialCommunityIcons
                        name={service.is_active ? 'toggle-switch' : 'toggle-switch-off'}
                        size={32}
                        color={service.is_active ? COLORS.primary : COLORS.gray[400]}
                      />
                    </TouchableOpacity>
                  </View>
                </TouchableOpacity>
              </Card>
            ))}

            <TouchableOpacity style={styles.addButton} onPress={handleAddService}>
              <MaterialCommunityIcons name="plus-circle" size={24} color={COLORS.primary} />
              <Text style={styles.addButtonText}>Añadir Servicio</Text>
            </TouchableOpacity>

            {localServices.length === 0 && (
              <View style={styles.presetsSection}>
                <Text style={styles.presetsTitle}>Servicios sugeridos</Text>
                {DEFAULT_SERVICES.map((preset, index) => (
                  <TouchableOpacity
                    key={index}
                    style={styles.presetItem}
                    onPress={() => handleAddPresetService(preset)}
                  >
                    <Text style={styles.presetName}>{preset.name}</Text>
                    <MaterialCommunityIcons name="plus" size={20} color={COLORS.primary} />
                  </TouchableOpacity>
                ))}
              </View>
            )}
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COMPONENTS.modal.overlay.backgroundColor,
    justifyContent: 'flex-end',
  },
  modalContent: {
    ...COMPONENTS.modal.content,
    borderTopLeftRadius: BORDER_RADIUS.xxl,
    borderTopRightRadius: BORDER_RADIUS.xxl,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
    maxHeight: '90%',
    padding: 0,
  },
  header: {
    ...COMPONENTS.header.base,
    padding: SPACING.lg,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray[100],
  },
  title: {
    ...COMPONENTS.header.title,
    fontSize: TYPOGRAPHY.size.lg,
  },
  saveText: {
    fontSize: TYPOGRAPHY.size.base,
    fontWeight: '600' as const,
    color: COLORS.primary,
  },
  content: {
    padding: SPACING.lg,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: SPACING.xl * 2,
  },
  emptyText: {
    fontSize: FONT_SIZES.lg,
    fontWeight: '600',
    color: COLORS.gray[900],
    marginTop: SPACING.md,
  },
  emptySubtext: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
    marginTop: SPACING.xs,
    textAlign: 'center',
    paddingHorizontal: SPACING.xl,
  },
  serviceCard: {
    ...COMPONENTS.card.base,
    marginBottom: SPACING.md,
    padding: 0,
  },
  serviceContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: SPACING.md,
  },
  serviceLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  serviceColor: {
    width: 4,
    height: 40,
    borderRadius: 2,
    marginRight: SPACING.md,
  },
  serviceInfo: {
    flex: 1,
  },
  serviceName: {
    fontSize: FONT_SIZES.md,
    fontWeight: '500',
    color: COLORS.gray[900],
  },
  serviceNameInactive: {
    color: COLORS.gray[500],
  },
  serviceDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: SPACING.xs,
    gap: SPACING.xs,
  },
  serviceDetail: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
  },
  serviceActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  toggleButton: {
    padding: SPACING.xs,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    ...COMPONENTS.button.secondary,
    paddingVertical: SPACING.md,
    gap: SPACING.sm,
    marginTop: SPACING.md,
  },
  addButtonText: {
    fontSize: COMPONENTS.button.secondary.fontSize,
    fontWeight: COMPONENTS.button.secondary.fontWeight,
    color: COMPONENTS.button.secondary.color,
  },
  form: {
    padding: SPACING.lg,
  },
  formGroup: {
    marginBottom: SPACING.lg,
  },
  label: {
    fontSize: FONT_SIZES.sm,
    fontWeight: '500',
    color: COLORS.gray[700],
    marginBottom: SPACING.sm,
  },
  input: {
    ...COMPONENTS.input.base,
    borderWidth: 1,
    borderColor: COLORS.gray[200],
  },
  categoryContainer: {
    flexDirection: 'row',
    gap: SPACING.sm,
  },
  categoryChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: COMPONENTS.button.primary.borderRadius,
    backgroundColor: COLORS.surface,
    gap: SPACING.xs,
  },
  categoryChipSelected: {
    backgroundColor: COLORS.secondary,
  },
  categoryText: {
    fontSize: TYPOGRAPHY.size.sm,
    fontWeight: '500' as const,
    color: COLORS.gray[600],
  },
  categoryTextSelected: {
    color: COLORS.white,
  },
  row: {
    flexDirection: 'row',
  },
  durationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  durationInput: {
    flex: 1,
    marginRight: SPACING.sm,
  },
  durationUnit: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  currencySymbol: {
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[600],
    marginRight: SPACING.sm,
  },
  priceInput: {
    flex: 1,
  },
  colorContainer: {
    flexDirection: 'row',
    gap: SPACING.sm,
  },
  colorChip: {
    width: 40,
    height: 40,
    borderRadius: COMPONENTS.button.primary.borderRadius,
    alignItems: 'center',
    justifyContent: 'center',
  },
  colorChipSelected: {
    borderWidth: 3,
    borderColor: COLORS.gray[300],
  },
  presetsSection: {
    marginTop: SPACING.xl,
  },
  presetsTitle: {
    fontSize: FONT_SIZES.sm,
    fontWeight: '600',
    color: COLORS.gray[600],
    marginBottom: SPACING.md,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  presetItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.md,
    backgroundColor: COLORS.surface,
    borderRadius: BORDER_RADIUS.lg,
    marginBottom: SPACING.sm,
  },
  presetName: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[700],
  },
});
