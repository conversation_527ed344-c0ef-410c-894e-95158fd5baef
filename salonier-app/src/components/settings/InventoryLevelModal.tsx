import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { User } from '../../types';

interface InventoryLevelModalProps {
  visible: boolean;
  onClose: () => void;
  user: User;
  onUpdate: (user: User) => void;
}

const INVENTORY_LEVELS = [
  {
    id: 'none',
    title: 'Solo Fórmulas',
    subtitle: 'Rápido y simple',
    description: 'Genera fórmulas profesionales sin gestionar inventario ni costos.',
    features: [
      'Fórmulas personalizadas',
      'Conversión de marcas',
      'Historial de servicios',
    ],
    icon: 'palette',
    color: '#4CAF50',
    recommended: 'Ideal para empezar',
  },
  {
    id: 'smart_cost',
    title: 'Smart Cost',
    subtitle: 'Conoce tu rentabilidad',
    description: 'Calcula automáticamente el costo de cada servicio y tu margen de ganancia.',
    features: [
      'Todo lo anterior +',
      'Costo por servicio',
      'Margen de ganancia',
      'Reportes de rentabilidad',
    ],
    icon: 'calculator',
    color: '#2196F3',
    recommended: 'Recomendado',
  },
  {
    id: 'full_control',
    title: 'Control Total',
    subtitle: 'Gestión completa',
    description: 'Control total del inventario con movimientos, alertas y consumo automático.',
    features: [
      'Todo lo anterior +',
      'Inventario completo',
      'Movimientos de stock',
      'Alertas de reposición',
      'Consumo automático',
    ],
    icon: 'chart-box',
    color: '#9C27B0',
    recommended: 'Para salones',
  },
];

export default function InventoryLevelModal({
  visible,
  onClose,
  user,
  onUpdate,
}: InventoryLevelModalProps) {
  const [selectedLevel, setSelectedLevel] = useState(user.inventory_level || 'none');
  const [saving, setSaving] = useState(false);

  const handleSave = async () => {
    setSaving(true);
    try {
      const updatedUser = {
        ...user,
        inventory_level: selectedLevel as 'none' | 'smart_cost' | 'full_control',
      };
      
      // TODO: Save to backend
      await new Promise(resolve => setTimeout(resolve, 500));
      
      onUpdate(updatedUser);
      onClose();
    } catch (error) {
      console.error('Error saving inventory level:', error);
    } finally {
      setSaving(false);
    }
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <MaterialCommunityIcons name="close" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.title}>Nivel de Control de Inventario</Text>
          <TouchableOpacity 
            onPress={handleSave} 
            style={styles.saveButton}
            disabled={saving}
          >
            <Text style={[styles.saveText, saving && styles.saveTextDisabled]}>
              {saving ? 'Guardando...' : 'Guardar'}
            </Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <Text style={styles.subtitle}>
            Elige cómo prefieres trabajar. Puedes cambiarlo cuando quieras.
          </Text>

          {INVENTORY_LEVELS.map((level) => (
            <TouchableOpacity
              key={level.id}
              style={[
                styles.levelCard,
                selectedLevel === level.id && styles.levelCardSelected,
              ]}
              onPress={() => setSelectedLevel(level.id as 'none' | 'smart_cost' | 'full_control')}
              activeOpacity={0.8}
            >
              <View style={styles.levelHeader}>
                <View style={[styles.iconContainer, { backgroundColor: level.color + '20' }]}>
                  <MaterialCommunityIcons 
                    name={level.icon as any} 
                    size={32} 
                    color={level.color} 
                  />
                </View>
                <View style={styles.levelInfo}>
                  <View style={styles.titleRow}>
                    <Text style={styles.levelTitle}>{level.title}</Text>
                    {level.recommended && (
                      <View style={[styles.badge, { backgroundColor: level.color + '20' }]}>
                        <Text style={[styles.badgeText, { color: level.color }]}>
                          {level.recommended}
                        </Text>
                      </View>
                    )}
                  </View>
                  <Text style={styles.levelSubtitle}>{level.subtitle}</Text>
                </View>
                {selectedLevel === level.id && (
                  <MaterialCommunityIcons 
                    name="check-circle" 
                    size={24} 
                    color={level.color} 
                  />
                )}
              </View>

              <Text style={styles.levelDescription}>{level.description}</Text>

              <View style={styles.features}>
                {level.features.map((feature, index) => (
                  <View key={index} style={styles.featureRow}>
                    <MaterialCommunityIcons 
                      name="check" 
                      size={16} 
                      color={level.color} 
                    />
                    <Text style={styles.featureText}>{feature}</Text>
                  </View>
                ))}
              </View>
            </TouchableOpacity>
          ))}

          <View style={styles.infoBox}>
            <MaterialCommunityIcons name="information" size={20} color="#666" />
            <Text style={styles.infoText}>
              Puedes cambiar tu nivel de control en cualquier momento desde Ajustes. 
              Tus datos se conservarán al cambiar de nivel.
            </Text>
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  closeButton: {
    padding: 4,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    flex: 1,
    textAlign: 'center',
    marginHorizontal: 16,
  },
  saveButton: {
    paddingVertical: 6,
    paddingHorizontal: 16,
  },
  saveText: {
    fontSize: 16,
    color: '#E91E63',
    fontWeight: '600',
  },
  saveTextDisabled: {
    color: '#ccc',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  subtitle: {
    fontSize: 15,
    color: '#666',
    marginBottom: 24,
    textAlign: 'center',
  },
  levelCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    borderWidth: 2,
    borderColor: '#f0f0f0',
  },
  levelCardSelected: {
    borderColor: '#E91E63',
  },
  levelHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  iconContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  levelInfo: {
    flex: 1,
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  levelTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  badge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
  },
  badgeText: {
    fontSize: 12,
    fontWeight: '600',
  },
  levelSubtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  levelDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    marginBottom: 16,
  },
  features: {
    gap: 8,
  },
  featureRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  featureText: {
    fontSize: 14,
    color: '#333',
    flex: 1,
  },
  infoBox: {
    flexDirection: 'row',
    backgroundColor: '#f0f0f0',
    borderRadius: 12,
    padding: 16,
    marginTop: 8,
    marginBottom: 24,
    gap: 12,
  },
  infoText: {
    flex: 1,
    fontSize: 13,
    color: '#666',
    lineHeight: 18,
  },
});