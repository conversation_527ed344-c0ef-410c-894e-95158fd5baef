import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ScrollView,
  TextInput,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { ProductPricing } from '../../types';
import { COLORS, SPACING, FONT_SIZES, BORDER_RADIUS, SHADOWS } from '../../constants';
import { COMPONENTS, TYPOGRAPHY } from '../../constants/design-system';
// import { dataService } from '../../services/dataService';

interface PricingSetupModalProps {
  visible: boolean;
  onClose: () => void;
  onComplete: (pricing: ProductPricing[]) => void;
  existingPricing?: ProductPricing[];
}

// Productos comunes predefinidos para setup rápido
const COMMON_PRODUCTS = [
  { name: 'Tinte permanente', category: 'dye', unit_type: 'ml', unit_size: 60 },
  { name: 'Oxidante 20 Vol', category: 'developer', unit_type: 'ml', unit_size: 1000 },
  { name: 'Oxidante 30 Vol', category: 'developer', unit_type: 'ml', unit_size: 1000 },
  { name: 'Oxidante 40 Vol', category: 'developer', unit_type: 'ml', unit_size: 1000 },
  { name: 'Decolorante', category: 'dye', unit_type: 'g', unit_size: 500 },
  { name: 'Tratamiento proteico', category: 'treatment', unit_type: 'ml', unit_size: 200 },
  { name: 'Olaplex/Smartbond', category: 'treatment', unit_type: 'ml', unit_size: 100 },
];

export default function PricingSetupModal({
  visible,
  onClose,
  onComplete,
  existingPricing = [],
}: PricingSetupModalProps) {
  const [pricing, setPricing] = useState<ProductPricing[]>([]);
  // Removed unused setCurrentStep to fix TS6133 error
  const [customProduct, setCustomProduct] = useState({
    name: '',
    brand: '',
    price: '',
  });

  useEffect(() => {
    if (visible) {
      initializePricing();
    }
  }, [visible]);

  const initializePricing = () => {
    if (existingPricing.length > 0) {
      setPricing(existingPricing);
    } else {
      // Inicializar con productos comunes
      const initialPricing = COMMON_PRODUCTS.map((product, index) => ({
        id: `temp-${index}`,
        product_name: product.name,
        brand: '',
        unit_price: 0,
        unit_size: product.unit_size,
        unit_type: product.unit_type as 'ml' | 'g',
        category: product.category as any,
        last_updated: new Date().toISOString(),
      }));
      setPricing(initialPricing);
    }
  };

  const updatePrice = (index: number, price: string) => {
    const newPricing = [...pricing];
    newPricing[index].unit_price = parseFloat(price) || 0;
    setPricing(newPricing);
  };

  const updateBrand = (index: number, brand: string) => {
    const newPricing = [...pricing];
    newPricing[index].brand = brand;
    setPricing(newPricing);
  };

  const addCustomProduct = () => {
    if (!customProduct.name || !customProduct.price) return;

    const newProduct: ProductPricing = {
      id: `custom-${Date.now()}`,
      product_name: customProduct.name,
      brand: customProduct.brand,
      unit_price: parseFloat(customProduct.price) || 0,
      unit_size: 100, // Default
      unit_type: 'ml',
      category: 'other',
      last_updated: new Date().toISOString(),
    };

    setPricing([...pricing, newProduct]);
    setCustomProduct({ name: '', brand: '', price: '' });
  };

  const removeProduct = (index: number) => {
    const newPricing = pricing.filter((_, i) => i !== index);
    setPricing(newPricing);
  };

  const handleComplete = () => {
    // Filtrar solo los productos con precio configurado
    const configuredPricing = pricing.filter(p => p.unit_price > 0);
    onComplete(configuredPricing);
    onClose();
  };

  const calculateProgress = () => {
    const configured = pricing.filter(p => p.unit_price > 0).length;
    return (configured / pricing.length) * 100;
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <MaterialCommunityIcons name="close" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.title}>Configurar Precios</Text>
          <TouchableOpacity onPress={handleComplete} style={styles.doneButton}>
            <Text style={styles.doneText}>Listo</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.progressContainer}>
          <View style={styles.progressBar}>
            <View style={[styles.progressFill, { width: `${calculateProgress()}%` }]} />
          </View>
          <Text style={styles.progressText}>
            {pricing.filter(p => p.unit_price > 0).length} de {pricing.length} configurados
          </Text>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <Text style={styles.instructions}>
            Configura los precios de compra de tus productos principales. Esto te permitirá ver el
            costo real de cada servicio.
          </Text>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Productos Comunes</Text>
            {pricing.map((product, index) => (
              <View key={product.id} style={styles.productCard}>
                <View style={styles.productHeader}>
                  <View style={styles.productInfo}>
                    <Text style={styles.productName}>{product.product_name}</Text>
                    <Text style={styles.productSize}>
                      {product.unit_size} {product.unit_type}
                    </Text>
                  </View>
                  <TouchableOpacity
                    onPress={() => removeProduct(index)}
                    style={styles.removeButton}
                  >
                    <MaterialCommunityIcons name="close-circle" size={20} color="#999" />
                  </TouchableOpacity>
                </View>

                <View style={styles.inputRow}>
                  <View style={styles.inputGroup}>
                    <Text style={styles.inputLabel}>Marca (opcional)</Text>
                    <TextInput
                      style={styles.input}
                      value={product.brand}
                      onChangeText={text => updateBrand(index, text)}
                      placeholder="Ej: L'Oréal"
                    />
                  </View>

                  <View style={styles.inputGroup}>
                    <Text style={styles.inputLabel}>Precio compra</Text>
                    <View style={styles.priceInput}>
                      <TextInput
                        style={styles.input}
                        value={product.unit_price > 0 ? product.unit_price.toString() : ''}
                        onChangeText={text => updatePrice(index, text)}
                        keyboardType="decimal-pad"
                        placeholder="0.00"
                      />
                      <Text style={styles.currency}>€</Text>
                    </View>
                  </View>
                </View>
              </View>
            ))}
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Añadir Producto Personalizado</Text>
            <View style={styles.customProductCard}>
              <TextInput
                style={[styles.input, styles.fullInput]}
                value={customProduct.name}
                onChangeText={text => setCustomProduct({ ...customProduct, name: text })}
                placeholder="Nombre del producto"
              />

              <View style={styles.inputRow}>
                <View style={styles.inputGroup}>
                  <TextInput
                    style={styles.input}
                    value={customProduct.brand}
                    onChangeText={text => setCustomProduct({ ...customProduct, brand: text })}
                    placeholder="Marca"
                  />
                </View>

                <View style={styles.inputGroup}>
                  <View style={styles.priceInput}>
                    <TextInput
                      style={styles.input}
                      value={customProduct.price}
                      onChangeText={text => setCustomProduct({ ...customProduct, price: text })}
                      keyboardType="decimal-pad"
                      placeholder="Precio"
                    />
                    <Text style={styles.currency}>€</Text>
                  </View>
                </View>
              </View>

              <TouchableOpacity
                style={[
                  styles.addButton,
                  (!customProduct.name || !customProduct.price) && styles.addButtonDisabled,
                ]}
                onPress={addCustomProduct}
                disabled={!customProduct.name || !customProduct.price}
              >
                <MaterialCommunityIcons name="plus" size={20} color="#fff" />
                <Text style={styles.addButtonText}>Añadir Producto</Text>
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.tipBox}>
            <MaterialCommunityIcons name="lightbulb-outline" size={20} color="#666" />
            <Text style={styles.tipText}>
              No necesitas configurar todos los productos ahora. Puedes empezar con los principales
              y añadir más después.
            </Text>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    ...COMPONENTS.header.base,
    paddingHorizontal: SPACING.lg,
    paddingTop: 60,
    paddingBottom: SPACING.lg,
    backgroundColor: COLORS.white,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray[100],
  },
  closeButton: {
    padding: 4,
  },
  title: {
    ...COMPONENTS.header.title,
    fontSize: TYPOGRAPHY.size.lg,
  },
  doneButton: {
    paddingVertical: 6,
    paddingHorizontal: 16,
  },
  doneText: {
    fontSize: TYPOGRAPHY.size.base,
    color: COLORS.primary,
    fontWeight: '600' as const,
  },
  progressContainer: {
    backgroundColor: COLORS.white,
    padding: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray[100],
  },
  progressBar: {
    height: 8,
    backgroundColor: COLORS.surface,
    borderRadius: BORDER_RADIUS.sm,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: COLORS.success,
    borderRadius: BORDER_RADIUS.sm,
  },
  progressText: {
    fontSize: 13,
    color: '#666',
    marginTop: 8,
    textAlign: 'center',
  },
  content: {
    flex: 1,
    padding: SPACING.lg,
  },
  instructions: {
    fontSize: 15,
    color: '#666',
    lineHeight: 22,
    marginBottom: 24,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: TYPOGRAPHY.size.base,
    fontWeight: '600' as const,
    color: COLORS.text,
    marginBottom: SPACING.md,
  },
  productCard: {
    ...COMPONENTS.card.base,
    marginBottom: SPACING.md,
  },
  productHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    fontSize: 15,
    fontWeight: '500',
    color: '#333',
  },
  productSize: {
    fontSize: 13,
    color: '#666',
    marginTop: 2,
  },
  removeButton: {
    padding: 4,
  },
  inputRow: {
    flexDirection: 'row',
    gap: 12,
  },
  inputGroup: {
    flex: 1,
  },
  inputLabel: {
    fontSize: 13,
    color: '#666',
    marginBottom: 6,
  },
  input: {
    ...COMPONENTS.input.base,
    padding: SPACING.md,
    fontSize: TYPOGRAPHY.size.base,
  },
  fullInput: {
    marginBottom: 12,
  },
  priceInput: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  currency: {
    fontSize: 15,
    color: '#666',
    marginLeft: 8,
  },
  customProductCard: {
    ...COMPONENTS.card.base,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    ...COMPONENTS.button.primary,
    marginTop: SPACING.md,
    gap: SPACING.sm,
  },
  addButtonDisabled: {
    backgroundColor: COLORS.gray[400],
  },
  addButtonText: {
    color: COMPONENTS.button.primary.color,
    fontSize: COMPONENTS.button.primary.fontSize,
    fontWeight: COMPONENTS.button.primary.fontWeight,
  },
  tipBox: {
    flexDirection: 'row',
    backgroundColor: COLORS.surface,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.md,
    marginTop: SPACING.sm,
    marginBottom: SPACING.xl,
    gap: SPACING.md,
  },
  tipText: {
    flex: 1,
    fontSize: 13,
    color: '#666',
    lineHeight: 18,
  },
});
