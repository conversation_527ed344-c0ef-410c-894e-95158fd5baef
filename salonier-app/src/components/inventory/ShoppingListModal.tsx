import React, { useState, useEffect, useMemo } from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  TextInput,
  Share,
} from 'react-native';
import Toast from 'react-native-toast-message';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { COLORS, SPACING, FONT_SIZES, BORDER_RADIUS, SHADOWS } from '../../constants';
import Card from '../common/Card';
import { Product } from '../../types';
import { generateShoppingListPDF } from '../../utils/pdf';
import { sendWhatsAppMessage } from '../../utils/whatsapp';

interface ShoppingListModalProps {
  visible: boolean;
  onClose: () => void;
  products: Product[];
  onMarkAsOrdered?: (productIds: string[]) => void;
}

interface ShoppingItem extends Product {
  suggestedQuantity: number;
  adjustedQuantity: number;
  isSelected: boolean;
}

export const ShoppingListModal: React.FC<ShoppingListModalProps> = ({
  visible,
  onClose,
  products,
  onMarkAsOrdered,
}) => {
  const [shoppingItems, setShoppingItems] = useState<ShoppingItem[]>([]);
  const [searchQuery, setSearchQuery] = useState('');

  // Filtrar solo productos con stock bajo
  const lowStockProducts = useMemo(() => {
    return products.filter(p => p.current_stock <= p.min_stock);
  }, [products]);

  useEffect(() => {
    // Convertir productos a items de compra con cantidades sugeridas
    const items = lowStockProducts.map(product => {
      // Calcular cantidad sugerida
      const deficit = product.min_stock - product.current_stock;
      const buffer = product.min_stock * 0.5; // 50% extra de buffer
      let suggested = Math.ceil(deficit + buffer);

      // Redondear según la unidad
      if (product.unit === 'units') {
        // Para unidades, redondear a múltiplos de 5 o 10
        if (suggested <= 10) {
          suggested = Math.ceil(suggested / 5) * 5;
        } else {
          suggested = Math.ceil(suggested / 10) * 10;
        }
      } else {
        // Para ml o g, redondear a centenas
        suggested = Math.ceil(suggested / 100) * 100;
      }

      return {
        ...product,
        suggestedQuantity: suggested,
        adjustedQuantity: suggested,
        isSelected: true,
      };
    });

    setShoppingItems(items);
  }, [lowStockProducts]);

  // Agrupar productos por marca
  const groupedItems = useMemo(() => {
    const groups: { [brand: string]: ShoppingItem[] } = {};

    const filteredItems = searchQuery
      ? shoppingItems.filter(
          item =>
            item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            item.brand.toLowerCase().includes(searchQuery.toLowerCase()) ||
            item.code.toLowerCase().includes(searchQuery.toLowerCase())
        )
      : shoppingItems;

    filteredItems.forEach(item => {
      if (!groups[item.brand]) {
        groups[item.brand] = [];
      }
      groups[item.brand].push(item);
    });

    return groups;
  }, [shoppingItems, searchQuery]);

  const selectedItems = shoppingItems.filter(item => item.isSelected);
  const totalEstimatedCost = selectedItems.reduce((sum, item) => {
    return sum + item.purchase_price * item.adjustedQuantity;
  }, 0);

  const handleQuantityChange = (productId: string, newQuantity: number) => {
    setShoppingItems(items =>
      items.map(item =>
        item.id === productId ? { ...item, adjustedQuantity: Math.max(0, newQuantity) } : item
      )
    );
  };

  const handleToggleItem = (productId: string) => {
    setShoppingItems(items =>
      items.map(item => (item.id === productId ? { ...item, isSelected: !item.isSelected } : item))
    );
  };

  const handleSelectAll = () => {
    const allSelected = shoppingItems.every(item => item.isSelected);
    setShoppingItems(items => items.map(item => ({ ...item, isSelected: !allSelected })));
  };

  const handleExportPDF = async () => {
    try {
      const pdfUri = await generateShoppingListPDF(selectedItems, totalEstimatedCost);
      await Share.share({
        url: pdfUri,
        title: 'Lista de Compra Salonier',
      });
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'No se pudo generar el PDF',
        position: 'top',
      });
    }
  };

  const handleShareWhatsApp = () => {
    const message = formatShoppingListMessage(selectedItems, totalEstimatedCost);
    sendWhatsAppMessage('', message);
  };

  const handleMarkAsOrdered = () => {
    Toast.show({
      type: 'warning',
      text1: 'Marcar como pedido',
      text2: 'Mantén presionado para confirmar el pedido',
      position: 'top',
      visibilityTime: 3000,
      onPress: () => {
        Toast.hide();
        if (onMarkAsOrdered) {
          onMarkAsOrdered(selectedItems.map(item => item.id));
        }
        Toast.show({
          type: 'success',
          text1: 'Éxito',
          text2: 'Productos marcados como pedidos',
          position: 'top',
        });
        onClose();
      },
    });
  };

  const formatShoppingListMessage = (_items: ShoppingItem[], total: number): string => {
    let message = '📋 *LISTA DE COMPRA SALONIER*\n';
    message += `📅 Fecha: ${new Date().toLocaleDateString('es-ES')}\n\n`;

    Object.entries(groupedItems).forEach(([brand, brandItems]) => {
      const selectedBrandItems = brandItems.filter(item => item.isSelected);
      if (selectedBrandItems.length > 0) {
        message += `*${brand}*\n`;
        selectedBrandItems.forEach(item => {
          message += `• ${item.name} (${item.code}): ${item.adjustedQuantity} ${item.unit}\n`;
        });
        message += '\n';
      }
    });

    message += `💰 *Total estimado: €${total.toFixed(2)}*\n`;
    message += '\n_Generado con Salonier App_';

    return message;
  };

  return (
    <Modal visible={visible} animationType="slide" onRequestClose={onClose}>
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <MaterialCommunityIcons name="arrow-left" size={24} color={COLORS.gray[900]} />
          </TouchableOpacity>
          <Text style={styles.title}>Lista de Compra</Text>
          <TouchableOpacity onPress={handleSelectAll} style={styles.selectAllButton}>
            <MaterialCommunityIcons
              name={
                shoppingItems.every(item => item.isSelected)
                  ? 'checkbox-marked'
                  : 'checkbox-blank-outline'
              }
              size={24}
              color={COLORS.primary}
            />
          </TouchableOpacity>
        </View>

        {/* Resumen */}
        <View style={styles.summary}>
          <View style={styles.summaryItem}>
            <Text style={styles.summaryLabel}>Productos</Text>
            <Text style={styles.summaryValue}>{selectedItems.length}</Text>
          </View>
          <View style={styles.summaryDivider} />
          <View style={styles.summaryItem}>
            <Text style={styles.summaryLabel}>Total estimado</Text>
            <Text style={styles.summaryValue}>€{totalEstimatedCost.toFixed(2)}</Text>
          </View>
        </View>

        {/* Búsqueda */}
        <View style={styles.searchContainer}>
          <MaterialCommunityIcons name="magnify" size={20} color={COLORS.gray[400]} />
          <TextInput
            style={styles.searchInput}
            placeholder="Buscar productos..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor={COLORS.gray[400]}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <MaterialCommunityIcons name="close-circle" size={20} color={COLORS.gray[400]} />
            </TouchableOpacity>
          )}
        </View>

        {/* Lista de productos agrupados */}
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {Object.entries(groupedItems).map(([brand, items]) => (
            <Card key={brand} style={styles.brandSection}>
              <Text style={styles.brandTitle}>{brand}</Text>
              {items.map(item => (
                <View key={item.id} style={styles.productItem}>
                  <TouchableOpacity
                    style={styles.productCheckbox}
                    onPress={() => handleToggleItem(item.id)}
                  >
                    <MaterialCommunityIcons
                      name={item.isSelected ? 'checkbox-marked' : 'checkbox-blank-outline'}
                      size={24}
                      color={item.isSelected ? COLORS.primary : COLORS.gray[400]}
                    />
                  </TouchableOpacity>

                  <View style={styles.productInfo}>
                    <Text style={styles.productName}>{item.name}</Text>
                    <Text style={styles.productCode}>{item.code}</Text>
                    <View style={styles.stockInfo}>
                      <Text style={styles.stockText}>
                        Stock: {item.current_stock}/{item.min_stock} {item.unit}
                      </Text>
                    </View>
                  </View>

                  <View style={styles.quantityControl}>
                    <TouchableOpacity
                      style={styles.quantityButton}
                      onPress={() => handleQuantityChange(item.id, item.adjustedQuantity - 1)}
                      disabled={!item.isSelected}
                    >
                      <MaterialCommunityIcons name="minus" size={20} color={COLORS.primary} />
                    </TouchableOpacity>

                    <TextInput
                      style={[
                        styles.quantityInput,
                        !item.isSelected && styles.quantityInputDisabled,
                      ]}
                      value={item.adjustedQuantity.toString()}
                      onChangeText={text => {
                        const value = parseInt(text) || 0;
                        handleQuantityChange(item.id, value);
                      }}
                      keyboardType="numeric"
                      editable={item.isSelected}
                    />

                    <TouchableOpacity
                      style={styles.quantityButton}
                      onPress={() => handleQuantityChange(item.id, item.adjustedQuantity + 1)}
                      disabled={!item.isSelected}
                    >
                      <MaterialCommunityIcons name="plus" size={20} color={COLORS.primary} />
                    </TouchableOpacity>
                  </View>
                </View>
              ))}

              <View style={styles.brandSubtotal}>
                <Text style={styles.subtotalText}>
                  Subtotal: €
                  {items
                    .filter(item => item.isSelected)
                    .reduce((sum, item) => sum + item.purchase_price * item.adjustedQuantity, 0)
                    .toFixed(2)}
                </Text>
              </View>
            </Card>
          ))}

          {Object.keys(groupedItems).length === 0 && (
            <View style={styles.emptyState}>
              <MaterialCommunityIcons name="cart-check" size={64} color={COLORS.gray[300]} />
              <Text style={styles.emptyText}>
                {searchQuery ? 'No se encontraron productos' : 'No hay productos con stock bajo'}
              </Text>
            </View>
          )}
        </ScrollView>

        {/* Acciones */}
        {selectedItems.length > 0 && (
          <View style={styles.actions}>
            <TouchableOpacity style={styles.actionButton} onPress={handleExportPDF}>
              <MaterialCommunityIcons name="file-pdf-box" size={20} color={COLORS.primary} />
              <Text style={styles.actionButtonText}>PDF</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.actionButton} onPress={handleShareWhatsApp}>
              <MaterialCommunityIcons name="whatsapp" size={20} color={COLORS.success} />
              <Text style={styles.actionButtonText}>WhatsApp</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.actionButton, styles.actionButtonPrimary]}
              onPress={handleMarkAsOrdered}
            >
              <MaterialCommunityIcons name="check-all" size={20} color={COLORS.white} />
              <Text style={[styles.actionButtonText, styles.actionButtonTextPrimary]}>
                Marcar como pedido
              </Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.md,
    paddingTop: 60,
    paddingBottom: SPACING.md,
    backgroundColor: COLORS.white,
    ...SHADOWS.sm,
  },
  closeButton: {
    padding: SPACING.xs,
  },
  title: {
    fontSize: FONT_SIZES.lg,
    fontWeight: 'bold',
    color: COLORS.gray[900],
    flex: 1,
    textAlign: 'center',
  },
  selectAllButton: {
    padding: SPACING.xs,
  },
  summary: {
    flexDirection: 'row',
    backgroundColor: COLORS.primary + '10',
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    marginHorizontal: SPACING.md,
    marginTop: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
  },
  summaryItem: {
    flex: 1,
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
  },
  summaryValue: {
    fontSize: FONT_SIZES.xl,
    fontWeight: 'bold',
    color: COLORS.primary,
    marginTop: 4,
  },
  summaryDivider: {
    width: 1,
    backgroundColor: COLORS.primary + '30',
    marginHorizontal: SPACING.md,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.gray[100],
    borderRadius: BORDER_RADIUS.md,
    paddingHorizontal: SPACING.md,
    marginHorizontal: SPACING.md,
    marginVertical: SPACING.md,
  },
  searchInput: {
    flex: 1,
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.sm,
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[900],
  },
  content: {
    flex: 1,
  },
  brandSection: {
    marginHorizontal: SPACING.md,
    marginBottom: SPACING.md,
    padding: SPACING.md,
  },
  brandTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: '600',
    color: COLORS.primary,
    marginBottom: SPACING.sm,
  },
  productItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.sm,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray[300],
  },
  productCheckbox: {
    marginRight: SPACING.sm,
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    fontSize: FONT_SIZES.sm,
    fontWeight: '500',
    color: COLORS.gray[900],
  },
  productCode: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.gray[600],
    marginTop: 2,
  },
  stockInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  stockText: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.warning,
  },
  quantityControl: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.xs,
  },
  quantityButton: {
    width: 32,
    height: 32,
    borderRadius: BORDER_RADIUS.sm,
    borderWidth: 1,
    borderColor: COLORS.gray[300],
    alignItems: 'center',
    justifyContent: 'center',
  },
  quantityInput: {
    width: 50,
    height: 32,
    borderWidth: 1,
    borderColor: COLORS.gray[300],
    borderRadius: BORDER_RADIUS.sm,
    textAlign: 'center',
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[900],
  },
  quantityInputDisabled: {
    backgroundColor: COLORS.gray[100],
    color: COLORS.gray[400],
  },
  brandSubtotal: {
    marginTop: SPACING.sm,
    paddingTop: SPACING.sm,
    borderTopWidth: 1,
    borderTopColor: COLORS.gray[300],
    alignItems: 'flex-end',
  },
  subtotalText: {
    fontSize: FONT_SIZES.sm,
    fontWeight: '600',
    color: COLORS.gray[600],
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.xl * 3,
  },
  emptyText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.gray[600],
    marginTop: SPACING.md,
  },
  actions: {
    flexDirection: 'row',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.md,
    backgroundColor: COLORS.white,
    borderTopWidth: 1,
    borderTopColor: COLORS.gray[300],
    gap: SPACING.sm,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: SPACING.xs,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.md,
    borderWidth: 1,
    borderColor: COLORS.primary,
    backgroundColor: COLORS.white,
  },
  actionButtonPrimary: {
    backgroundColor: COLORS.primary,
    borderColor: COLORS.primary,
  },
  actionButtonText: {
    fontSize: FONT_SIZES.sm,
    fontWeight: '600',
    color: COLORS.primary,
  },
  actionButtonTextPrimary: {
    color: COLORS.white,
  },
});
