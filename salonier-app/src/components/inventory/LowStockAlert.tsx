import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { COLORS, SPACING, FONT_SIZES, BORDER_RADIUS } from '../../constants';
import { Product } from '../../types';
import Card from '../common/Card';

interface LowStockAlertProps {
  products: Product[];
  onPress?: () => void;
}

export const LowStockAlert: React.FC<LowStockAlertProps> = ({ products, onPress }) => {
  const navigation = useNavigation<any>();
  const lowStockProducts = products.filter(p => p.current_stock <= p.min_stock);

  if (lowStockProducts.length === 0) {
    return null;
  }

  const handlePress = () => {
    if (onPress) {
      onPress();
    } else {
      navigation.navigate('MainTabs', { screen: 'Inventory' });
    }
  };

  const getStockPercentage = (product: Product) => {
    return (product.current_stock / product.min_stock) * 100;
  };

  const getStockColor = (percentage: number) => {
    if (percentage <= 50) return COLORS.error;
    return COLORS.warning;
  };

  return (
    <Card style={styles.container}>
      <TouchableOpacity onPress={handlePress} activeOpacity={0.8}>
        <View style={styles.header}>
          <View style={styles.titleContainer}>
            <MaterialCommunityIcons name="alert-circle" size={24} color={COLORS.error} />
            <Text style={styles.title}>Stock Bajo</Text>
          </View>
          <View style={styles.badge}>
            <Text style={styles.badgeText}>{lowStockProducts.length}</Text>
          </View>
        </View>

        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          style={styles.productsList}
        >
          {lowStockProducts.slice(0, 5).map((product) => {
            const percentage = getStockPercentage(product);
            const color = getStockColor(percentage);
            
            return (
              <View key={product.id} style={styles.productItem}>
                <Text style={styles.productName} numberOfLines={1}>
                  {product.name}
                </Text>
                <View style={styles.stockInfo}>
                  <View style={[styles.stockBar, { backgroundColor: color + '20' }]}>
                    <View 
                      style={[
                        styles.stockBarFill, 
                        { 
                          backgroundColor: color,
                          width: `${Math.min(percentage, 100)}%`
                        }
                      ]} 
                    />
                  </View>
                  <Text style={[styles.stockText, { color }]}>
                    {product.current_stock}/{product.min_stock} {product.unit}
                  </Text>
                </View>
              </View>
            );
          })}
          
          {lowStockProducts.length > 5 && (
            <View style={styles.moreIndicator}>
              <Text style={styles.moreText}>
                +{lowStockProducts.length - 5} más
              </Text>
            </View>
          )}
        </ScrollView>

        <TouchableOpacity style={styles.actionButton} onPress={handlePress}>
          <Text style={styles.actionText}>Ver inventario completo</Text>
          <MaterialCommunityIcons name="chevron-right" size={20} color={COLORS.primary} />
        </TouchableOpacity>
      </TouchableOpacity>
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: SPACING.md,
    marginBottom: SPACING.md,
    padding: 0,
    overflow: 'hidden',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.md,
    paddingBottom: SPACING.sm,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.sm,
  },
  title: {
    fontSize: FONT_SIZES.md,
    fontWeight: '600',
    color: COLORS.gray[900],
  },
  badge: {
    backgroundColor: COLORS.error,
    paddingHorizontal: SPACING.sm,
    paddingVertical: 2,
    borderRadius: BORDER_RADIUS.full,
    minWidth: 24,
    alignItems: 'center',
  },
  badgeText: {
    color: COLORS.white,
    fontSize: FONT_SIZES.xs,
    fontWeight: 'bold',
  },
  productsList: {
    paddingHorizontal: SPACING.md,
  },
  productItem: {
    backgroundColor: COLORS.gray[50],
    padding: SPACING.sm,
    borderRadius: BORDER_RADIUS.sm,
    marginRight: SPACING.sm,
    width: 150,
  },
  productName: {
    fontSize: FONT_SIZES.sm,
    fontWeight: '500',
    color: COLORS.gray[900],
    marginBottom: SPACING.xs,
  },
  stockInfo: {
    gap: SPACING.xs,
  },
  stockBar: {
    height: 4,
    borderRadius: 2,
    overflow: 'hidden',
  },
  stockBarFill: {
    height: '100%',
    borderRadius: 2,
  },
  stockText: {
    fontSize: FONT_SIZES.xs,
    fontWeight: '500',
  },
  moreIndicator: {
    backgroundColor: COLORS.gray[100],
    padding: SPACING.sm,
    borderRadius: BORDER_RADIUS.sm,
    justifyContent: 'center',
    alignItems: 'center',
    minWidth: 80,
  },
  moreText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
    fontWeight: '500',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: SPACING.xs,
    padding: SPACING.md,
    borderTopWidth: 1,
    borderTopColor: COLORS.gray[200],
    backgroundColor: COLORS.gray[50],
  },
  actionText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.primary,
    fontWeight: '600',
  },
});