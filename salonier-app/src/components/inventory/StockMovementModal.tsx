import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ScrollView,
  TextInput,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import Toast from 'react-native-toast-message';
import { Ionicons } from '@expo/vector-icons';
import { Product } from '../../types';
import { StockMovement } from '../../types/improved-types';
import { dataService } from '../../services/dataService';

interface StockMovementModalProps {
  visible: boolean;
  onClose: () => void;
  product: Product | null;
  onMovementAdded: () => void;
}

const MOVEMENT_TYPES = [
  { id: 'purchase', label: 'Compra', icon: 'cart', color: '#4CAF50' },
  { id: 'sale', label: 'Venta', icon: 'cash', color: '#2196F3' },
  { id: 'consumption', label: 'Consumo', icon: 'color-palette', color: '#FF9800' },
  { id: 'adjustment', label: 'Ajuste', icon: 'sync', color: '#9C27B0' },
  { id: 'return', label: 'Devolución', icon: 'return-up-back', color: '#F44336' },
  { id: 'damage', label: 'Daño/Pérdida', icon: 'warning', color: '#E91E63' },
];

export default function StockMovementModal({
  visible,
  onClose,
  product,
  onMovementAdded,
}: StockMovementModalProps) {
  const [movements, setMovements] = useState<StockMovement[]>([]);
  const [showAddForm, setShowAddForm] = useState(false);
  const [movementType, setMovementType] = useState('purchase');
  const [quantity, setQuantity] = useState('');
  const [notes, setNotes] = useState('');
  const [cost, setCost] = useState('');

  useEffect(() => {
    if (product && visible) {
      loadMovements();
    }
  }, [product, visible]);

  const loadMovements = async () => {
    if (!product) return;
    const allMovements = await dataService.inventory.getStockMovements();
    const productMovements = allMovements.filter((m: any) => m.productId === product.id);
    setMovements(
      productMovements.sort(
        (a: any, b: any) =>
          new Date(b.createdAt || b.date).getTime() - new Date(a.createdAt || a.date).getTime()
      )
    );
  };

  const handleAddMovement = async () => {
    if (!product || !quantity) return;

    const quantityNum = parseInt(quantity);
    if (isNaN(quantityNum) || quantityNum <= 0) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'La cantidad debe ser un número positivo',
      });
      return;
    }

    const isIncoming = ['purchase', 'return'].includes(movementType);
    const finalQuantity = isIncoming ? quantityNum : -quantityNum;

    const movement = {
      productId: product.id,
      type: movementType as any,
      quantity: finalQuantity,
      previousStock: product.current_stock,
      newStock: product.current_stock + finalQuantity,
      notes: notes.trim() || undefined,
      cost: cost ? parseFloat(cost) : undefined,
      userId: 'current-user',
      createdAt: new Date().toISOString(),
    };

    await dataService.inventory.addStockMovement(movement);

    // Update product stock
    const newStock = product.current_stock + finalQuantity;
    await dataService.inventory.updateProduct(product.id, { currentStock: newStock });

    // Reset form
    setQuantity('');
    setNotes('');
    setCost('');
    setShowAddForm(false);

    // Reload movements and notify parent
    loadMovements();
    onMovementAdded();
  };

  const getMovementIcon = (type: string) => {
    const movement = MOVEMENT_TYPES.find(m => m.id === type);
    return movement || MOVEMENT_TYPES[0];
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return `Hoy ${date.toLocaleTimeString('es-ES', {
        hour: '2-digit',
        minute: '2-digit',
      })}`;
    } else if (date.toDateString() === yesterday.toDateString()) {
      return `Ayer ${date.toLocaleTimeString('es-ES', {
        hour: '2-digit',
        minute: '2-digit',
      })}`;
    } else {
      return date.toLocaleDateString('es-ES', {
        day: 'numeric',
        month: 'short',
        hour: '2-digit',
        minute: '2-digit',
      });
    }
  };

  if (!product) return null;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.title}>Historial de Movimientos</Text>
          <TouchableOpacity style={styles.addButton} onPress={() => setShowAddForm(!showAddForm)}>
            <Ionicons name={showAddForm ? 'close' : 'add'} size={24} color="#E91E63" />
          </TouchableOpacity>
        </View>

        <View style={styles.productInfo}>
          <Text style={styles.productName}>{product.name}</Text>
          <View style={styles.stockInfo}>
            <Text style={styles.stockLabel}>Stock actual:</Text>
            <Text
              style={[
                styles.stockValue,
                product.current_stock <= product.min_stock && styles.lowStock,
              ]}
            >
              {product.current_stock} {product.unit}
            </Text>
          </View>
        </View>

        {showAddForm && (
          <View style={styles.addForm}>
            <Text style={styles.formTitle}>Registrar Movimiento</Text>

            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              style={styles.typeSelector}
            >
              {MOVEMENT_TYPES.map(type => (
                <TouchableOpacity
                  key={type.id}
                  style={[styles.typeOption, movementType === type.id && styles.typeOptionActive]}
                  onPress={() => setMovementType(type.id)}
                >
                  <Ionicons
                    name={type.icon as any}
                    size={20}
                    color={movementType === type.id ? '#fff' : type.color}
                  />
                  <Text
                    style={[styles.typeLabel, movementType === type.id && styles.typeLabelActive]}
                  >
                    {type.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>

            <View style={styles.inputRow}>
              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Cantidad</Text>
                <TextInput
                  style={styles.input}
                  value={quantity}
                  onChangeText={setQuantity}
                  keyboardType="numeric"
                  placeholder="0"
                />
              </View>

              {['purchase', 'sale'].includes(movementType) && (
                <View style={styles.inputGroup}>
                  <Text style={styles.inputLabel}>
                    {movementType === 'purchase' ? 'Costo' : 'Precio'}
                  </Text>
                  <TextInput
                    style={styles.input}
                    value={cost}
                    onChangeText={setCost}
                    keyboardType="decimal-pad"
                    placeholder="0.00"
                  />
                </View>
              )}
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Notas (opcional)</Text>
              <TextInput
                style={[styles.input, styles.notesInput]}
                value={notes}
                onChangeText={setNotes}
                placeholder="Ej: Pedido #1234, Cliente María..."
                multiline
              />
            </View>

            <TouchableOpacity
              style={[styles.submitButton, !quantity && styles.submitButtonDisabled]}
              onPress={handleAddMovement}
              disabled={!quantity}
            >
              <Text style={styles.submitButtonText}>Registrar Movimiento</Text>
            </TouchableOpacity>
          </View>
        )}

        <ScrollView style={styles.movementsList}>
          {movements.length === 0 ? (
            <View style={styles.emptyState}>
              <Ionicons name="document-text-outline" size={48} color="#ccc" />
              <Text style={styles.emptyText}>No hay movimientos registrados</Text>
              <Text style={styles.emptySubtext}>Toca el + para añadir el primer movimiento</Text>
            </View>
          ) : (
            movements.map(movement => {
              const typeInfo = getMovementIcon(movement.type);
              const isIncoming = movement.quantity > 0;

              return (
                <View key={movement.id} style={styles.movementItem}>
                  <View style={[styles.movementIcon, { backgroundColor: typeInfo.color }]}>
                    <Ionicons name={typeInfo.icon as any} size={20} color="#fff" />
                  </View>

                  <View style={styles.movementDetails}>
                    <Text style={styles.movementType}>{typeInfo.label}</Text>
                    <Text style={styles.movementDate}>
                      {formatDate((movement as any).createdAt || (movement as any).date)}
                    </Text>
                    {(movement as any).notes && (
                      <Text style={styles.movementNotes}>{(movement as any).notes}</Text>
                    )}
                  </View>

                  <View style={styles.movementQuantity}>
                    <Text
                      style={[
                        styles.quantityText,
                        isIncoming ? styles.quantityPositive : styles.quantityNegative,
                      ]}
                    >
                      {isIncoming ? '+' : ''}
                      {movement.quantity} {product.unit}
                    </Text>
                    {movement.cost && (
                      <Text style={styles.costText}>{movement.cost.toFixed(2)}€</Text>
                    )}
                  </View>
                </View>
              );
            })
          )}
        </ScrollView>
      </KeyboardAvoidingView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  closeButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
  },
  addButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'flex-end',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  productInfo: {
    backgroundColor: '#fff',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  productName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  stockInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  stockLabel: {
    fontSize: 14,
    color: '#666',
    marginRight: 8,
  },
  stockValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  lowStock: {
    color: '#F44336',
  },
  addForm: {
    backgroundColor: '#fff',
    padding: 20,
    marginBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  formTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
  },
  typeSelector: {
    marginBottom: 20,
  },
  typeOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    marginRight: 8,
  },
  typeOptionActive: {
    backgroundColor: '#E91E63',
    borderColor: '#E91E63',
  },
  typeLabel: {
    marginLeft: 6,
    fontSize: 14,
    color: '#666',
  },
  typeLabelActive: {
    color: '#fff',
  },
  inputRow: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 16,
  },
  inputGroup: {
    flex: 1,
  },
  inputLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 6,
  },
  input: {
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#333',
  },
  notesInput: {
    minHeight: 60,
    textAlignVertical: 'top',
  },
  submitButton: {
    backgroundColor: '#E91E63',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    marginTop: 8,
  },
  submitButtonDisabled: {
    backgroundColor: '#ccc',
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  movementsList: {
    flex: 1,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    marginTop: 12,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 4,
  },
  movementItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 16,
    marginHorizontal: 20,
    marginTop: 10,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  movementIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  movementDetails: {
    flex: 1,
  },
  movementType: {
    fontSize: 15,
    fontWeight: '500',
    color: '#333',
  },
  movementDate: {
    fontSize: 13,
    color: '#999',
    marginTop: 2,
  },
  movementNotes: {
    fontSize: 13,
    color: '#666',
    marginTop: 4,
  },
  movementQuantity: {
    alignItems: 'flex-end',
  },
  quantityText: {
    fontSize: 16,
    fontWeight: '600',
  },
  quantityPositive: {
    color: '#4CAF50',
  },
  quantityNegative: {
    color: '#F44336',
  },
  costText: {
    fontSize: 13,
    color: '#666',
    marginTop: 2,
  },
});
