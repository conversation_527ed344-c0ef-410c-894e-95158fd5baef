import React, { useState, useEffect } from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  TextInput,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import Toast from 'react-native-toast-message';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { Picker } from '@react-native-picker/picker';
import { COLORS, SPACING, FONT_SIZES, BORDER_RADIUS, SHADOWS } from '../../constants';
import { COMPONENTS, TYPOGRAPHY } from '../../constants/design-system';
import Card from '../common/Card';
import { Product } from '../../types';

interface ProductDetailModalProps {
  visible: boolean;
  onClose: () => void;
  product: Product | null;
  onSave: (product: Product) => void;
  onDelete?: (productId: string) => void;
}

const CATEGORIES = [
  { value: 'dye', label: 'Tinte', icon: 'palette' },
  { value: 'developer', label: 'Oxidante', icon: 'flask' },
  { value: 'treatment', label: 'Tratamiento', icon: 'hair-dryer' },
  { value: 'tool', label: 'Herramienta', icon: 'toolbox' },
];

const UNITS = [
  { value: 'units', label: 'Unidades' },
  { value: 'ml', label: 'Mililitros (ml)' },
  { value: 'g', label: 'Gramos (g)' },
];

export const ProductDetailModal: React.FC<ProductDetailModalProps> = ({
  visible,
  onClose,
  product,
  onSave,
  onDelete,
}) => {
  const isEditing = !!product;

  const [formData, setFormData] = useState<Partial<Product>>({
    name: '',
    brand: '',
    line: '',
    code: '',
    category: 'dye',
    current_stock: 0,
    min_stock: 0,
    unit: 'units',
    purchase_price: 0,
    sale_price: 0,
    notes: '',
  });

  // Removed unused state variables to fix TS6133 errors

  useEffect(() => {
    if (product) {
      setFormData(product);
    } else {
      // Reset form for new product
      setFormData({
        name: '',
        brand: '',
        line: '',
        code: '',
        category: 'dye',
        current_stock: 0,
        min_stock: 0,
        unit: 'units',
        purchase_price: 0,
        sale_price: 0,
        notes: '',
      });
    }
  }, [product, visible]);

  const validateForm = (): boolean => {
    if (!formData.name?.trim()) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'El nombre del producto es requerido',
        position: 'top',
      });
      return false;
    }
    if (!formData.brand?.trim()) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'La marca es requerida',
        position: 'top',
      });
      return false;
    }
    if (!formData.code?.trim()) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'El código del producto es requerido',
        position: 'top',
      });
      return false;
    }
    if (formData.min_stock! < 0) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'El stock mínimo no puede ser negativo',
        position: 'top',
      });
      return false;
    }
    return true;
  };

  const handleSave = () => {
    if (!validateForm()) return;

    const productToSave: Product = {
      id: product?.id || Date.now().toString(),
      name: formData.name!,
      brand: formData.brand!,
      line: formData.line,
      code: formData.code!,
      category: formData.category as Product['category'],
      current_stock: formData.current_stock!,
      min_stock: formData.min_stock!,
      unit: formData.unit as Product['unit'],
      purchase_price: formData.purchase_price!,
      sale_price: formData.sale_price,
      notes: formData.notes,
      last_purchase_date: product?.last_purchase_date || new Date().toISOString(),
      created_at: product?.created_at || new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    onSave(productToSave);
    onClose();
  };

  const handleDelete = () => {
    Toast.show({
      type: 'warning',
      text1: 'Eliminar producto',
      text2: 'Mantener presionado para confirmar eliminación',
      position: 'top',
      visibilityTime: 3000,
      onPress: () => {
        Toast.hide();
        if (product && onDelete) {
          onDelete(product.id);
          onClose();
          Toast.show({
            type: 'success',
            text1: 'Producto eliminado',
            text2: 'El producto se ha eliminado correctamente',
            position: 'top',
          });
        }
      },
    });
  };

  const handleStockAdjustment = (change: number) => {
    const newStock = Math.max(0, formData.current_stock! + change);
    setFormData({ ...formData, current_stock: newStock });
  };

  const getCategoryIcon = (category: string) => {
    return CATEGORIES.find(c => c.value === category)?.icon || 'package';
  };

  return (
    <Modal visible={visible} animationType="slide" transparent={true} onRequestClose={onClose}>
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <View style={styles.modalContent}>
          <View style={styles.header}>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <MaterialCommunityIcons name="close" size={24} color={COLORS.textSecondary} />
            </TouchableOpacity>
            <Text style={styles.title}>{isEditing ? 'Editar producto' : 'Nuevo producto'}</Text>
            <TouchableOpacity onPress={handleSave} style={styles.saveButton}>
              <Text style={styles.saveText}>Guardar</Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
            {/* Información básica */}
            <Card style={styles.section}>
              <Text style={styles.sectionTitle}>Información básica</Text>

              <View style={styles.inputGroup}>
                <Text style={styles.label}>Nombre del producto *</Text>
                <TextInput
                  style={styles.input}
                  value={formData.name}
                  onChangeText={text => setFormData({ ...formData, name: text })}
                  placeholder="Ej: Majirel 7.31"
                />
              </View>

              <View style={styles.row}>
                <View style={[styles.inputGroup, { flex: 1 }]}>
                  <Text style={styles.label}>Marca *</Text>
                  <TextInput
                    style={styles.input}
                    value={formData.brand}
                    onChangeText={text => setFormData({ ...formData, brand: text })}
                    placeholder="Ej: L'Oréal"
                  />
                </View>

                <View style={[styles.inputGroup, { flex: 1, marginLeft: SPACING.sm }]}>
                  <Text style={styles.label}>Línea</Text>
                  <TextInput
                    style={styles.input}
                    value={formData.line}
                    onChangeText={text => setFormData({ ...formData, line: text })}
                    placeholder="Ej: Majirel"
                  />
                </View>
              </View>

              <View style={styles.row}>
                <View style={[styles.inputGroup, { flex: 1 }]}>
                  <Text style={styles.label}>Código *</Text>
                  <TextInput
                    style={styles.input}
                    value={formData.code}
                    onChangeText={text => setFormData({ ...formData, code: text })}
                    placeholder="Ej: 7.31"
                    autoCapitalize="characters"
                  />
                </View>

                <View style={[styles.inputGroup, { flex: 1, marginLeft: SPACING.sm }]}>
                  <Text style={styles.label}>Categoría</Text>
                  <View style={styles.pickerContainer}>
                    <Picker
                      selectedValue={formData.category}
                      onValueChange={value => setFormData({ ...formData, category: value })}
                      style={styles.picker}
                    >
                      {CATEGORIES.map(cat => (
                        <Picker.Item key={cat.value} label={cat.label} value={cat.value} />
                      ))}
                    </Picker>
                  </View>
                </View>
              </View>
            </Card>

            {/* Control de stock */}
            <Card style={styles.section}>
              <View style={styles.sectionHeader}>
                <Text style={styles.sectionTitle}>Control de stock</Text>
                <MaterialCommunityIcons
                  name={getCategoryIcon(formData.category!) as any}
                  size={20}
                  color={COLORS.primary}
                />
              </View>

              <View style={styles.stockControl}>
                <View style={styles.stockInfo}>
                  <Text style={styles.stockLabel}>Stock actual</Text>
                  <Text style={styles.stockValue}>
                    {formData.current_stock} {formData.unit}
                  </Text>
                </View>

                {isEditing && (
                  <View style={styles.stockButtons}>
                    <TouchableOpacity
                      style={[styles.stockButton, styles.stockButtonDanger]}
                      onPress={() => handleStockAdjustment(-1)}
                    >
                      <MaterialCommunityIcons name="minus" size={20} color={COLORS.white} />
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={styles.stockButton}
                      onPress={() => handleStockAdjustment(5)}
                    >
                      <Text style={styles.stockButtonText}>+5</Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={styles.stockButton}
                      onPress={() => handleStockAdjustment(10)}
                    >
                      <Text style={styles.stockButtonText}>+10</Text>
                    </TouchableOpacity>
                  </View>
                )}
              </View>

              {!isEditing && (
                <View style={styles.inputGroup}>
                  <Text style={styles.label}>Stock inicial</Text>
                  <TextInput
                    style={styles.input}
                    value={formData.current_stock?.toString()}
                    onChangeText={text =>
                      setFormData({ ...formData, current_stock: parseInt(text) || 0 })
                    }
                    keyboardType="numeric"
                    placeholder="0"
                  />
                </View>
              )}

              <View style={styles.row}>
                <View style={[styles.inputGroup, { flex: 1 }]}>
                  <Text style={styles.label}>Stock mínimo</Text>
                  <TextInput
                    style={styles.input}
                    value={formData.min_stock?.toString()}
                    onChangeText={text =>
                      setFormData({ ...formData, min_stock: parseInt(text) || 0 })
                    }
                    keyboardType="numeric"
                    placeholder="0"
                  />
                </View>

                <View style={[styles.inputGroup, { flex: 1, marginLeft: SPACING.sm }]}>
                  <Text style={styles.label}>Unidad</Text>
                  <View style={styles.pickerContainer}>
                    <Picker
                      selectedValue={formData.unit}
                      onValueChange={value => setFormData({ ...formData, unit: value })}
                      style={styles.picker}
                    >
                      {UNITS.map(unit => (
                        <Picker.Item key={unit.value} label={unit.label} value={unit.value} />
                      ))}
                    </Picker>
                  </View>
                </View>
              </View>
            </Card>

            {/* Precios */}
            <Card style={styles.section}>
              <Text style={styles.sectionTitle}>Precios</Text>

              <View style={styles.row}>
                <View style={[styles.inputGroup, { flex: 1 }]}>
                  <Text style={styles.label}>Precio compra (€)</Text>
                  <TextInput
                    style={styles.input}
                    value={formData.purchase_price?.toString()}
                    onChangeText={text =>
                      setFormData({ ...formData, purchase_price: parseFloat(text) || 0 })
                    }
                    keyboardType="decimal-pad"
                    placeholder="0.00"
                  />
                </View>

                <View style={[styles.inputGroup, { flex: 1, marginLeft: SPACING.sm }]}>
                  <Text style={styles.label}>Precio venta (€)</Text>
                  <TextInput
                    style={styles.input}
                    value={formData.sale_price?.toString()}
                    onChangeText={text =>
                      setFormData({ ...formData, sale_price: parseFloat(text) || 0 })
                    }
                    keyboardType="decimal-pad"
                    placeholder="0.00"
                  />
                </View>
              </View>

              {formData.purchase_price! > 0 && formData.sale_price! > 0 && (
                <View style={styles.marginInfo}>
                  <Text style={styles.marginLabel}>Margen de ganancia</Text>
                  <Text style={styles.marginValue}>
                    {(
                      ((formData.sale_price! - formData.purchase_price!) /
                        formData.purchase_price!) *
                      100
                    ).toFixed(0)}
                    %
                  </Text>
                </View>
              )}
            </Card>

            {/* Notas */}
            <Card style={styles.section}>
              <Text style={styles.sectionTitle}>Notas adicionales</Text>
              <TextInput
                style={[styles.input, styles.textArea]}
                value={formData.notes}
                onChangeText={text => setFormData({ ...formData, notes: text })}
                placeholder="Información adicional sobre el producto..."
                multiline
                numberOfLines={3}
              />
            </Card>

            {/* Botón eliminar */}
            {isEditing && (
              <TouchableOpacity style={styles.deleteButton} onPress={handleDelete}>
                <MaterialCommunityIcons name="delete-outline" size={20} color={COLORS.error} />
                <Text style={styles.deleteText}>Eliminar producto</Text>
              </TouchableOpacity>
            )}

            <View style={{ height: SPACING.xl * 2 }} />
          </ScrollView>
        </View>
      </KeyboardAvoidingView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: COMPONENTS.modal.overlay.backgroundColor,
  },
  modalContent: {
    ...COMPONENTS.modal.content,
    borderTopLeftRadius: BORDER_RADIUS.xxl,
    borderTopRightRadius: BORDER_RADIUS.xxl,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
    maxHeight: '90%',
    padding: 0,
    ...SHADOWS.xl,
  },
  header: {
    ...COMPONENTS.header.base,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.lg,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surface,
  },
  closeButton: {
    padding: SPACING.xs,
  },
  title: {
    ...COMPONENTS.header.title,
    fontSize: TYPOGRAPHY.size['2xl'],
  },
  saveButton: {
    padding: SPACING.xs,
  },
  saveText: {
    color: COLORS.secondary,
    fontSize: TYPOGRAPHY.size.base,
    fontWeight: '600' as const,
  },
  content: {
    flex: 1,
    padding: SPACING.lg,
  },
  section: {
    marginBottom: SPACING.lg,
  },
  sectionTitle: {
    fontSize: TYPOGRAPHY.size.lg,
    fontWeight: '600' as const,
    color: COLORS.text,
    marginBottom: SPACING.md,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  inputGroup: {
    marginBottom: SPACING.md,
  },
  label: {
    fontSize: TYPOGRAPHY.size.sm,
    fontWeight: '500' as const,
    color: COLORS.textSecondary,
    marginBottom: SPACING.xs,
  },
  input: {
    ...COMPONENTS.input.base,
  },
  textArea: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  row: {
    flexDirection: 'row',
    marginBottom: SPACING.md,
  },
  pickerContainer: {
    backgroundColor: COLORS.surface,
    borderRadius: BORDER_RADIUS.lg,
    overflow: 'hidden',
  },
  picker: {
    height: 44,
  },
  stockControl: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.md,
    backgroundColor: COLORS.surface,
    borderRadius: BORDER_RADIUS.lg,
    marginBottom: SPACING.md,
  },
  stockInfo: {
    alignItems: 'center',
  },
  stockLabel: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.gray[600],
  },
  stockValue: {
    fontSize: FONT_SIZES.xl,
    fontWeight: 'bold',
    color: COLORS.gray[900],
    marginTop: 4,
  },
  stockButtons: {
    flexDirection: 'row',
    gap: SPACING.sm,
  },
  stockButton: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    backgroundColor: COLORS.secondary,
    borderRadius: BORDER_RADIUS.full,
    alignItems: 'center',
    justifyContent: 'center',
  },
  stockButtonDanger: {
    backgroundColor: COLORS.error,
  },
  stockButtonText: {
    color: COLORS.white,
    fontSize: FONT_SIZES.sm,
    fontWeight: '600',
  },
  marginInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.sm,
    backgroundColor: COLORS.success + '20',
    borderRadius: BORDER_RADIUS.sm,
  },
  marginLabel: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.success,
  },
  marginValue: {
    fontSize: FONT_SIZES.md,
    fontWeight: 'bold',
    color: COLORS.success,
  },
  deleteButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: SPACING.sm,
    paddingVertical: SPACING.lg,
    paddingHorizontal: SPACING.xl,
    marginTop: SPACING.md,
    marginBottom: SPACING.xl,
    borderRadius: BORDER_RADIUS.full,
    backgroundColor: COLORS.surface,
  },
  deleteText: {
    color: COLORS.error,
    fontSize: FONT_SIZES.md,
    fontWeight: '500',
  },
});
