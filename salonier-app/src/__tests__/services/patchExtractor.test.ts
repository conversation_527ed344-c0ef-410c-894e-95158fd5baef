import { extractPatches, PATCH_SIZE } from '../../services/patchExtractor';

// Mock de expo-image-manipulator
jest.mock('expo-image-manipulator', () => ({
  manipulateAsync: jest.fn().mockResolvedValue({
    uri: 'mocked-uri',
    width: PATCH_SIZE,
    height: PATCH_SIZE,
    base64: 'mocked-base64-data',
  }),
}));

describe('patchExtractor', () => {
  const mockImageUri = 'file:///path/to/image.jpg';
  const mockImageDimensions = { width: 1000, height: 1000 };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('extractPatches', () => {
    it('should extract 5 patches from different zones', async () => {
      const patches = await extractPatches(mockImageUri, mockImageDimensions);

      expect(patches).toHaveLength(5);
      expect(patches[0]).toHaveProperty('x');
      expect(patches[0]).toHaveProperty('y');
      expect(patches[0]).toHaveProperty('width', PATCH_SIZE);
      expect(patches[0]).toHaveProperty('height', PATCH_SIZE);
      expect(patches[0]).toHaveProperty('data');
    });

    it('should extract patches from correct zones', async () => {
      const patches = await extractPatches(mockImageUri, mockImageDimensions);

      // Top center (roots)
      expect(patches[0].x).toBeCloseTo(mockImageDimensions.width / 2 - PATCH_SIZE / 2);
      expect(patches[0].y).toBeCloseTo(mockImageDimensions.height * 0.2);

      // Middle left
      expect(patches[1].x).toBeCloseTo(mockImageDimensions.width * 0.3);
      expect(patches[1].y).toBeCloseTo(mockImageDimensions.height * 0.5);

      // Middle right
      expect(patches[2].x).toBeCloseTo(mockImageDimensions.width * 0.7 - PATCH_SIZE);
      expect(patches[2].y).toBeCloseTo(mockImageDimensions.height * 0.5);

      // Bottom left (ends)
      expect(patches[3].x).toBeCloseTo(mockImageDimensions.width * 0.3);
      expect(patches[3].y).toBeCloseTo(mockImageDimensions.height * 0.8 - PATCH_SIZE);

      // Bottom right (ends)
      expect(patches[4].x).toBeCloseTo(mockImageDimensions.width * 0.7 - PATCH_SIZE);
      expect(patches[4].y).toBeCloseTo(mockImageDimensions.height * 0.8 - PATCH_SIZE);
    });

    it('should handle small images', async () => {
      const smallDimensions = { width: 150, height: 150 };
      const patches = await extractPatches(mockImageUri, smallDimensions);

      expect(patches).toHaveLength(5);
      patches.forEach(patch => {
        expect(patch.x).toBeGreaterThanOrEqual(0);
        expect(patch.y).toBeGreaterThanOrEqual(0);
        expect(patch.x + patch.width).toBeLessThanOrEqual(smallDimensions.width);
        expect(patch.y + patch.height).toBeLessThanOrEqual(smallDimensions.height);
      });
    });

    it('should throw error for invalid image URI', async () => {
      await expect(extractPatches('', mockImageDimensions)).rejects.toThrow();
    });

    it('should throw error for invalid dimensions', async () => {
      await expect(
        extractPatches(mockImageUri, { width: 0, height: 0 })
      ).rejects.toThrow();
    });
  });
});