import { analyzeColorPatches, getAverageColor, levelToHex } from '../../services/colorAnalysis';

describe('colorAnalysis', () => {
  describe('getAverageColor', () => {
    it('should calculate average color from RGB values', () => {
      const rgbValues = [
        { r: 100, g: 100, b: 100 },
        { r: 200, g: 200, b: 200 },
      ];
      
      const result = getAverageColor(rgbValues);
      
      expect(result).toEqual({
        r: 150,
        g: 150,
        b: 150,
      });
    });

    it('should handle empty array', () => {
      const result = getAverageColor([]);
      
      expect(result).toEqual({
        r: 0,
        g: 0,
        b: 0,
      });
    });

    it('should handle single color', () => {
      const rgbValues = [{ r: 255, g: 128, b: 64 }];
      
      const result = getAverageColor(rgbValues);
      
      expect(result).toEqual({
        r: 255,
        g: 128,
        b: 64,
      });
    });
  });

  describe('levelToHex', () => {
    it('should convert level 1 to black', () => {
      expect(levelToHex(1)).toBe('#000000');
    });

    it('should convert level 10 to light blonde', () => {
      expect(levelToHex(10)).toBe('#FFE5B4');
    });

    it('should convert level 5 to medium brown', () => {
      expect(levelToHex(5)).toBe('#8B4513');
    });

    it('should handle out of range values', () => {
      expect(levelToHex(0)).toBe('#000000');
      expect(levelToHex(11)).toBe('#FFE5B4');
    });
  });

  describe('analyzeColorPatches', () => {
    it('should analyze patches and return color profile', async () => {
      const patches = [
        { x: 0, y: 0, width: 75, height: 75, data: 'base64data1' },
        { x: 100, y: 100, width: 75, height: 75, data: 'base64data2' },
      ];

      const result = await analyzeColorPatches(patches);

      expect(result).toHaveProperty('level');
      expect(result).toHaveProperty('tone');
      expect(result).toHaveProperty('reflect');
      expect(result.level).toBeGreaterThanOrEqual(1);
      expect(result.level).toBeLessThanOrEqual(10);
    });

    it('should handle empty patches array', async () => {
      const result = await analyzeColorPatches([]);

      expect(result).toEqual({
        level: 5,
        tone: 'natural',
        reflect: 'none',
      });
    });
  });
});