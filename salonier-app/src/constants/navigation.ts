import { COLORS } from './index';
import { COMPONENTS } from './design-system';

export const stackScreenOptions = {
  headerShown: true,
  headerStyle: {
    backgroundColor: COLORS.white,
    elevation: 0,
    shadowOpacity: 0,
    borderBottomWidth: 0,
  },
  headerTitleStyle: COMPONENTS.header.title,
  headerTitleAlign: 'center' as const,
  headerTintColor: COLORS.text,
  headerBackTitleVisible: false,
};

export const tabScreenOptions = {
  tabBarActiveTintColor: COMPONENTS.bottomTab.icon.activeColor,
  tabBarInactiveTintColor: COMPONENTS.bottomTab.icon.color,
  tabBarStyle: COMPONENTS.bottomTab.bar,
  tabBarLabelStyle: {
    fontSize: COMPONENTS.bottomTab.label.fontSize,
    marginTop: 2,
  },
  tabBarIconStyle: {
    marginBottom: 0,
  },
  headerStyle: {
    backgroundColor: COLORS.white,
    elevation: 0,
    shadowOpacity: 0,
    borderBottomWidth: 0,
  },
  headerTitleStyle: COMPONENTS.header.title,
  headerTitleAlign: 'center' as const,
};