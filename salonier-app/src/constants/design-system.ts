// Sistema de diseño moderno para Salonier
import { Platform } from 'react-native';
import { COLORS, SPACING, BORDER_RADIUS } from './index';

// Tipografía mejorada
export const TYPOGRAPHY = {
  fontFamily: {
    regular: Platform.select({
      ios: 'Inter',
      android: 'Inter',
      default: 'System',
    }),
    medium: Platform.select({
      ios: 'Inter-Medium',
      android: 'Inter-Medium',
      default: 'System',
    }),
    semibold: Platform.select({
      ios: 'Inter-SemiBold',
      android: 'Inter-SemiBold',
      default: 'System',
    }),
    bold: Platform.select({
      ios: 'Inter-Bold',
      android: 'Inter-Bold',
      default: 'System',
    }),
  },
  
  // Tamaños de texto
  size: {
    xs: 12,
    sm: 14,
    base: 16,
    lg: 18,
    xl: 20,
    '2xl': 22,
    '3xl': 24,
    '4xl': 32,
  },
  
  // Altura de línea
  lineHeight: {
    tight: 1.2,
    normal: 1.5,
    relaxed: 1.75,
  },
  
  // Espaciado de letras
  letterSpacing: {
    tight: -0.015,
    normal: 0,
    wide: 0.015,
  },
};

// Sombras modernas
export const SHADOWS = {
  none: {
    shadowColor: 'transparent',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  sm: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 4,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.12,
    shadowRadius: 8,
    elevation: 6,
  },
  xl: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 8,
  },
};

// Componentes base
export const COMPONENTS = {
  // Botones
  button: {
    primary: {
      backgroundColor: COLORS.secondary,
      color: COLORS.white,
      borderRadius: BORDER_RADIUS.full,
      paddingVertical: SPACING.md,
      paddingHorizontal: SPACING.xl,
      fontWeight: '700' as const,
      fontSize: TYPOGRAPHY.size.base,
      letterSpacing: TYPOGRAPHY.letterSpacing.wide,
    },
    secondary: {
      backgroundColor: COLORS.accent,
      color: COLORS.primary,
      borderRadius: BORDER_RADIUS.full,
      paddingVertical: SPACING.md,
      paddingHorizontal: SPACING.xl,
      fontWeight: '700' as const,
      fontSize: TYPOGRAPHY.size.base,
      letterSpacing: TYPOGRAPHY.letterSpacing.wide,
    },
    ghost: {
      backgroundColor: 'transparent',
      color: COLORS.primary,
      borderRadius: BORDER_RADIUS.full,
      paddingVertical: SPACING.md,
      paddingHorizontal: SPACING.xl,
      fontWeight: '500' as const,
      fontSize: TYPOGRAPHY.size.base,
    },
  },
  
  // Inputs
  input: {
    base: {
      backgroundColor: COLORS.surface,
      borderRadius: BORDER_RADIUS.lg,
      paddingVertical: SPACING.md,
      paddingHorizontal: SPACING.md,
      fontSize: TYPOGRAPHY.size.base,
      color: COLORS.text,
      borderWidth: 0,
    },
    focused: {
      borderWidth: 2,
      borderColor: COLORS.secondary,
    },
    error: {
      borderWidth: 2,
      borderColor: COLORS.error,
    },
  },
  
  // Cards
  card: {
    base: {
      backgroundColor: COLORS.white,
      borderRadius: BORDER_RADIUS.xl,
      padding: SPACING.md,
      ...SHADOWS.sm,
    },
    elevated: {
      backgroundColor: COLORS.white,
      borderRadius: BORDER_RADIUS.xl,
      padding: SPACING.md,
      ...SHADOWS.lg,
    },
  },
  
  // Listas
  listItem: {
    base: {
      backgroundColor: COLORS.white,
      paddingVertical: SPACING.md,
      paddingHorizontal: SPACING.md,
      minHeight: 72,
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      gap: SPACING.md,
    },
    pressed: {
      backgroundColor: COLORS.surfaceHover,
    },
  },
  
  // Headers
  header: {
    base: {
      backgroundColor: COLORS.white,
      paddingHorizontal: SPACING.md,
      paddingTop: SPACING.md,
      paddingBottom: SPACING.sm,
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      justifyContent: 'space-between' as const,
    },
    title: {
      fontSize: TYPOGRAPHY.size.lg,
      fontWeight: '700' as const,
      color: COLORS.text,
      letterSpacing: TYPOGRAPHY.letterSpacing.tight,
    },
  },
  
  // Tabs
  tab: {
    base: {
      paddingVertical: SPACING.md,
      paddingHorizontal: SPACING.sm,
      borderBottomWidth: 3,
      borderBottomColor: 'transparent',
    },
    active: {
      borderBottomColor: COLORS.primary,
    },
    text: {
      fontSize: TYPOGRAPHY.size.sm,
      fontWeight: '700' as const,
      letterSpacing: TYPOGRAPHY.letterSpacing.wide,
    },
  },
  
  // Modal
  modal: {
    overlay: {
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    content: {
      backgroundColor: COLORS.white,
      borderRadius: BORDER_RADIUS.xxl,
      padding: SPACING.lg,
      maxWidth: '90%',
      maxHeight: '80%',
    },
  },
  
  // Avatar
  avatar: {
    small: {
      width: 40,
      height: 40,
      borderRadius: 20,
    },
    medium: {
      width: 56,
      height: 56,
      borderRadius: 28,
    },
    large: {
      width: 80,
      height: 80,
      borderRadius: 40,
    },
    xlarge: {
      width: 128,
      height: 128,
      borderRadius: 64,
    },
  },
  
  // Navigation
  bottomTab: {
    bar: {
      backgroundColor: COLORS.white,
      borderTopWidth: 1,
      borderTopColor: COLORS.gray[100],
      paddingBottom: SPACING.sm,
      paddingTop: SPACING.sm,
    },
    item: {
      flex: 1,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
      gap: 4,
    },
    icon: {
      size: 24,
      color: COLORS.gray[500],
      activeColor: COLORS.primary,
    },
    label: {
      fontSize: TYPOGRAPHY.size.xs,
      color: COLORS.gray[500],
      activeColor: COLORS.primary,
    },
  },
};

// Animaciones
export const ANIMATIONS = {
  duration: {
    fast: 200,
    normal: 300,
    slow: 500,
  },
  easing: {
    easeIn: [0.4, 0, 1, 1],
    easeOut: [0, 0, 0.2, 1],
    easeInOut: [0.4, 0, 0.2, 1],
    spring: [0.175, 0.885, 0.32, 1.275],
  },
};

// Iconos personalizados
export const ICONS = {
  size: {
    xs: 16,
    sm: 20,
    md: 24,
    lg: 32,
    xl: 40,
  },
  strokeWidth: {
    thin: 1,
    regular: 1.5,
    medium: 2,
    bold: 2.5,
  },
};

// Sistema de grids
export const GRID = {
  columns: 12,
  gutter: SPACING.md,
  maxWidth: 1200,
};

// Breakpoints para diseño responsivo
export const BREAKPOINTS = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
};

// Estados de componentes
export const STATES = {
  hover: {
    opacity: 0.8,
  },
  pressed: {
    opacity: 0.6,
    scale: 0.98,
  },
  disabled: {
    opacity: 0.5,
  },
  loading: {
    opacity: 0.7,
  },
};

// Utilidades
export const UTILS = {
  // Función para obtener color de texto basado en el fondo
  getTextColor: (backgroundColor: string): string => {
    // Implementar lógica de contraste
    return backgroundColor === COLORS.primary || backgroundColor === COLORS.secondary
      ? COLORS.white
      : COLORS.text;
  },
  
  // Función para aplicar opacidad a colores hex
  hexToRgba: (hex: string, alpha: number): string => {
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);
    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
  },
};