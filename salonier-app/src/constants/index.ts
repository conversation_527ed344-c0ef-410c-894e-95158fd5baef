// Constantes de la aplicación Salonier

export const COLORS = {
  // Colores principales
  primary: '#131516', // Charcoal oscuro
  secondary: '#1184e3', // Azul vibrante
  accent: '#caddec', // Azul suave
  
  // Neutros
  white: '#FFFFFF',
  black: '#000000',
  gray: {
    50: '#FAFAFA',
    100: '#f1f2f3',
    200: '#dee1e3',
    300: '#c7cdd2',
    400: '#9ca3a8',
    500: '#6b7780',
    600: '#525252',
    700: '#404040',
    800: '#262626',
    900: '#131516',
  },
  
  // Estados
  success: '#10B981',
  error: '#EF4444',
  warning: '#F59E0B',
  info: '#3B82F6',
  
  // Fondos
  background: '#FFFFFF',
  surface: '#f1f2f3',
  surfaceVariant: '#dee1e3',
  surfaceHover: '#e5e7e9',
  
  // Texto
  text: '#131516',
  textSecondary: '#6b7780',
  textTertiary: '#9ca3a8',
};

export const SPACING = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
};

export const FONT_SIZES = {
  xs: 12,
  sm: 14,
  md: 16,
  lg: 18,
  xl: 20,
  xxl: 24,
  xxxl: 32,
};

export const FONT_FAMILIES = {
  regular: 'System',
  medium: 'System',
  bold: 'System',
  light: 'System',
};

export const BORDER_RADIUS = {
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  xxl: 24,
  full: 9999,
};

export const SHADOWS = {
  sm: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
  },
  large: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
  },
};

export const CONSULTATION_STEPS = {
  SELECT_CLIENT: 'select_client',
  SAFETY_CHECK: 'safety_check',
  CURRENT_HAIR: 'current_hair',
  DESIRED_COLOR: 'desired_color',
  FORMULATION: 'formulation',
  BRAND_CONVERSION: 'brand_conversion',
  DOCUMENTATION: 'documentation',
  COMPLETE: 'complete',
};

export const HAIR_LEVELS = [
  { level: 1, name: 'Negro', color: '#000000' },
  { level: 2, name: 'Negro Natural', color: '#1C1C1C' },
  { level: 3, name: 'Castaño Oscuro', color: '#3B2F2F' },
  { level: 4, name: 'Castaño Medio', color: '#4A3C28' },
  { level: 5, name: 'Castaño Claro', color: '#6F4E37' },
  { level: 6, name: 'Rubio Oscuro', color: '#8B6A47' },
  { level: 7, name: 'Rubio Medio', color: '#B8860B' },
  { level: 8, name: 'Rubio Claro', color: '#DAA520' },
  { level: 9, name: 'Rubio Muy Claro', color: '#F0E68C' },
  { level: 10, name: 'Rubio Platino', color: '#FFFACD' },
];

export const UNDERTONES = [
  { id: 'warm', name: 'Cálido', color: '#FF6B35' },
  { id: 'cool', name: 'Frío', color: '#4ECDC4' },
  { id: 'neutral', name: 'Neutro', color: '#95A5A6' },
];

export const REGIONS = [
  { id: 'es', name: 'España', currency: 'EUR', symbol: '€' },
  { id: 'us', name: 'Estados Unidos', currency: 'USD', symbol: '$' },
  { id: 'mx', name: 'México', currency: 'MXN', symbol: '$' },
  { id: 'ar', name: 'Argentina', currency: 'ARS', symbol: '$' },
  { id: 'co', name: 'Colombia', currency: 'COP', symbol: '$' },
  { id: 'cl', name: 'Chile', currency: 'CLP', symbol: '$' },
  { id: 'pe', name: 'Perú', currency: 'PEN', symbol: 'S/' },
];

export const SERVICE_CATEGORIES = [
  { id: 'color', name: 'Coloración', icon: 'palette' },
  { id: 'cut', name: 'Corte', icon: 'content-cut' },
  { id: 'treatment', name: 'Tratamiento', icon: 'spa' },
  { id: 'styling', name: 'Styling', icon: 'hair-dryer' },
];

export const APPOINTMENT_STATUS = {
  scheduled: { label: 'Programada', color: COLORS.info },
  confirmed: { label: 'Confirmada', color: COLORS.success },
  completed: { label: 'Completada', color: COLORS.gray[500] },
  cancelled: { label: 'Cancelada', color: COLORS.error },
};