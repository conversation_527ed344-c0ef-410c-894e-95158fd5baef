import { COLORS, FONT_FAMILIES } from './index';

// Re-export colors with additional properties needed by components
export const colors = {
  ...COLORS,
  text: COLORS.gray[900],
  textLight: COLORS.gray[600],
  border: COLORS.gray[300],
  divider: COLORS.gray[200],
  overlay: 'rgba(0, 0, 0, 0.5)',
  shadow: COLORS.black,
  transparent: 'transparent',
};

// Font definitions
export const fonts = {
  regular: FONT_FAMILIES.regular,
  medium: FONT_FAMILIES.medium,
  semiBold: FONT_FAMILIES.bold,
  bold: FONT_FAMILIES.bold,
  light: FONT_FAMILIES.light,
  mono: 'Courier', // For code/error displays
};

// Re-export everything else from index
export * from './index';