import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { BaseToast, ErrorToast, ToastConfig } from 'react-native-toast-message';
import { COLORS, SPACING, FONT_SIZES } from './index';
import { SHADOWS, TYPOGRAPHY } from './design-system';
import { Icon } from '../components/common/Icon';

export const toastConfig: ToastConfig = {
  success: props => (
    <BaseToast
      {...props}
      style={[styles.base, styles.success]}
      contentContainerStyle={styles.contentContainer}
      text1Style={styles.title}
      text2Style={styles.message}
      renderLeadingIcon={() => (
        <View style={styles.iconContainer}>
          <Icon name="check-circle" size="md" color={COLORS.success} />
        </View>
      )}
    />
  ),

  error: props => (
    <ErrorToast
      {...props}
      style={[styles.base, styles.error]}
      contentContainerStyle={styles.contentContainer}
      text1Style={styles.title}
      text2Style={styles.message}
      renderLeadingIcon={() => (
        <View style={styles.iconContainer}>
          <Icon name="x-circle" size="md" color={COLORS.danger} />
        </View>
      )}
    />
  ),

  info: props => (
    <BaseToast
      {...props}
      style={[styles.base, styles.info]}
      contentContainerStyle={styles.contentContainer}
      text1Style={styles.title}
      text2Style={styles.message}
      renderLeadingIcon={() => (
        <View style={styles.iconContainer}>
          <Icon name="info" size="md" color={COLORS.primary} />
        </View>
      )}
    />
  ),

  warning: props => (
    <BaseToast
      {...props}
      style={[styles.base, styles.warning]}
      contentContainerStyle={styles.contentContainer}
      text1Style={styles.title}
      text2Style={styles.message}
      renderLeadingIcon={() => (
        <View style={styles.iconContainer}>
          <Icon name="alert-triangle" size="md" color={COLORS.warning} />
        </View>
      )}
    />
  ),
};

const styles = StyleSheet.create({
  base: {
    borderLeftWidth: 0,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    minHeight: 80,
    backgroundColor: COLORS.white,
    borderRadius: 12,
    marginHorizontal: SPACING.lg,
    ...SHADOWS.md,
  },
  success: {
    borderLeftWidth: 4,
    borderLeftColor: COLORS.success,
  },
  error: {
    borderLeftWidth: 4,
    borderLeftColor: COLORS.danger,
  },
  info: {
    borderLeftWidth: 4,
    borderLeftColor: COLORS.primary,
  },
  warning: {
    borderLeftWidth: 4,
    borderLeftColor: COLORS.warning,
  },
  contentContainer: {
    paddingHorizontal: SPACING.md,
    flex: 1,
  },
  iconContainer: {
    justifyContent: 'center',
    marginLeft: SPACING.sm,
  },
  title: {
    fontSize: FONT_SIZES.md,
    fontFamily: TYPOGRAPHY.fontFamily.medium,
    color: COLORS.text,
    marginBottom: 2,
  },
  message: {
    fontSize: FONT_SIZES.sm,
    fontFamily: TYPOGRAPHY.fontFamily.regular,
    color: COLORS.textSecondary,
    lineHeight: 18,
  },
});
