import React, { createContext, useContext, useReducer, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { User, Client, Product, Appointment, Service } from '../types/improved-types';

// Estado global de la aplicación
interface AppState {
  user: User | null;
  clients: Client[];
  products: Product[];
  appointments: Appointment[];
  services: Service[];
  loading: boolean;
  error: Error | null;
  lastSync: string | null;
}

// Acciones disponibles
type AppAction =
  | { type: 'SET_USER'; payload: User | null }
  | { type: 'SET_CLIENTS'; payload: Client[] }
  | { type: 'ADD_CLIENT'; payload: Client }
  | { type: 'UPDATE_CLIENT'; payload: { id: string; updates: Partial<Client> } }
  | { type: 'DELETE_CLIENT'; payload: string }
  | { type: 'SET_PRODUCTS'; payload: Product[] }
  | { type: 'UPDATE_PRODUCT_STOCK'; payload: { id: string; stock: number } }
  | { type: 'SET_APPOINTMENTS'; payload: Appointment[] }
  | { type: 'ADD_APPOINTMENT'; payload: Appointment }
  | { type: 'UPDATE_APPOINTMENT'; payload: { id: string; updates: Partial<Appointment> } }
  | { type: 'SET_SERVICES'; payload: Service[] }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: Error | null }
  | { type: 'SET_LAST_SYNC'; payload: string }
  | { type: 'RESET_STATE' };

// Estado inicial
const initialState: AppState = {
  user: null,
  clients: [],
  products: [],
  appointments: [],
  services: [],
  loading: false,
  error: null,
  lastSync: null,
};

// Reducer
function appReducer(state: AppState, action: AppAction): AppState {
  switch (action.type) {
    case 'SET_USER':
      return { ...state, user: action.payload };

    case 'SET_CLIENTS':
      return { ...state, clients: action.payload };

    case 'ADD_CLIENT':
      return { ...state, clients: [...state.clients, action.payload] };

    case 'UPDATE_CLIENT':
      return {
        ...state,
        clients: state.clients.map(client =>
          client.id === action.payload.id
            ? { ...client, ...action.payload.updates }
            : client
        ),
      };

    case 'DELETE_CLIENT':
      return {
        ...state,
        clients: state.clients.filter(client => client.id !== action.payload),
      };

    case 'SET_PRODUCTS':
      return { ...state, products: action.payload };

    case 'UPDATE_PRODUCT_STOCK':
      return {
        ...state,
        products: state.products.map(product =>
          product.id === action.payload.id
            ? { ...product, currentStock: action.payload.stock }
            : product
        ),
      };

    case 'SET_APPOINTMENTS':
      return { ...state, appointments: action.payload };

    case 'ADD_APPOINTMENT':
      return { ...state, appointments: [...state.appointments, action.payload] };

    case 'UPDATE_APPOINTMENT':
      return {
        ...state,
        appointments: state.appointments.map(appointment =>
          appointment.id === action.payload.id
            ? { ...appointment, ...action.payload.updates }
            : appointment
        ),
      };

    case 'SET_SERVICES':
      return { ...state, services: action.payload };

    case 'SET_LOADING':
      return { ...state, loading: action.payload };

    case 'SET_ERROR':
      return { ...state, error: action.payload };

    case 'SET_LAST_SYNC':
      return { ...state, lastSync: action.payload };

    case 'RESET_STATE':
      return initialState;

    default:
      return state;
  }
}

// Contexto
interface AppContextType extends AppState {
  dispatch: React.Dispatch<AppAction>;
  // Métodos de conveniencia
  login: (user: User) => Promise<void>;
  logout: () => Promise<void>;
  syncData: () => Promise<void>;
  isLowStock: (productId: string) => boolean;
  getUpcomingAppointments: (days?: number) => Appointment[];
  getClientById: (id: string) => Client | undefined;
  getProductById: (id: string) => Product | undefined;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

// Keys para AsyncStorage
const STORAGE_KEYS = {
  USER: '@salonier/user',
  CLIENTS: '@salonier/clients',
  PRODUCTS: '@salonier/products',
  APPOINTMENTS: '@salonier/appointments',
  SERVICES: '@salonier/services',
  LAST_SYNC: '@salonier/last_sync',
};

// Provider
export const AppProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // Cargar datos guardados al iniciar
  useEffect(() => {
    loadStoredData();
  }, []);

  // Guardar datos cuando cambien
  useEffect(() => {
    if (state.user) {
      AsyncStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(state.user));
    }
  }, [state.user]);

  useEffect(() => {
    if (state.clients.length > 0) {
      AsyncStorage.setItem(STORAGE_KEYS.CLIENTS, JSON.stringify(state.clients));
    }
  }, [state.clients]);

  useEffect(() => {
    if (state.products.length > 0) {
      AsyncStorage.setItem(STORAGE_KEYS.PRODUCTS, JSON.stringify(state.products));
    }
  }, [state.products]);

  const loadStoredData = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });

      const [userStr, clientsStr, productsStr, appointmentsStr, servicesStr, lastSyncStr] =
        await Promise.all([
          AsyncStorage.getItem(STORAGE_KEYS.USER),
          AsyncStorage.getItem(STORAGE_KEYS.CLIENTS),
          AsyncStorage.getItem(STORAGE_KEYS.PRODUCTS),
          AsyncStorage.getItem(STORAGE_KEYS.APPOINTMENTS),
          AsyncStorage.getItem(STORAGE_KEYS.SERVICES),
          AsyncStorage.getItem(STORAGE_KEYS.LAST_SYNC),
        ]);

      if (userStr) {
        dispatch({ type: 'SET_USER', payload: JSON.parse(userStr) });
      }
      if (clientsStr) {
        dispatch({ type: 'SET_CLIENTS', payload: JSON.parse(clientsStr) });
      }
      if (productsStr) {
        dispatch({ type: 'SET_PRODUCTS', payload: JSON.parse(productsStr) });
      }
      if (appointmentsStr) {
        dispatch({ type: 'SET_APPOINTMENTS', payload: JSON.parse(appointmentsStr) });
      }
      if (servicesStr) {
        dispatch({ type: 'SET_SERVICES', payload: JSON.parse(servicesStr) });
      }
      if (lastSyncStr) {
        dispatch({ type: 'SET_LAST_SYNC', payload: lastSyncStr });
      }
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error as Error });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const login = async (user: User) => {
    dispatch({ type: 'SET_USER', payload: user });
    await syncData();
  };

  const logout = async () => {
    await AsyncStorage.multiRemove(Object.values(STORAGE_KEYS));
    dispatch({ type: 'RESET_STATE' });
  };

  const syncData = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      // Aquí iría la sincronización con el backend
      // Por ahora solo actualizamos la fecha
      const now = new Date().toISOString();
      dispatch({ type: 'SET_LAST_SYNC', payload: now });
      await AsyncStorage.setItem(STORAGE_KEYS.LAST_SYNC, now);
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error as Error });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const isLowStock = (productId: string): boolean => {
    const product = state.products.find(p => p.id === productId);
    if (!product || !product.currentStock || !product.minStock) return false;
    return product.currentStock <= product.minStock;
  };

  const getUpcomingAppointments = (days: number = 7): Appointment[] => {
    const now = new Date();
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + days);

    return state.appointments.filter(appointment => {
      const appointmentDate = new Date(appointment.startTime);
      return appointmentDate >= now && appointmentDate <= futureDate;
    });
  };

  const getClientById = (id: string): Client | undefined => {
    return state.clients.find(client => client.id === id);
  };

  const getProductById = (id: string): Product | undefined => {
    return state.products.find(product => product.id === id);
  };

  const value: AppContextType = {
    ...state,
    dispatch,
    login,
    logout,
    syncData,
    isLowStock,
    getUpcomingAppointments,
    getClientById,
    getProductById,
  };

  return <AppContext.Provider value={value}>{children}</AppContext.Provider>;
};

// Hook para usar el contexto
export function useApp() {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within AppProvider');
  }
  return context;
}

// Hooks específicos para cada parte del estado
export function useUser() {
  const { user } = useApp();
  return user;
}

export function useClients() {
  const { clients, dispatch } = useApp();
  
  const addClient = (client: Client) => {
    dispatch({ type: 'ADD_CLIENT', payload: client });
  };

  const updateClient = (id: string, updates: Partial<Client>) => {
    dispatch({ type: 'UPDATE_CLIENT', payload: { id, updates } });
  };

  const deleteClient = (id: string) => {
    dispatch({ type: 'DELETE_CLIENT', payload: id });
  };

  return { clients, addClient, updateClient, deleteClient };
}

export function useProducts() {
  const { products, dispatch, isLowStock } = useApp();
  
  const updateStock = (id: string, stock: number) => {
    dispatch({ type: 'UPDATE_PRODUCT_STOCK', payload: { id, stock } });
  };

  const lowStockProducts = products.filter(product => 
    product.currentStock && product.minStock && isLowStock(product.id)
  );

  return { products, updateStock, lowStockProducts, isLowStock };
}

export function useAppointments() {
  const { appointments, dispatch, getUpcomingAppointments } = useApp();
  
  const addAppointment = (appointment: Appointment) => {
    dispatch({ type: 'ADD_APPOINTMENT', payload: appointment });
  };

  const updateAppointment = (id: string, updates: Partial<Appointment>) => {
    dispatch({ type: 'UPDATE_APPOINTMENT', payload: { id, updates } });
  };

  const todayAppointments = appointments.filter(appointment => {
    const today = new Date();
    const appointmentDate = new Date(appointment.startTime);
    return (
      appointmentDate.getDate() === today.getDate() &&
      appointmentDate.getMonth() === today.getMonth() &&
      appointmentDate.getFullYear() === today.getFullYear()
    );
  });

  return {
    appointments,
    todayAppointments,
    upcomingAppointments: getUpcomingAppointments(),
    addAppointment,
    updateAppointment,
  };
}