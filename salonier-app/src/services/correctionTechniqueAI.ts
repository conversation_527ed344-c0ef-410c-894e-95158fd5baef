import { HairAnalysis } from '../types';

export interface TechniqueRecommendation {
  techniqueId: string;
  confidence: number;
  reasoning: string;
  warnings?: string[];
  sessionEstimate: '1 session' | '2-3 sessions' | '3+ sessions';
}

export interface CorrectionAnalysisData {
  problemType: string;
  priority: string;
  currentHair: HairAnalysis;
  targetColor: any;
  clientExpectation: string;
  professionalAssessment: string;
}

export class CorrectionTechniqueAI {
  static analyzeTechnique(data: CorrectionAnalysisData): TechniqueRecommendation {
    const { problemType, priority, currentHair, targetColor } = data;

    // Análisis complejo basado en múltiples factores
    const hairLevel = currentHair.natural_level;
    const hairCondition = currentHair.condition;
    const porosity = currentHair.porosity;
    const grayPercentage = currentHair.gray_percentage;

    // Lógica inteligente para cada tipo de problema
    switch (problemType) {
      case 'orange':
        return this.analyzeOrangeProblem(hairLevel, hairCondition, porosity, priority, targetColor);

      case 'green':
        return this.analyzeGreenProblem(hairLevel, hairCondition, priority, targetColor);

      case 'uneven':
        return this.analyzeUnevenProblem(hairLevel, hairCondition, porosity, priority);

      case 'too_dark':
        return this.analyzeTooDoarkProblem(hairLevel, hairCondition, targetColor);

      case 'too_light':
        return this.analyzeTooLightProblem(hairLevel, hairCondition, targetColor, grayPercentage);

      case 'bands':
      case 'spots':
        return this.analyzeUnevennessProblem(hairLevel, hairCondition, problemType);

      case 'yellow':
        return this.analyzeYellowProblem(hairLevel, hairCondition, priority);

      default:
        return {
          techniqueId: 'neutralization',
          confidence: 0.6,
          reasoning: 'Técnica general recomendada para problemas no específicos',
          sessionEstimate: '1 session',
        };
    }
  }

  private static analyzeOrangeProblem(
    hairLevel: number,
    condition: string,
    _porosity: string,
    _priority: string,
    _targetColor: any
  ): TechniqueRecommendation {
    if (hairLevel >= 6 && condition !== 'damaged') {
      return {
        techniqueId: 'neutralization',
        confidence: 0.9,
        reasoning: `Con nivel ${hairLevel} y cabello ${condition}, la neutralización directa es ideal. Los tonos naranjas se eliminan eficazmente con violetas/azules.`,
        sessionEstimate: '1 session',
      };
    }

    if (hairLevel <= 5 || condition === 'damaged') {
      return {
        techniqueId: 'color_bath',
        confidence: 0.8,
        reasoning: `Para cabello nivel ${hairLevel} o dañado, un baño de color es más suave y gradual.`,
        warnings: ['Proceso más lento pero más seguro para el cabello'],
        sessionEstimate: '2-3 sessions',
      };
    }

    return {
      techniqueId: 'neutralization',
      confidence: 0.7,
      reasoning: 'Neutralización estándar para tonos naranjas',
      sessionEstimate: '1 session',
    };
  }

  private static analyzeGreenProblem(
    hairLevel: number,
    _condition: string,
    priority: string,
    targetColor: any
  ): TechniqueRecommendation {
    if (priority === 'darken' || (targetColor?.level && targetColor.level < hairLevel)) {
      return {
        techniqueId: 'pre_pigmentation',
        confidence: 0.95,
        reasoning:
          'Los tonos verdes requieren pre-pigmentación con rojos/cobres antes del color final. Esencial para oscurecer desde cabello decolorado.',
        sessionEstimate: '1 session',
      };
    }

    return {
      techniqueId: 'neutralization',
      confidence: 0.75,
      reasoning: 'Neutralización con pigmentos rojizos para eliminar el verde.',
      warnings: ['Vigilar no sobrecalentar el color'],
      sessionEstimate: '1 session',
    };
  }

  private static analyzeUnevenProblem(
    _hairLevel: number,
    condition: string,
    porosity: string,
    _priority: string
  ): TechniqueRecommendation {
    if (condition === 'damaged' || porosity === 'high') {
      return {
        techniqueId: 'color_bath',
        confidence: 0.85,
        reasoning:
          'Para cabello dañado o poroso, un baño de color permite mayor control y menos agresión.',
        sessionEstimate: '2-3 sessions',
      };
    }

    return {
      techniqueId: 'gentle_lightening',
      confidence: 0.8,
      reasoning: 'Decoloración suave en zonas desiguales seguida de tonalización uniforme.',
      warnings: ['Requiere técnica precisa para evitar más desigualdad'],
      sessionEstimate: '1 session',
    };
  }

  private static analyzeTooDoarkProblem(
    hairLevel: number,
    condition: string,
    targetColor: any
  ): TechniqueRecommendation {
    const targetLevel = targetColor?.level || 7;
    const levelDifference = Math.abs(targetLevel - hairLevel);

    if (levelDifference <= 2 && condition !== 'damaged') {
      return {
        techniqueId: 'gentle_lightening',
        confidence: 0.9,
        reasoning: `Diferencia de ${levelDifference} niveles permite decoloración suave controlada.`,
        sessionEstimate: '1 session',
      };
    }

    if (levelDifference > 2) {
      return {
        techniqueId: 'gentle_lightening',
        confidence: 0.7,
        reasoning: `Diferencia de ${levelDifference} niveles requiere proceso gradual.`,
        warnings: ['Considerar múltiples sesiones para preservar la integridad del cabello'],
        sessionEstimate: '2-3 sessions',
      };
    }

    return {
      techniqueId: 'color_bath',
      confidence: 0.8,
      reasoning: 'Baño de color para ajuste suave sin comprometer el cabello dañado.',
      sessionEstimate: '2-3 sessions',
    };
  }

  private static analyzeTooLightProblem(
    hairLevel: number,
    condition: string,
    targetColor: any,
    grayPercentage: number
  ): TechniqueRecommendation {
    if (grayPercentage > 30) {
      return {
        techniqueId: 'pre_pigmentation',
        confidence: 0.95,
        reasoning: `Con ${grayPercentage}% de canas, la pre-pigmentación es esencial para cobertura uniforme y durabilidad.`,
        sessionEstimate: '1 session',
      };
    }

    return {
      techniqueId: 'pre_pigmentation',
      confidence: 0.9,
      reasoning:
        'Pre-pigmentación necesaria para oscurecer desde cabello muy claro y lograr color uniforme.',
      sessionEstimate: '1 session',
    };
  }

  private static analyzeUnevennessProblem(
    _hairLevel: number,
    _condition: string,
    problemType: string
  ): TechniqueRecommendation {
    if (problemType === 'bands') {
      return {
        techniqueId: 'gentle_lightening',
        confidence: 0.85,
        reasoning: 'Las bandas requieren decoloración selectiva en zonas más oscuras para igualar.',
        warnings: ['Aplicación muy precisa necesaria'],
        sessionEstimate: '1 session',
      };
    }

    // spots
    return {
      techniqueId: 'color_bath',
      confidence: 0.8,
      reasoning: 'Baño de color permite tratar manchas de forma suave y controlada.',
      sessionEstimate: '1 session',
    };
  }

  private static analyzeYellowProblem(
    hairLevel: number,
    condition: string,
    _priority: string
  ): TechniqueRecommendation {
    if (hairLevel >= 8 && condition !== 'damaged') {
      return {
        techniqueId: 'neutralization',
        confidence: 0.95,
        reasoning:
          'En niveles altos, la neutralización con violetas es muy efectiva contra amarillos.',
        sessionEstimate: '1 session',
      };
    }

    return {
      techniqueId: 'color_bath',
      confidence: 0.8,
      reasoning: 'Baño de color matizante para eliminar amarillos gradualmente.',
      sessionEstimate: '1 session',
    };
  }

  // Método para nueva coloración (bonus)
  static analyzeColoringTechnique(
    currentHair: HairAnalysis,
    targetColor: any,
    _clientPreferences: any
  ): TechniqueRecommendation {
    const hairLevel = currentHair.natural_level;
    const targetLevel = targetColor?.level || 7;
    const technique = targetColor?.technique || 'global';

    // Análisis para técnicas de coloración normal
    if (technique === 'global') {
      if (Math.abs(targetLevel - hairLevel) <= 1) {
        return {
          techniqueId: 'global',
          confidence: 0.9,
          reasoning: 'Coloración global ideal para cambios sutiles de tono.',
          sessionEstimate: '1 session',
        };
      }
    }

    if (technique === 'highlights' || technique === 'balayage') {
      if (currentHair.condition === 'healthy' && currentHair.density !== 'thin') {
        return {
          techniqueId: technique,
          confidence: 0.85,
          reasoning: `${technique} recomendado para cabello sano y con densidad adecuada.`,
          sessionEstimate: '1 session',
        };
      }
    }

    return {
      techniqueId: 'global',
      confidence: 0.7,
      reasoning: 'Técnica estándar recomendada.',
      sessionEstimate: '1 session',
    };
  }
}
