import { HairAnalysis, ColorFormulation, Brand } from '../types';

/**
 * Parámetros para generar una formulación con IA
 */
export interface FormulationAIRequest {
  currentAnalysis: HairAnalysis;
  desiredColor: {
    level: number;
    tone: string;
  };
  technique?: string;
  preferredBrands?: string[];
  colorHistory?: any;
}

/**
 * Servicio de IA para generación de formulaciones de color
 * Preparado para integración con OpenAI cuando esté configurado
 */
export class FormulationAIService {
  /**
   * Genera una formulación de color basada en análisis y preferencias
   */
  static async generateFormulation(request: FormulationAIRequest): Promise<ColorFormulation> {
    // En producción: llamada a OpenAI
    /*
    const prompt = this.buildFormulationPrompt(request);
    const response = await openai.chat.completions.create({
      model: "gpt-4",
      messages: [{
        role: "system",
        content: "Eres un experto colorista con 20 años de experiencia en formulaciones de color."
      }, {
        role: "user",
        content: prompt
      }],
      temperature: 0.7,
      max_tokens: 800
    });
    return JSON.parse(response.choices[0].message.content);
    */

    // Simulación mientras no tengamos OpenAI
    await new Promise(resolve => setTimeout(resolve, 1500));

    return this.generateMockFormulation(request);
  }

  /**
   * Genera una formulación mock realista
   */
  private static generateMockFormulation(request: FormulationAIRequest): ColorFormulation {
    const { currentAnalysis, desiredColor, technique = 'global', preferredBrands = [] } = request;
    
    // Determinar marca preferida o usar L'Oréal por defecto
    const brandId = this.selectBrand(preferredBrands);
    const brandName = this.getBrandName(brandId);
    
    // Calcular si necesita aclarado
    const needsLightening = desiredColor.level > currentAnalysis.natural_level;
    const levelDifference = Math.abs(desiredColor.level - currentAnalysis.natural_level);
    
    // Determinar volumen de oxidante
    const developerVolume = this.calculateDeveloperVolume(levelDifference, needsLightening);
    
    // Generar productos de la formulación
    const products = this.generateProducts(
      desiredColor,
      currentAnalysis,
      brandName,
      developerVolume,
      needsLightening
    );
    
    // Calcular tiempo de procesamiento
    const processingTime = this.calculateProcessingTime(
      levelDifference,
      currentAnalysis.gray_percentage,
      technique
    );
    
    // Calcular costos
    const totalCost = this.calculateCost(products);
    const suggestedPrice = totalCost * 2.8; // Margen del 180%
    
    return {
      id: `form_${Date.now()}`,
      consultation_id: '',
      brand_id: brandId,
      product_line_id: 'pl1',
      formula: {
        products,
        developer_volume: developerVolume,
        processing_time: processingTime,
        technique,
      },
      developer_volume: developerVolume,
      processing_time: processingTime,
      technique,
      total_cost: totalCost,
      suggested_price: suggestedPrice,
      created_at: new Date().toISOString(),
    };
  }

  /**
   * Selecciona la marca a usar basada en preferencias
   */
  private static selectBrand(preferredBrands: string[]): string {
    if (preferredBrands.length > 0) {
      // Mapear nombres de marca a IDs
      const brandMap: Record<string, string> = {
        'L\'Oréal': '4',
        'Wella': '1',
        'Schwarzkopf': '2',
        'Matrix': '3',
      };
      
      for (const brand of preferredBrands) {
        if (brandMap[brand]) {
          return brandMap[brand];
        }
      }
    }
    
    return '4'; // L'Oréal por defecto
  }

  /**
   * Obtiene el nombre de la marca por ID
   */
  private static getBrandName(brandId: string): string {
    const brandNames: Record<string, string> = {
      '1': 'Wella',
      '2': 'Schwarzkopf',
      '3': 'Matrix',
      '4': 'L\'Oréal',
    };
    
    return brandNames[brandId] || 'L\'Oréal';
  }

  /**
   * Calcula el volumen de oxidante necesario
   */
  private static calculateDeveloperVolume(levelDifference: number, needsLightening: boolean): number {
    if (!needsLightening) return 10; // Solo depositar color
    if (levelDifference <= 1) return 20;
    if (levelDifference <= 2) return 30;
    return 40; // Para aclarados más intensos
  }

  /**
   * Genera los productos de la formulación
   */
  private static generateProducts(
    desiredColor: { level: number; tone: string },
    currentAnalysis: HairAnalysis,
    brandName: string,
    developerVolume: number,
    needsLightening: boolean
  ) {
    const products = [];
    
    // Tinte principal
    const mainShade = this.generateMainShade(desiredColor, brandName);
    products.push({
      product_id: `shade_${desiredColor.level}`,
      name: mainShade,
      amount: 60,
      unit: 'ml' as const,
    });
    
    // Tinte de refuerzo si hay canas
    if (currentAnalysis.gray_percentage > 30) {
      const baseShade = this.generateBaseShade(desiredColor.level, brandName);
      products.push({
        product_id: `base_${desiredColor.level}`,
        name: baseShade,
        amount: 30,
        unit: 'ml' as const,
      });
    }
    
    // Oxidante
    products.push({
      product_id: `ox_${developerVolume}`,
      name: `Oxidante ${developerVolume} volúmenes`,
      amount: this.calculateDeveloperAmount(products),
      unit: 'ml' as const,
    });
    
    return products;
  }

  /**
   * Genera el tinte principal basado en nivel y tono
   */
  private static generateMainShade(desiredColor: { level: number; tone: string }, brandName: string): string {
    const level = desiredColor.level;
    const toneMap: Record<string, string> = {
      'natural': '',
      'ash': '.1',
      'golden': '.3',
      'copper': '.4',
      'mahogany': '.5',
      'violet': '.2',
      'beige': '.13',
    };
    
    const toneCode = toneMap[desiredColor.tone] || '';
    const productLine = brandName === 'L\'Oréal' ? 'Majirel' : 
                       brandName === 'Wella' ? 'Koleston' :
                       brandName === 'Schwarzkopf' ? 'Igora' : 'SoColor';
    
    return `${productLine} ${level}${toneCode}`;
  }

  /**
   * Genera tinte base para cobertura de canas
   */
  private static generateBaseShade(level: number, brandName: string): string {
    const productLine = brandName === 'L\'Oréal' ? 'Majirel' : 
                       brandName === 'Wella' ? 'Koleston' :
                       brandName === 'Schwarzkopf' ? 'Igora' : 'SoColor';
    
    return `${productLine} ${level} Natural`;
  }

  /**
   * Calcula la cantidad de oxidante (proporción 1:1.5)
   */
  private static calculateDeveloperAmount(colorProducts: any[]): number {
    const totalColorAmount = colorProducts
      .filter(p => !p.name.toLowerCase().includes('oxidante'))
      .reduce((sum, p) => sum + p.amount, 0);
    
    return Math.round(totalColorAmount * 1.5);
  }

  /**
   * Calcula el tiempo de procesamiento
   */
  private static calculateProcessingTime(
    levelDifference: number,
    grayPercentage: number,
    technique: string
  ): number {
    let baseTime = 35; // Tiempo base en minutos
    
    // Ajustar por diferencia de nivel
    if (levelDifference > 2) baseTime += 10;
    if (levelDifference > 3) baseTime += 5;
    
    // Ajustar por canas
    if (grayPercentage > 50) baseTime += 5;
    
    // Ajustar por técnica
    if (technique === 'selective') baseTime -= 5;
    if (technique === 'zones') baseTime += 5;
    
    return Math.max(20, Math.min(50, baseTime));
  }

  /**
   * Calcula el costo estimado de la formulación
   */
  private static calculateCost(products: any[]): number {
    let totalCost = 0;
    
    for (const product of products) {
      // Precios estimados por ml/g
      const pricePerUnit = product.name.toLowerCase().includes('oxidante') ? 0.08 : 0.25;
      totalCost += product.amount * pricePerUnit;
    }
    
    return Math.round(totalCost * 100) / 100; // Redondear a 2 decimales
  }

  /**
   * Construye el prompt para OpenAI (para uso futuro)
   */
  private static buildFormulationPrompt(request: FormulationAIRequest): string {
    const { currentAnalysis, desiredColor, technique, preferredBrands } = request;
    
    return `
Como experto colorista, genera una formulación precisa para:

ANÁLISIS ACTUAL:
- Nivel natural: ${currentAnalysis.natural_level}
- Porcentaje de canas: ${currentAnalysis.gray_percentage}%
- Condición: ${currentAnalysis.condition}
- Porosidad: ${currentAnalysis.porosity}

COLOR DESEADO:
- Nivel: ${desiredColor.level}
- Tono: ${desiredColor.tone}

PREFERENCIAS:
- Técnica: ${technique}
- Marcas preferidas: ${preferredBrands.join(', ') || 'Ninguna especificada'}

Proporciona una formulación detallada en formato JSON con productos específicos, cantidades exactas, volumen de oxidante, tiempo de procesamiento y técnica de aplicación.
    `.trim();
  }
}

// Exportar instancia para compatibilidad
export const formulationAIService = FormulationAIService;
