import { dataService } from './dataService';
import { Product, StockMovement, ColorFormulation } from '../types';

export interface ConsumptionResult {
  success: boolean;
  consumedProducts: ConsumptionItem[];
  insufficientStock: ConsumptionItem[];
  totalCost: number;
  errors?: string[];
}

export interface ConsumptionItem {
  productId: string;
  productName: string;
  amountNeeded: number;
  amountAvailable: number;
  unit: string;
  unitCost?: number;
  totalCost?: number;
}

export class InventoryConsumptionService {
  /**
   * Consume products from inventory based on formulation
   */
  static async consumeFormulation(
    formulation: ColorFormulation,
    userId: string,
    consultationId: string,
    clientName: string
  ): Promise<ConsumptionResult> {
    try {
      // Get all products from inventory
      const inventoryProducts = await dataService.inventory.getProducts(userId);
      
      const consumedProducts: ConsumptionItem[] = [];
      const insufficientStock: ConsumptionItem[] = [];
      const errors: string[] = [];
      let totalCost = 0;

      // Process each product in the formulation
      for (const formulaProduct of formulation.formula.products) {
        // Skip if formula product doesn't have a name
        if (!formulaProduct || !formulaProduct.name) {
          errors.push('Producto en fórmula sin nombre');
          continue;
        }
        
        // Find matching product in inventory
        const inventoryProduct = inventoryProducts.find((p: Product) => 
          p && p.name && this.matchProduct(p, formulaProduct.name)
        );

        if (!inventoryProduct) {
          errors.push(`Producto no encontrado en inventario: ${formulaProduct.name}`);
          continue;
        }

        const amountNeeded = formulaProduct.amount;
        const amountAvailable = inventoryProduct.current_stock || 0;
        const unit = formulaProduct.unit;

        // Calculate cost
        let unitCost = 0;
        let productCost = 0;
        if (inventoryProduct.purchase_price && inventoryProduct.unit_size) {
          unitCost = inventoryProduct.purchase_price / inventoryProduct.unit_size;
          productCost = unitCost * amountNeeded;
          totalCost += productCost;
        }

        const consumptionItem: ConsumptionItem = {
          productId: inventoryProduct.id,
          productName: inventoryProduct.name,
          amountNeeded,
          amountAvailable,
          unit,
          unitCost,
          totalCost: productCost,
        };

        // Check if there's enough stock
        if (amountAvailable >= amountNeeded) {
          consumedProducts.push(consumptionItem);
          
          // Create stock movement
          const newStock = amountAvailable - amountNeeded;
          const movement: Partial<StockMovement> = {
            product_id: inventoryProduct.id,
            user_id: userId,
            type: 'consumption',
            quantity: -amountNeeded,
            unit: unit,
            previous_stock: amountAvailable,
            new_stock: newStock,
            reason: `Consumo en servicio - Cliente: ${clientName}`,
            reference_id: consultationId,
            created_at: new Date().toISOString(),
          };

          // Update stock
          await dataService.inventory.addStockMovement(movement);
          await dataService.inventory.updateProduct(inventoryProduct.id, {
            current_stock: newStock,
          });
        } else {
          insufficientStock.push(consumptionItem);
        }
      }

      return {
        success: insufficientStock.length === 0 && errors.length === 0,
        consumedProducts,
        insufficientStock,
        totalCost,
        errors: errors.length > 0 ? errors : undefined,
      };
    } catch (error) {
      console.error('Error consuming inventory:', error);
      return {
        success: false,
        consumedProducts: [],
        insufficientStock: [],
        totalCost: 0,
        errors: ['Error al procesar el consumo de inventario'],
      };
    }
  }

  /**
   * Check if inventory has sufficient stock for formulation
   */
  static async checkStock(
    formulation: ColorFormulation,
    userId: string
  ): Promise<{
    hasStock: boolean;
    missing: ConsumptionItem[];
    available: ConsumptionItem[];
    totalCost: number;
  }> {
    try {
      const inventoryProducts = await dataService.inventory.getProducts(userId);
      const missing: ConsumptionItem[] = [];
      const available: ConsumptionItem[] = [];
      let totalCost = 0;

      for (const formulaProduct of formulation.formula.products) {
        // Skip if formula product doesn't have a name
        if (!formulaProduct || !formulaProduct.name) {
          continue;
        }
        
        const inventoryProduct = inventoryProducts.find((p: Product) => 
          p && p.name && this.matchProduct(p, formulaProduct.name)
        );

        if (!inventoryProduct) {
          missing.push({
            productId: '',
            productName: formulaProduct.name,
            amountNeeded: formulaProduct.amount,
            amountAvailable: 0,
            unit: formulaProduct.unit,
          });
          continue;
        }

        const amountNeeded = formulaProduct.amount;
        const amountAvailable = inventoryProduct.current_stock || 0;

        // Calculate cost
        let unitCost = 0;
        let productCost = 0;
        if (inventoryProduct.purchase_price && inventoryProduct.unit_size) {
          unitCost = inventoryProduct.purchase_price / inventoryProduct.unit_size;
          productCost = unitCost * amountNeeded;
          totalCost += productCost;
        }

        const item: ConsumptionItem = {
          productId: inventoryProduct.id,
          productName: inventoryProduct.name,
          amountNeeded,
          amountAvailable,
          unit: formulaProduct.unit,
          unitCost,
          totalCost: productCost,
        };

        if (amountAvailable >= amountNeeded) {
          available.push(item);
        } else {
          missing.push(item);
        }
      }

      return {
        hasStock: missing.length === 0,
        missing,
        available,
        totalCost,
      };
    } catch (error) {
      console.error('Error checking stock:', error);
      return {
        hasStock: false,
        missing: [],
        available: [],
        totalCost: 0,
      };
    }
  }

  /**
   * Match product from formulation with inventory
   */
  private static matchProduct(inventoryProduct: Product, formulaProductName: string): boolean {
    // Validate both parameters exist and have required properties
    if (!inventoryProduct || !inventoryProduct.name || !formulaProductName) {
      return false;
    }
    
    const inventoryName = inventoryProduct.name.toLowerCase();
    const formulaName = formulaProductName.toLowerCase();
    
    // Exact match
    if (inventoryName === formulaName) return true;
    
    // Contains match
    if (inventoryName.includes(formulaName) || formulaName.includes(inventoryName)) return true;
    
    // Match by code if available
    if (inventoryProduct.code) {
      const code = inventoryProduct.code.toLowerCase();
      if (formulaName.includes(code) || code === formulaName) return true;
    }
    
    // Match common variations
    const variations = this.getProductVariations(formulaName);
    return variations.some(v => inventoryName.includes(v));
  }

  /**
   * Get common product name variations
   */
  private static getProductVariations(productName: string): string[] {
    const variations: string[] = [];
    
    // Remove common suffixes/prefixes
    const cleanName = productName
      .replace(/\s*\d+vol\s*/gi, '')
      .replace(/\s*\d+%\s*/gi, '')
      .replace(/\s*ml\s*/gi, '')
      .replace(/\s*gr?\s*/gi, '')
      .trim();
    
    variations.push(cleanName);
    
    // Add common variations
    if (cleanName.includes('oxidante') || cleanName.includes('developer')) {
      variations.push('oxidante', 'developer', 'revelador', 'peroxide');
    }
    
    if (cleanName.includes('shampoo') || cleanName.includes('champú')) {
      variations.push('shampoo', 'champú', 'shampu');
    }
    
    return variations;
  }

  /**
   * Calculate real cost based on inventory prices
   */
  static async calculateFormulationCost(
    formulation: ColorFormulation,
    userId: string
  ): Promise<number> {
    const stockCheck = await this.checkStock(formulation, userId);
    return stockCheck.totalCost;
  }
}