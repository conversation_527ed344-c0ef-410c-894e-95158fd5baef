interface ConversionRequest {
  originBrand: string;
  originLine?: string;
  originCode: string;
  targetBrand: string;
  targetLine?: string;
}

interface ConversionResult {
  conversion: string;
  targetLine: string;
  confidence: number;
  alternatives?: {
    line: string;
    code: string;
    reason: string;
  }[];
  adjustments?: string[];
  note?: string;
}

// Información de contexto sobre las marcas
const BRAND_CONTEXT: Record<
  string,
  {
    characteristics: string;
    lines: Record<string, string>;
  }
> = {
  "L'Oréal": {
    characteristics:
      'Base ligeramente verdosa, sistema decimal X.YZ donde X es altura, Y reflejo principal, Z reflejo secundario',
    lines: {
      Majirel: 'Permanente con amoniaco, máxima cobertura de canas',
      INOA: 'Sin amoniaco, base en aceite, más suave',
      'Dia Light': 'Tono sobre tono ácida, sin amoniaco, brillo intenso',
      'Dia Richesse': 'Alcalina suave, sin amoniaco, tonos profundos',
    },
  },
  Wella: {
    characteristics: 'Bases más cálidas/doradas, sistema X/YZ con barra diagonal',
    lines: {
      'Koleston Perfect': 'Permanente, máxima cobertura y duración',
      'Illumina Color': 'Luminosidad y transparencia, menos cobertura',
      'Color Touch': 'Demi-permanente sin amoniaco',
      Blondor: 'Sistema de decoloración',
    },
  },
  Schwarzkopf: {
    characteristics: 'Tonos neutros precisos, sistema X-YZ con guión',
    lines: {
      'Igora Royal': 'Permanente clásica, excelente cobertura',
      'Igora Vibrance': 'Demi-permanente, tonos vibrantes',
      BlondMe: 'Especializada en rubios',
      Essensity: 'Sin amoniaco, orgánica',
    },
  },
  Revlon: {
    characteristics: 'Bases con tendencia rojiza, sistema decimal',
    lines: {
      Revlonissimo: 'Permanente profesional',
      'Young Color': 'Tonos juveniles y vibrantes',
      'Nutri Color': 'Tratamiento y color',
    },
  },
  Alfaparf: {
    characteristics: 'Sistema decimal italiano, tonos mediterráneos',
    lines: {
      Evolution: 'Permanente clásica',
      'Color Wear': 'Sin amoniaco, larga duración',
      'Precious Nature': 'Natural y orgánica',
    },
  },
  Salerm: {
    characteristics: 'Sistema decimal, formulación española',
    lines: {
      Salermvison: 'Permanente profesional',
      'Salerm Color': 'Línea clásica',
      'HD Colors': 'Alta definición de color',
    },
  },
};

function buildConversionPrompt(request: ConversionRequest): string {
  const { originBrand, originLine, originCode, targetBrand, targetLine } = request;

  const originInfo = BRAND_CONTEXT[originBrand] || { characteristics: '', lines: {} };
  const targetInfo = BRAND_CONTEXT[targetBrand] || { characteristics: '', lines: {} };

  return `
Eres un experto colorista profesional con 20 años de experiencia en conversión entre marcas.

SOLICITUD DE CONVERSIÓN:
- ORIGEN: ${originBrand} ${originLine || ''} - Código: ${originCode}
- DESTINO: ${targetBrand} ${targetLine ? `(específicamente ${targetLine})` : '(sugerir mejor línea)'}

INFORMACIÓN TÉCNICA:
${originBrand}: ${originInfo.characteristics || 'Sistema estándar'}
${originLine && originInfo.lines?.[originLine] ? `- ${originLine}: ${originInfo.lines[originLine]}` : ''}

${targetBrand}: ${targetInfo.characteristics || 'Sistema estándar'}
${Object.entries(targetInfo.lines || {})
  .map(([line, desc]) => `- ${line}: ${desc}`)
  .join('\n')}

INSTRUCCIONES DE CONVERSIÓN:
1. Decodifica el código origen según el sistema de ${originBrand}
2. Identifica altura de tono, reflejo principal y secundario
3. Considera las diferencias de base pigmentaria entre marcas
4. ${targetLine ? `Convierte específicamente a ${targetLine}` : 'Sugiere la línea más apropiada basándote en las características'}
5. Si no hay línea destino especificada, proporciona alternativas

FACTORES A CONSIDERAR:
- Diferencias en nomenclatura entre sistemas
- Compensación por bases pigmentarias (ej: L'Oréal verdoso vs Wella dorado)
- Tipo de coloración (permanente, demi, sin amoniaco)
- Cobertura de canas si aplica

Responde ÚNICAMENTE en formato JSON válido:
{
  "conversion": "código exacto en marca destino",
  "targetLine": "línea recomendada o especificada",
  "confidence": número entre 0-100,
  "alternatives": [
    {
      "line": "nombre de línea alternativa",
      "code": "código en esa línea",
      "reason": "breve explicación de por qué esta opción"
    }
  ],
  "adjustments": [
    "ajustes necesarios de tiempo o técnica",
    "consideraciones de aplicación"
  ],
  "note": "observación importante si la hay"
}
`;
}

// Simulación de llamada a OpenAI - En producción usar la API real
async function callOpenAI(prompt: string): Promise<string> {
  // En producción, aquí iría la llamada real a OpenAI
  // Por ahora, simularemos respuestas inteligentes basadas en reglas

  // Simulación de delay de red
  await new Promise(resolve => setTimeout(resolve, 1500));

  // Parser básico para extraer información del prompt
  const originMatch = prompt.match(/ORIGEN: (\S+)\s+(\S+)?\s*-\s*Código:\s*(\S+)/);
  const targetMatch = prompt.match(/DESTINO: (\S+)/);

  if (!originMatch || !targetMatch) {
    throw new Error('No se pudo procesar la solicitud');
  }

  const [, originBrand, originLine, originCode] = originMatch;
  const [, targetBrand] = targetMatch;

  // Lógica de conversión simulada
  let conversion = originCode;
  const confidence = 85;
  const adjustments: string[] = [];

  // Conversiones básicas entre sistemas
  if (originBrand.includes("L'Oréal") && targetBrand === 'Wella') {
    conversion = originCode.replace('.', '/');
    adjustments.push('Aumentar tiempo de procesamiento en 5 minutos');
    adjustments.push(
      'La base de Wella es más cálida, considerar agregar ceniza si se busca neutralidad'
    );
  } else if (originBrand === 'Wella' && targetBrand.includes("L'Oréal")) {
    conversion = originCode.replace('/', '.');
    adjustments.push('Reducir tiempo de procesamiento en 5 minutos');
  } else if (targetBrand === 'Schwarzkopf') {
    conversion = originCode.replace('.', '-').replace('/', '-');
    adjustments.push('Schwarzkopf tiene bases más neutras');
  }

  // Determinar línea apropiada
  let targetLine = 'Koleston Perfect';
  if (targetBrand.includes("L'Oréal")) {
    targetLine = originLine?.includes('INOA') ? 'INOA' : 'Majirel';
  } else if (targetBrand === 'Schwarzkopf') {
    targetLine = 'Igora Royal';
  }

  // Crear respuesta simulada
  const response: ConversionResult = {
    conversion,
    targetLine,
    confidence,
    adjustments,
    alternatives: [
      {
        line: targetLine === 'Koleston Perfect' ? 'Illumina Color' : 'Dia Light',
        code: conversion,
        reason: 'Si buscas más brillo y transparencia',
      },
    ],
  };

  return JSON.stringify(response);
}

export async function convertColorWithAI(request: ConversionRequest): Promise<ConversionResult> {
  try {
    const prompt = buildConversionPrompt(request);

    // En producción: usar OpenAI real
    // const response = await openai.createCompletion({
    //   model: "gpt-4",
    //   prompt,
    //   temperature: 0.1,
    //   max_tokens: 500
    // });
    // const result = JSON.parse(response.choices[0].text);

    // Por ahora usar simulación
    const responseText = await callOpenAI(prompt);
    const result = JSON.parse(responseText);

    // Validar respuesta
    if (!result.conversion || !result.targetLine || typeof result.confidence !== 'number') {
      throw new Error('Respuesta inválida del servicio');
    }

    return result;
  } catch (error) {
    console.error('Error en conversión:', error);
    throw error;
  }
}

// Función auxiliar para obtener información de una línea
export function getLineInfo(brand: string, line: string): string {
  const brandInfo = BRAND_CONTEXT[brand];
  return brandInfo?.lines?.[line] || 'Línea profesional';
}
