// Mock implementation of DataService for development
import { DataService } from './dataService';
import { mockData } from './mockData';

// Helper to simulate async behavior
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Mock user for development
const mockUser = {
  id: '1',
  email: '<EMAIL>',
  full_name: 'Demo User',
  role: 'stylist' as const,
  salon_name: 'Salonier Demo Salon',
  inventory_level: 'full_control' as const,
  behavior_metrics: {
    consultations_completed: 15,
    favorite_products: [
      { name: 'Majirel 7.31', count: 12 },
      { name: 'Oxidante 20 Vol', count: 10 },
    ],
    average_service_price: 75,
    average_margin: 65,
    dismissed_suggestions: [],
    feature_usage: {
      inventory_checked: 5,
      costs_viewed: 0,
      reports_generated: 2,
      brand_conversions: 0,
    },
    last_active_date: new Date().toISOString(),
  },
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
};

export const mockDataService: DataService = {
  auth: {
    signIn: async (email: string, password: string) => {
      await delay(500);
      if (email === '<EMAIL>' && password === 'demo') {
        return { user: mockUser, session: { token: 'mock-token' } };
      }
      throw new Error('Invalid credentials');
    },
    signUp: async (email: string, _password: string, metadata?: any) => {
      await delay(500);
      return { user: { ...mockUser, email, ...metadata }, session: { token: 'mock-token' } };
    },
    signOut: async () => {
      await delay(200);
      return { success: true };
    },
    getCurrentUser: async () => {
      await delay(100);
      return mockUser;
    },
    getSession: async () => {
      await delay(100);
      return { token: 'mock-token', user: mockUser };
    },
  },

  clients: {
    getAll: async (_userId: string) => {
      await delay(300);
      return mockData.clients;
    },
    getById: async (id: string) => {
      await delay(200);
      return mockData.clients.find(c => c.id === id) || null;
    },
    create: async (client: any) => {
      await delay(300);
      const newClient = {
        id: `client-${Date.now()}`,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        ...client,
      };
      mockData.clients.push(newClient);
      return newClient;
    },
    update: async (id: string, updates: any) => {
      await delay(300);
      const index = mockData.clients.findIndex(c => c.id === id);
      if (index !== -1) {
        mockData.clients[index] = {
          ...mockData.clients[index],
          ...updates,
          updated_at: new Date().toISOString(),
        };
        return mockData.clients[index];
      }
      throw new Error('Client not found');
    },
    delete: async (id: string) => {
      await delay(300);
      const index = mockData.clients.findIndex(c => c.id === id);
      if (index !== -1) {
        mockData.clients.splice(index, 1);
        return { success: true };
      }
      throw new Error('Client not found');
    },
    searchByName: async (query: string) => {
      await delay(200);
      const lowerQuery = query.toLowerCase();
      return mockData.clients.filter(c => c.full_name.toLowerCase().includes(lowerQuery));
    },
    getUpcomingRetouch: async (_userId: string) => {
      await delay(300);
      // Return clients that haven't visited in 6+ weeks
      const sixWeeksAgo = new Date();
      sixWeeksAgo.setDate(sixWeeksAgo.getDate() - 42);

      return mockData.clients.filter(c => {
        if (!c.last_visit) return false;
        const lastVisit = new Date(c.last_visit);
        return lastVisit < sixWeeksAgo;
      });
    },
  },

  brands: {
    getAll: async () => {
      await delay(300);
      return mockData.brands;
    },
    getProductLines: async (brandId: string) => {
      await delay(200);
      const brand = mockData.brands.find(b => b.id === brandId);
      return brand?.productLines || [];
    },
    getProducts: async (_productLineId: string) => {
      await delay(200);
      for (const brand of mockData.brands) {
        const line = brand.productLines?.find((pl: any) => pl.id === productLineId);
        if (line) {
          return line.products || [];
        }
      }
      return [];
    },
    searchProducts: async (query: string) => {
      await delay(300);
      const results: any[] = [];
      const lowerQuery = query.toLowerCase();

      for (const brand of mockData.brands) {
        for (const line of brand.productLines || []) {
          for (const product of line.products || []) {
            if (
              product.name.toLowerCase().includes(lowerQuery) ||
              product.code.toLowerCase().includes(lowerQuery)
            ) {
              results.push({
                ...product,
                brandName: brand.name,
                lineName: line.name,
              });
            }
          }
        }
      }
      return results;
    },
    getConversions: async (productId: string) => {
      await delay(200);
      // Mock conversions to other brands
      return [
        { brandId: '2', productCode: 'ALT-123', confidence: 0.95 },
        { brandId: '3', productCode: 'OTHER-456', confidence: 0.85 },
      ];
    },
  },

  appointments: {
    getAll: async (_userId: string) => {
      await delay(300);
      return mockData.appointments;
    },
    getByDate: async (_userId: string, date: Date) => {
      await delay(200);
      const dateStr = date.toISOString().split('T')[0];
      return mockData.appointments.filter(a => a.date && a.date.startsWith(dateStr));
    },
    create: async (appointment: any) => {
      await delay(300);
      const newAppointment = {
        id: `apt-${Date.now()}`,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        status: 'scheduled',
        reminder_sent: false,
        ...appointment,
      };
      mockData.appointments.push(newAppointment);
      return newAppointment;
    },
    update: async (id: string, updates: any) => {
      await delay(300);
      const index = mockData.appointments.findIndex(a => a.id === id);
      if (index !== -1) {
        mockData.appointments[index] = {
          ...mockData.appointments[index],
          ...updates,
          updated_at: new Date().toISOString(),
        };
        return mockData.appointments[index];
      }
      throw new Error('Appointment not found');
    },
    cancel: async (id: string) => {
      await delay(200);
      const index = mockData.appointments.findIndex(a => a.id === id);
      if (index !== -1) {
        mockData.appointments[index].status = 'cancelled';
        return mockData.appointments[index];
      }
      throw new Error('Appointment not found');
    },
  },

  services: {
    getAll: async (_userId: string) => {
      await delay(200);
      return mockData.services;
    },
    getActive: async (_userId: string) => {
      await delay(200);
      return mockData.services.filter(s => s.is_active);
    },
    create: async (service: any) => {
      await delay(300);
      const newService = {
        id: `svc-${Date.now()}`,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        is_active: true,
        is_custom: true,
        ...service,
      };
      mockData.services.push(newService);
      return newService;
    },
    update: async (id: string, updates: any) => {
      await delay(300);
      const index = mockData.services.findIndex(s => s.id === id);
      if (index !== -1) {
        mockData.services[index] = {
          ...mockData.services[index],
          ...updates,
          updated_at: new Date().toISOString(),
        };
        return mockData.services[index];
      }
      throw new Error('Service not found');
    },
  },

  consultations: {
    getAll: async (_userId: string) => {
      await delay(300);
      return mockData.consultations || [];
    },
    getByClient: async (clientId: string) => {
      await delay(200);
      return (mockData.consultations || []).filter(c => c.client_id === clientId);
    },
    create: async (consultation: any) => {
      await delay(300);
      const newConsultation = {
        id: `cons-${Date.now()}`,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        status: 'in_progress',
        ...consultation,
      };
      if (!mockData.consultations) {
        mockData.consultations = [];
      }
      mockData.consultations.push(newConsultation);
      return newConsultation;
    },
    update: async (id: string, updates: any) => {
      await delay(300);
      if (!mockData.consultations) return null;
      const index = mockData.consultations.findIndex(c => c.id === id);
      if (index !== -1) {
        mockData.consultations[index] = {
          ...mockData.consultations[index],
          ...updates,
          updated_at: new Date().toISOString(),
        };
        return mockData.consultations[index];
      }
      throw new Error('Consultation not found');
    },
    getLastByClient: async (clientId: string) => {
      await delay(200);
      const clientConsultations = (mockData.consultations || [])
        .filter(c => c.client_id === clientId && c.status === 'completed')
        .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
      return clientConsultations[0] || null;
    },
    getHistory: async (clientId: string, limit?: number) => {
      await delay(300);
      const consultations = (mockData.consultations || [])
        .filter(c => c.client_id === clientId)
        .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
      return limit ? consultations.slice(0, limit) : consultations;
    },
    updateResult: async (id: string, result: any) => {
      await delay(300);
      if (!mockData.consultations) return null;
      const index = mockData.consultations.findIndex(c => c.id === id);
      if (index !== -1) {
        mockData.consultations[index] = {
          ...mockData.consultations[index],
          ...result,
          updated_at: new Date().toISOString(),
          completed_at: new Date().toISOString(),
          status: 'completed',
        };
        return mockData.consultations[index];
      }
      throw new Error('Consultation not found');
    },
  },

  metrics: {
    getDashboard: async (_userId: string) => {
      await delay(400);
      const today = new Date();
      const todayStr = today.toISOString().split('T')[0];

      // Calculate metrics
      const todayAppointments = mockData.appointments.filter(
        a => a.date && a.date.startsWith(todayStr) && a.status !== 'cancelled'
      );

      const upcomingClients = mockData.clients.filter(c => {
        if (!c.last_visit) return false;
        const lastVisit = new Date(c.last_visit);
        const sixWeeksAgo = new Date();
        sixWeeksAgo.setDate(sixWeeksAgo.getDate() - 42);
        return lastVisit < sixWeeksAgo;
      });

      const lowStockProducts = mockData.products.filter(
        p => p.current_stock && p.min_stock && p.current_stock <= p.min_stock
      );

      return {
        todayAppointments: todayAppointments.length,
        upcomingRetouches: upcomingClients.length,
        lowStockAlerts: lowStockProducts.length,
        completedConsultations: 15,
        monthlyRevenue: 4500,
        topServices: [
          { name: 'Coloración Completa', count: 12 },
          { name: 'Mechas', count: 8 },
          { name: 'Retoque Raíz', count: 15 },
        ],
      };
    },
  },

  inventory: {
    getProducts: async (_userId: string) => {
      await delay(300);
      return mockData.products || [];
    },
    getProductById: async (id: string) => {
      await delay(200);
      return mockData.products.find(p => p.id === id) || null;
    },
    createProduct: async (product: any) => {
      await delay(300);
      const newProduct = {
        id: `prod-${Date.now()}`,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        current_stock: 0,
        is_active: true,
        ...product,
      };
      if (!mockData.products) {
        mockData.products = [];
      }
      mockData.products.push(newProduct);
      return newProduct;
    },
    updateProduct: async (id: string, updates: any) => {
      await delay(300);
      const index = mockData.products.findIndex(p => p.id === id);
      if (index !== -1) {
        mockData.products[index] = {
          ...mockData.products[index],
          ...updates,
          updated_at: new Date().toISOString(),
        };
        return mockData.products[index];
      }
      throw new Error('Product not found');
    },
    deleteProduct: async (id: string) => {
      await delay(300);
      const index = mockData.products.findIndex(p => p.id === id);
      if (index !== -1) {
        mockData.products.splice(index, 1);
        return { success: true };
      }
      throw new Error('Product not found');
    },
    addStockMovement: async (movement: any) => {
      await delay(300);
      const newMovement = {
        id: `mov-${Date.now()}`,
        created_at: new Date().toISOString(),
        ...movement,
      };

      // Update product stock
      const product = mockData.products.find(p => p.id === movement.product_id);
      if (product) {
        product.current_stock = movement.new_stock;
      }

      if (!mockData.stockMovements) {
        mockData.stockMovements = [];
      }
      mockData.stockMovements.push(newMovement);
      return newMovement;
    },
    getStockMovements: async (productId: string) => {
      await delay(200);
      return (mockData.stockMovements || []).filter(m => m.product_id === productId);
    },
    getLowStockProducts: async (_userId: string) => {
      await delay(300);
      return mockData.products.filter(
        p =>
          p.current_stock !== undefined &&
          p.min_stock !== undefined &&
          p.current_stock <= p.min_stock
      );
    },
  },

  users: {
    getById: async (id: string) => {
      await delay(200);
      // For now, return the mock user if id matches
      if (id === mockUser.id) {
        return mockUser;
      }
      throw new Error('User not found');
    },
    update: async (id: string, updates: any) => {
      await delay(300);
      if (id === mockUser.id) {
        Object.assign(mockUser, updates, {
          updated_at: new Date().toISOString(),
        });
        return mockUser;
      }
      throw new Error('User not found');
    },
    updateBehaviorMetrics: async (id: string, metrics: any) => {
      await delay(200);
      if (id === mockUser.id) {
        mockUser.behavior_metrics = {
          ...mockUser.behavior_metrics,
          ...metrics,
          last_active_date: new Date().toISOString(),
        };
        return mockUser;
      }
      throw new Error('User not found');
    },
    updatePreferences: async (id: string, preferences: any) => {
      await delay(200);
      if (id === mockUser.id) {
        // Merge preferences correctly, maintaining the User structure
        mockUser = {
          ...mockUser,
          ...preferences,
          updated_at: new Date().toISOString(),
        };
        return mockUser;
      }
      throw new Error('User not found');
    },
  },
};
