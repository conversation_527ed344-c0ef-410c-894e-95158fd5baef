// Capa de abstracción para servicios de datos
// Permite cambiar entre mock y real con una variable de entorno

import { mockDataService } from './mockDataService';
import { DataService } from '../types/data-service';
// import { supabaseDataService } from './supabaseDataService'; // Implementar cuando esté listo

// Configuración: usar mock data por defecto durante desarrollo
const USE_MOCK_DATA = true; // Cambiar a false cuando Supabase esté configurado

// Re-export DataService interface from types
export type { DataService } from '../types/data-service';

// Exportar el servicio activo según configuración
export const dataService: DataService = USE_MOCK_DATA 
  ? mockDataService 
  : mockDataService; // Por ahora siempre mock, cambiar cuando tengamos supabaseDataService

// Helpers para verificar el modo actual
export const isUsingMockData = () => USE_MOCK_DATA;
export const getDataMode = () => USE_MOCK_DATA ? 'mock' : 'supabase';