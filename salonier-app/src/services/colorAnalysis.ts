import { ColorPatch } from './patchExtractor';

export interface ColorAnalysisResult {
  level: number; // 1-10
  undertone: 'warm' | 'cool' | 'neutral';
  reflectPercentage: number;
  dominantColor: string; // Hex color
  confidence: number; // 0-100
}

export interface HairColorProfile {
  roots: ColorAnalysisResult;
  mid: ColorAnalysisResult;
  ends: ColorAnalysisResult;
  overall: {
    averageLevel: number;
    uniformity: 'uniform' | 'slight_variation' | 'high_variation';
    condition: 'healthy' | 'damaged' | 'very_damaged';
    recommendations: string[];
  };
}

export class ColorAnalysis {
  /**
   * Analiza un parche de color usando OpenAI Vision
   * En desarrollo usa análisis simulado
   */
  static async analyzePatch(patch: ColorPatch): Promise<ColorAnalysisResult> {
    try {
      // En producción aquí iría la llamada a OpenAI Vision:
      /*
      const response = await openai.chat.completions.create({
        model: "gpt-4-vision-preview",
        messages: [{
          role: "user",
          content: [
            {
              type: "text",
              text: "Analyze this hair color sample. Return: level (1-10), undertone (warm/cool/neutral), and reflect percentage. JSON format only."
            },
            {
              type: "image_url",
              image_url: { url: `data:image/jpeg;base64,${patch.base64}` }
            }
          ]
        }],
        max_tokens: 150
      });
      */

      // Por ahora, simulamos el análisis basado en la zona
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Generar resultados simulados basados en la zona con decimales
      let baseLevel = 6;
      let undertone: 'warm' | 'cool' | 'neutral' = 'neutral';
      
      switch (patch.zone) {
        case 'roots':
          // Genera niveles como 4.0, 4.3, 4.5, 4.7, 5.0
          const rootBase = Math.floor(Math.random() * 2) + 4;
          const rootDecimal = [0, 0.3, 0.5, 0.7][Math.floor(Math.random() * 4)];
          baseLevel = rootBase + rootDecimal;
          undertone = 'neutral';
          break;
        case 'mid':
          // Genera niveles como 5.0, 5.3, 5.5, 5.7, 6.0, 6.3, 6.5, 6.7
          const midBase = Math.floor(Math.random() * 2) + 5;
          const midDecimal = [0, 0.3, 0.5, 0.7][Math.floor(Math.random() * 4)];
          baseLevel = midBase + midDecimal;
          undertone = ['warm', 'neutral', 'cool'][Math.floor(Math.random() * 3)] as any;
          break;
        case 'ends':
          // Genera niveles como 6.0, 6.3, 6.5, 6.7, 7.0, 7.3, 7.5, 7.7, 8.0
          const endBase = Math.floor(Math.random() * 3) + 6;
          const endDecimal = [0, 0.3, 0.5, 0.7][Math.floor(Math.random() * 4)];
          baseLevel = endBase + endDecimal;
          undertone = 'warm';
          break;
      }
      
      return {
        level: baseLevel,
        undertone,
        reflectPercentage: Math.floor(Math.random() * 30) + 10,
        dominantColor: this.levelToHexColor(baseLevel),
        confidence: Math.floor(Math.random() * 20) + 80 // 80-100
      };
    } catch (error) {
      console.error('Error analyzing patch:', error);
      throw error;
    }
  }

  /**
   * Combina los resultados de múltiples parches en un perfil completo
   */
  static async analyzeMultiplePatches(patches: ColorPatch[]): Promise<HairColorProfile> {
    try {
      // Analizar cada parche
      const analyses = await Promise.all(
        patches.map(patch => this.analyzePatch(patch))
      );
      
      // Agrupar por zona
      const zoneAnalyses = {
        roots: [] as ColorAnalysisResult[],
        mid: [] as ColorAnalysisResult[],
        ends: [] as ColorAnalysisResult[]
      };
      
      patches.forEach((patch, index) => {
        zoneAnalyses[patch.zone].push(analyses[index]);
      });
      
      // Promediar resultados por zona
      const averageResults = (results: ColorAnalysisResult[]): ColorAnalysisResult => {
        if (results.length === 0) {
          return {
            level: 6,
            undertone: 'neutral',
            reflectPercentage: 20,
            dominantColor: '#8B6A47',
            confidence: 0
          };
        }
        
        const avgLevel = Math.round(
          results.reduce((sum, r) => sum + r.level, 0) / results.length
        );
        
        const avgReflect = Math.round(
          results.reduce((sum, r) => sum + r.reflectPercentage, 0) / results.length
        );
        
        const avgConfidence = Math.round(
          results.reduce((sum, r) => sum + r.confidence, 0) / results.length
        );
        
        // Tono más común
        const tones = results.map(r => r.undertone);
        const mostCommonTone = this.getMostCommon(tones) || 'neutral';
        
        return {
          level: avgLevel,
          undertone: mostCommonTone,
          reflectPercentage: avgReflect,
          dominantColor: this.levelToHexColor(avgLevel),
          confidence: avgConfidence
        };
      };
      
      const profile: HairColorProfile = {
        roots: averageResults(zoneAnalyses.roots),
        mid: averageResults(zoneAnalyses.mid),
        ends: averageResults(zoneAnalyses.ends),
        overall: {
          averageLevel: 0,
          uniformity: 'uniform',
          condition: 'healthy',
          recommendations: []
        }
      };
      
      // Calcular métricas generales
      profile.overall.averageLevel = Math.round(
        (profile.roots.level + profile.mid.level + profile.ends.level) / 3
      );
      
      // Determinar uniformidad
      const levelDifference = Math.max(
        profile.roots.level,
        profile.mid.level,
        profile.ends.level
      ) - Math.min(
        profile.roots.level,
        profile.mid.level,
        profile.ends.level
      );
      
      if (levelDifference <= 1) {
        profile.overall.uniformity = 'uniform';
      } else if (levelDifference <= 2) {
        profile.overall.uniformity = 'slight_variation';
      } else {
        profile.overall.uniformity = 'high_variation';
      }
      
      // Determinar condición basada en diferencias
      if (profile.ends.level > profile.roots.level + 2) {
        profile.overall.condition = 'damaged';
        profile.overall.recommendations.push('Las puntas están muy claras, indica daño. Considerar tratamiento reconstructor.');
      }
      
      // Generar recomendaciones
      if (profile.overall.uniformity === 'high_variation') {
        profile.overall.recommendations.push('Alta variación de color detectada. Se recomienda fórmula multi-zona.');
      }
      
      if (profile.roots.reflectPercentage > 30) {
        profile.overall.recommendations.push('Alto porcentaje de canas en raíces detectado.');
      }
      
      return profile;
    } catch (error) {
      console.error('Error analyzing multiple patches:', error);
      throw error;
    }
  }

  /**
   * Convierte nivel de cabello a color hex aproximado
   */
  private static levelToHexColor(level: number): string {
    const colors = [
      '#000000', // 1 - Negro
      '#1C1C1C', // 2 - Negro oscuro
      '#2F1F0F', // 3 - Castaño muy oscuro
      '#3D2314', // 4 - Castaño oscuro
      '#5D4037', // 5 - Castaño medio
      '#8B6A47', // 6 - Castaño claro
      '#A0826D', // 7 - Rubio oscuro
      '#D4A574', // 8 - Rubio medio
      '#F5DEB3', // 9 - Rubio claro
      '#FFFACD'  // 10 - Rubio muy claro
    ];
    
    return colors[Math.max(0, Math.min(level - 1, 9))];
  }

  /**
   * Encuentra el elemento más común en un array
   */
  private static getMostCommon<T>(arr: T[]): T | undefined {
    if (arr.length === 0) return undefined;
    
    const counts = new Map<T, number>();
    let maxCount = 0;
    let mostCommon = arr[0];
    
    for (const item of arr) {
      const count = (counts.get(item) || 0) + 1;
      counts.set(item, count);
      
      if (count > maxCount) {
        maxCount = count;
        mostCommon = item;
      }
    }
    
    return mostCommon;
  }

  /**
   * Genera un resumen del análisis para mostrar al usuario
   */
  static generateSummary(profile: HairColorProfile): string {
    const summaryParts = [
      `Nivel promedio: ${profile.overall.averageLevel.toFixed(1)}`,
      `Raíces: Nivel ${profile.roots.level.toFixed(1)} (${profile.roots.undertone})`,
      `Medios: Nivel ${profile.mid.level.toFixed(1)} (${profile.mid.undertone})`,
      `Puntas: Nivel ${profile.ends.level.toFixed(1)} (${profile.ends.undertone})`,
      `Uniformidad: ${
        profile.overall.uniformity === 'uniform' ? 'Uniforme' :
        profile.overall.uniformity === 'slight_variation' ? 'Variación ligera' :
        'Alta variación'
      }`
    ];
    
    return summaryParts.join('\n');
  }
}