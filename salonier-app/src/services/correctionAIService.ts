import {
  CorrectionAIRequest,
  CorrectionFormulation,
  CorrectionDiagnosis,
  CorrectionData,
} from '../types/correction-types';
import { HairAnalysis } from '../types';

/**
 * Servicio de IA para correcciones de color
 * Preparado para integración con OpenAI cuando esté configurado
 */
export class CorrectionAIService {
  /**
   * Analiza el problema de color y genera un diagnóstico
   */
  static async analyzeColorProblem(
    analysis: HairAnalysis,
    correctionData: CorrectionData
  ): Promise<CorrectionDiagnosis> {
    // En producción: llamada a OpenAI
    /*
    const prompt = this.buildDiagnosisPrompt(analysis, correctionData);
    const response = await openai.chat.completions.create({
      model: "gpt-4",
      messages: [{
        role: "system",
        content: "Eres un experto colorista con 20 años de experiencia en correcciones de color."
      }, {
        role: "user",
        content: prompt
      }],
      temperature: 0.7,
      max_tokens: 500
    });
    return JSON.parse(response.choices[0].message.content);
    */

    // Simulación mientras no tengamos OpenAI
    await new Promise(resolve => setTimeout(resolve, 800));

    // Diagnóstico basado en el problema
    const severity = this.calculateSeverity(correctionData, analysis);
    const canCorrectInOneSession = severity !== 'severe' && !correctionData.bleachingDone;

    return {
      severity,
      canCorrectInOneSession,
      recommendedApproach: this.getRecommendedApproach(correctionData.problem, severity),
      risks: this.getRisks(correctionData, analysis),
      alternativeOptions: this.getAlternatives(correctionData.problem),
    };
  }

  /**
   * Genera una formulación correctiva con IA
   */
  static async generateCorrectionFormula(
    request: CorrectionAIRequest
  ): Promise<CorrectionFormulation> {
    // En producción: llamada a OpenAI
    /*
    const prompt = this.buildFormulationPrompt(request);
    const response = await openai.chat.completions.create({
      model: "gpt-4",
      messages: [{
        role: "system",
        content: `Eres un maestro colorista especializado en correcciones complejas. 
                  Conoces todas las marcas profesionales y la teoría del color.
                  Las marcas favoritas del cliente son: ${request.userBrands.join(', ')}`
      }, {
        role: "user",
        content: prompt
      }],
      temperature: 0.6,
      max_tokens: 1000
    });
    return JSON.parse(response.choices[0].message.content);
    */

    // Simulación basada en el tipo de problema
    await new Promise(resolve => setTimeout(resolve, 1200));

    return this.generateMockCorrectionFormula(request);
  }

  /**
   * Construye el prompt para diagnóstico
   */
  private static buildDiagnosisPrompt(
    analysis: HairAnalysis,
    correctionData: CorrectionData
  ): string {
    return `
DIAGNÓSTICO DE CORRECCIÓN DE COLOR

PROBLEMA REPORTADO:
- Tipo: ${correctionData.problem}
- Descripción: ${correctionData.problemDescription}
- Última coloración: ${correctionData.lastColorDate}
- Productos usados: ${correctionData.productsUsed}
- Decoloración previa: ${correctionData.bleachingDone ? 'Sí' : 'No'}

ESTADO ACTUAL DEL CABELLO:
- Nivel natural: ${analysis.natural_level}
- Condición: ${analysis.condition}
- Porosidad: ${analysis.porosity}
- Densidad: ${analysis.density}
- % Canas: ${analysis.gray_percentage}

Por favor analiza y devuelve un JSON con:
{
  "severity": "mild|moderate|severe",
  "canCorrectInOneSession": boolean,
  "recommendedApproach": "descripción detallada",
  "risks": ["lista de riesgos"],
  "alternativeOptions": ["opciones alternativas"]
}
`;
  }

  /**
   * Construye el prompt para formulación
   */
  private static buildFormulationPrompt(request: CorrectionAIRequest): string {
    const { problemType, currentAnalysis, correctionData, userBrands } = request;

    return `
FORMULACIÓN DE CORRECCIÓN DE COLOR

PROBLEMA A CORREGIR: ${problemType}
${correctionData.problemDescription}

ANÁLISIS ACTUAL:
- Nivel: ${currentAnalysis.natural_level}
- Subtono: ${currentAnalysis.undertone}
- Condición: ${currentAnalysis.condition}
- Última coloración: ${correctionData.lastColorDate}

MARCAS DISPONIBLES: ${userBrands.join(', ')}

Genera una formulación correctiva considerando:
1. Teoría del color wheel para neutralización
2. Estado actual del cabello
3. Productos de las marcas disponibles
4. Mínimo daño posible

Devuelve JSON con la estructura CorrectionFormulation.
`;
  }

  /**
   * Calcula la severidad del problema
   */
  private static calculateSeverity(
    correctionData: CorrectionData,
    analysis: HairAnalysis
  ): 'mild' | 'moderate' | 'severe' {
    // Factores que aumentan severidad
    let severityScore = 0;

    // Problema base
    if (['orange', 'green'].includes(correctionData.problem)) severityScore += 1;
    if (['too_dark', 'uneven'].includes(correctionData.problem)) severityScore += 2;

    // Condición del cabello
    if (analysis.condition === 'damaged') severityScore += 2;
    if (analysis.condition === 'very_healthy') severityScore -= 1;

    // Tratamientos recientes
    if (correctionData.bleachingDone) severityScore += 2;
    if (correctionData.lastColorDate === 'Hoy' || correctionData.lastColorDate === 'Ayer') {
      severityScore += 2;
    }

    // Alta porosidad
    if (analysis.porosity === 'high') severityScore += 1;

    if (severityScore <= 2) return 'mild';
    if (severityScore <= 5) return 'moderate';
    return 'severe';
  }

  /**
   * Obtiene el enfoque recomendado según el problema
   */
  private static getRecommendedApproach(problem: string, severity: string): string {
    const approaches = {
      orange: {
        mild: 'Aplicar toner ceniza/violeta nivel 8-9 con oxidante 10vol por 15-20 minutos',
        moderate: 'Pre-pigmentar con violeta, luego aplicar color base ceniza',
        severe: 'Corrección en 2-3 sesiones con tratamientos intermedios',
      },
      green: {
        mild: 'Neutralizar con tono cobrizo/rojizo, oxidante 10vol',
        moderate: 'Pre-pigmentación roja seguida de color objetivo',
        severe: 'Decapado suave y reconstrucción del color',
      },
      uneven: {
        mild: 'Igualación con fórmula multi-zona',
        moderate: 'Técnica de relleno en zonas claras, matización en oscuras',
        severe: 'Igualación progresiva en múltiples sesiones',
      },
      too_dark: {
        mild: 'Baño de color o decapado muy suave',
        moderate: 'Decapado controlado con mordiente',
        severe: 'Aclarado progresivo con decoloración suave',
      },
      too_light: {
        mild: 'Repigmentación directa al tono deseado',
        moderate: 'Repigmentación en 2 pasos: relleno + color',
        severe: 'Reconstrucción gradual del pigmento',
      },
    };

    const problemApproaches = approaches[problem as keyof typeof approaches];
    if (!problemApproaches) return 'Evaluación personalizada necesaria';
    return (
      problemApproaches[severity as keyof typeof problemApproaches] ||
      'Evaluación personalizada necesaria'
    );
  }

  /**
   * Identifica riesgos según el caso
   */
  private static getRisks(correctionData: CorrectionData, analysis: HairAnalysis): string[] {
    const risks = [];

    if (analysis.condition === 'damaged') {
      risks.push('Riesgo de rotura por daño previo');
    }

    if (correctionData.bleachingDone) {
      risks.push('Cabello sensibilizado por decoloración reciente');
    }

    if (correctionData.lastColorDate === 'Hoy' || correctionData.lastColorDate === 'Ayer') {
      risks.push('Sobreprocesamiento por coloración muy reciente');
    }

    if (correctionData.problem === 'too_dark') {
      risks.push('Posible viraje a tonos no deseados durante el aclarado');
    }

    if (analysis.porosity === 'high') {
      risks.push('Absorción irregular del color por alta porosidad');
    }

    return risks;
  }

  /**
   * Sugiere alternativas
   */
  private static getAlternatives(problem: string): string[] {
    const alternatives = {
      orange: [
        'Matización progresiva con shampoo violeta',
        'Gloss/glaze temporal para neutralizar',
        'Baño de color con pigmentos fríos',
      ],
      green: [
        'Lavados clarificantes para atenuar',
        'Mascarilla con pigmentos cálidos',
        'Color de fantasía que incorpore el verde',
      ],
      uneven: [
        'Técnica de lowlights para disimular',
        'Balayage correctivo',
        'Transición a color más oscuro uniforme',
      ],
      too_dark: [
        'Reflejos estratégicos para dar luminosidad',
        'Técnica de barrido superficial',
        'Esperar deslavado natural con cuidados',
      ],
      too_light: [
        'Tono sobre tono progresivo',
        'Técnica reverse balayage',
        'Glossing para profundidad',
      ],
    };

    return (
      alternatives[problem as keyof typeof alternatives] || ['Consulta personalizada recomendada']
    );
  }

  /**
   * Genera una formulación mock para desarrollo
   */
  private static generateMockCorrectionFormula(
    request: CorrectionAIRequest
  ): CorrectionFormulation {
    const { problemType, currentAnalysis, correctionData, userBrands } = request;
    const primaryBrand = userBrands[0] || "L'Oréal";

    // Base según el tipo de problema
    const formulations = {
      orange: {
        mainProducts: [
          {
            product_id: 'corr_1',
            name: `${primaryBrand} Toner Ceniza 9.1`,
            amount: 50,
            unit: 'ml' as const,
            purpose: 'neutralization' as const,
          },
          {
            product_id: 'corr_2',
            name: `${primaryBrand} Mix Violeta`,
            amount: 10,
            unit: 'ml' as const,
            purpose: 'additive' as const,
          },
          {
            product_id: 'ox_10',
            name: 'Oxidante 10 volúmenes',
            amount: 60,
            unit: 'ml' as const,
            purpose: 'base' as const,
          },
        ],
        neutralizingTone: {
          color: '#9370DB',
          theory: 'El violeta neutraliza el naranja según el círculo cromático',
          level: 9,
        },
        processingTime: 20,
        technique: 'global' as const,
        precautions: [
          'Vigilar cada 5 minutos para evitar sobreneutralización',
          'No usar calor',
          'Aplicar tratamiento post-color',
        ],
        expectedResult: 'Neutralización del naranja, tono más frío y natural',
        sessionsNeeded: 1,
        maintenanceProducts: [
          'Shampoo matizador violeta 2x semana',
          'Mascarilla hidratante semanal',
          'Protector térmico diario',
        ],
      },
      green: {
        mainProducts: [
          {
            product_id: 'corr_3',
            name: `${primaryBrand} Pre-pigmento Cobre`,
            amount: 30,
            unit: 'ml' as const,
            purpose: 'neutralization' as const,
          },
          {
            product_id: 'corr_4',
            name: `${primaryBrand} 7.4 Rubio Cobrizo`,
            amount: 60,
            unit: 'ml' as const,
            purpose: 'base' as const,
          },
          {
            product_id: 'ox_20',
            name: 'Oxidante 20 volúmenes',
            amount: 90,
            unit: 'ml' as const,
            purpose: 'base' as const,
          },
        ],
        neutralizingTone: {
          color: '#B87333',
          theory: 'El cobre/rojo neutraliza el verde según teoría del color',
          level: 7,
        },
        processingTime: 30,
        technique: 'zones' as const,
        precautions: [
          'Aplicar pre-pigmento solo en zonas verdes',
          'Trabajar por secciones',
          'Test de mechón obligatorio',
        ],
        expectedResult: 'Eliminación del verde, tono cálido natural',
        sessionsNeeded: correctionData.bleachingDone ? 2 : 1,
        maintenanceProducts: ['Shampoo sin sulfatos', 'Aceite protector', 'Mascarilla nutritiva'],
      },
      uneven: {
        mainProducts: [
          {
            product_id: 'corr_5',
            name: `${primaryBrand} ${currentAnalysis.natural_level}.0 Natural`,
            amount: 40,
            unit: 'ml' as const,
            purpose: 'base' as const,
          },
          {
            product_id: 'corr_6',
            name: `${primaryBrand} ${currentAnalysis.natural_level + 1}.0 Natural`,
            amount: 20,
            unit: 'ml' as const,
            purpose: 'base' as const,
          },
          {
            product_id: 'ox_20',
            name: 'Oxidante 20 volúmenes',
            amount: 90,
            unit: 'ml' as const,
            purpose: 'base' as const,
          },
        ],
        neutralizingTone: {
          color: '#8B7355',
          theory: 'Igualación mediante mezcla de niveles',
          level: currentAnalysis.natural_level,
        },
        processingTime: 35,
        technique: 'selective' as const,
        precautions: [
          'Aplicar primero en zonas más claras',
          'Trabajar rápido para evitar manchas',
          'Peinar para difuminar',
        ],
        expectedResult: 'Color uniforme y parejo en todo el cabello',
        sessionsNeeded: 1,
        maintenanceProducts: ['Shampoo para color', 'Acondicionador sellador', 'Serum anti-frizz'],
      },
    };

    // Retornar formulación por defecto si no hay específica
    const defaultFormulation: CorrectionFormulation = {
      mainProducts: [
        {
          product_id: 'default_1',
          name: `${primaryBrand} Color Corrector`,
          amount: 60,
          unit: 'ml',
          purpose: 'base',
        },
        {
          product_id: 'ox_10',
          name: 'Oxidante 10 volúmenes',
          amount: 60,
          unit: 'ml',
          purpose: 'base',
        },
      ],
      neutralizingTone: {
        color: '#808080',
        theory: 'Corrección estándar',
        level: currentAnalysis.natural_level,
      },
      processingTime: 25,
      technique: 'global',
      precautions: ['Vigilar constantemente', 'No aplicar calor'],
      expectedResult: 'Mejora gradual del problema',
      sessionsNeeded: 2,
      maintenanceProducts: ['Productos de mantenimiento según resultado'],
    };

    return formulations[problemType as keyof typeof formulations] || defaultFormulation;
  }
}
