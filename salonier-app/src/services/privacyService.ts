import { manipulateAsync, SaveFormat } from 'expo-image-manipulator';

export interface ImageValidationResult {
  isValid: boolean;
  issues: string[];
}

export class PrivacyService {
  /**
   * Validate image quality for AI analysis
   */
  static async validateImageQuality(imageUri: string): Promise<ImageValidationResult> {
    const issues: string[] = [];
    
    try {
      // Get image info
      const imageInfo = await manipulateAsync(imageUri, []);
      const { width, height } = imageInfo;
      
      // Check minimum resolution
      if (width < 800 || height < 600) {
        issues.push('Resolución muy baja. Mínimo recomendado: 800x600');
      }
      
      // Check aspect ratio (not too wide or tall)
      const aspectRatio = width / height;
      if (aspectRatio > 3 || aspectRatio < 0.33) {
        issues.push('Proporción de imagen inusual');
      }
      
      // Additional quality checks could be added here
      // - Check if image is too dark
      // - Check if image is blurry
      // - etc.
      
      return {
        isValid: issues.length === 0,
        issues,
      };
    } catch (error) {
      console.error('Image validation error:', error);
      return {
        isValid: false,
        issues: ['Error al validar la imagen'],
      };
    }
  }

  /**
   * Optimize image for analysis (resize and compress)
   */
  static async optimizeForAnalysis(imageUri: string): Promise<string> {
    try {
      const optimized = await manipulateAsync(
        imageUri,
        [
          { resize: { width: 1024 } }, // Maintain aspect ratio
        ],
        {
          compress: 0.8,
          format: SaveFormat.JPEG,
        }
      );
      
      return optimized.uri;
    } catch (error) {
      console.error('Image optimization error:', error);
      return imageUri; // Return original if optimization fails
    }
  }

  /**
   * Create a privacy-safe crop of the image
   * Focuses on hair areas and avoids face regions
   */
  static async createPrivacyCrop(imageUri: string): Promise<string> {
    try {
      const imageInfo = await manipulateAsync(imageUri, []);
      const { width, height } = imageInfo;
      
      // Crop to focus on lower 75% of image (avoiding face area)
      const cropHeight = Math.floor(height * 0.75);
      const cropY = Math.floor(height * 0.25);
      
      const cropped = await manipulateAsync(
        imageUri,
        [
          { 
            crop: { 
              originX: 0, 
              originY: cropY, 
              width: width, 
              height: cropHeight 
            }
          }
        ],
        {
          compress: 0.8,
          format: SaveFormat.JPEG,
        }
      );
      
      return cropped.uri;
    } catch (error) {
      console.error('Privacy crop error:', error);
      return imageUri;
    }
  }

  /**
   * Get capture guidelines for privacy-safe photos
   */
  static getCaptureGuidelines() {
    return {
      dos: [
        'Enfoca el cabello desde la nuca o lateral',
        'Incluye raíces, medios y puntas',
        'Usa buena iluminación natural',
        'Mantén la cámara estable'
      ],
      donts: [
        'No incluyas el rostro',
        'Evita sombras fuertes',
        'No uses flash directo',
        'Evita fondos con patrones'
      ],
      zones: {
        safe: 'Zona del cabello (parte media y baja)',
        avoid: 'Zona del rostro (parte superior)'
      }
    };
  }
}