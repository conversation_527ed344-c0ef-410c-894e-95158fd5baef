import { User, UserBehaviorMetrics } from '../types';

export interface SmartSuggestion {
  id: string;
  type: 'discover_margins' | 'optimize_stock' | 'smart_pricing' | 'feature_highlight';
  title: string;
  description: string;
  benefit?: string;
  action: {
    label: string;
    type: 'change_level' | 'open_modal' | 'navigate' | 'tutorial';
    payload?: any;
  };
  priority: number;
  icon: string;
  color: string;
}

class SmartSuggestionService {
  private readonly SUGGESTION_COOLDOWN_DAYS = 7;
  private readonly MIN_CONSULTATIONS_FOR_MARGINS = 5;
  private readonly MIN_PRODUCT_USES_FOR_STOCK = 8;
  private readonly LOW_MARGIN_THRESHOLD = 50;

  getSuggestion(user: User): SmartSuggestion | null {
    if (!user.behavior_metrics) return null;

    // Check if enough time has passed since last suggestion
    if (this.isInCooldown(user.behavior_metrics)) return null;

    // Check dismissed suggestions
    const dismissed = user.behavior_metrics.dismissed_suggestions || [];

    // Priority 1: Discover margins after initial consultations
    if (this.shouldSuggestMargins(user, dismissed)) {
      return this.getMarginsSuggestion(user.behavior_metrics);
    }

    // Priority 2: Stock optimization for frequent products
    if (this.shouldSuggestStock(user, dismissed)) {
      return this.getStockSuggestion(user.behavior_metrics);
    }

    // Priority 3: Smart pricing for low margins
    if (this.shouldSuggestPricing(user, dismissed)) {
      return this.getPricingSuggestion(user.behavior_metrics);
    }

    // Priority 4: Feature highlights based on usage
    return this.getFeatureHighlight(user, dismissed);
  }

  private isInCooldown(metrics: UserBehaviorMetrics): boolean {
    if (!metrics.last_suggestion_date) return false;
    
    const daysSinceLastSuggestion = this.daysBetween(
      new Date(metrics.last_suggestion_date),
      new Date()
    );
    
    return daysSinceLastSuggestion < this.SUGGESTION_COOLDOWN_DAYS;
  }

  private shouldSuggestMargins(user: User, dismissed: string[]): boolean {
    if (dismissed.includes('discover_margins')) return false;
    if (user.inventory_level !== 'none') return false;
    
    const metrics = user.behavior_metrics!;
    return metrics.consultations_completed >= this.MIN_CONSULTATIONS_FOR_MARGINS &&
           metrics.feature_usage?.costs_viewed === 0;
  }

  private shouldSuggestStock(user: User, dismissed: string[]): boolean {
    if (dismissed.includes('optimize_stock')) return false;
    if (user.inventory_level === 'full_control') return false;
    
    const metrics = user.behavior_metrics!;
    const hasFrequentProducts = metrics.favorite_products?.some(
      p => p.count >= this.MIN_PRODUCT_USES_FOR_STOCK
    ) || false;
    
    return hasFrequentProducts && metrics.consultations_completed >= 10;
  }

  private shouldSuggestPricing(user: User, dismissed: string[]): boolean {
    if (dismissed.includes('smart_pricing')) return false;
    if (user.inventory_level === 'none') return false;
    
    const metrics = user.behavior_metrics!;
    return metrics.average_margin < this.LOW_MARGIN_THRESHOLD &&
           metrics.consultations_completed >= 3;
  }

  private getMarginsSuggestion(metrics: UserBehaviorMetrics): SmartSuggestion {
    const potentialExtraRevenue = this.calculatePotentialRevenue(metrics);
    
    return {
      id: 'discover_margins',
      type: 'discover_margins',
      title: '¡Felicidades por tus primeros servicios! 🎉',
      description: `Has completado ${metrics.consultations_completed} consultas exitosas. ¿Sabías que podrías estar ganando hasta ${potentialExtraRevenue}€ más al mes?`,
      benefit: 'Descubre tus márgenes reales en 30 segundos',
      action: {
        label: 'Ver mis márgenes',
        type: 'change_level',
        payload: { level: 'smart_cost' }
      },
      priority: 1,
      icon: 'calculator',
      color: '#4CAF50'
    };
  }

  private getStockSuggestion(metrics: UserBehaviorMetrics): SmartSuggestion {
    const topProduct = metrics.favorite_products[0];
    
    return {
      id: 'optimize_stock',
      type: 'optimize_stock',
      title: 'Optimiza tu inventario',
      description: `Usas ${topProduct.name} en el ${Math.round(topProduct.count / metrics.consultations_completed * 100)}% de tus servicios.`,
      benefit: 'Nunca te quedes sin stock y ahorra 10 min/semana',
      action: {
        label: 'Activar alertas',
        type: 'change_level',
        payload: { level: 'full_control' }
      },
      priority: 2,
      icon: 'package-variant',
      color: '#2196F3'
    };
  }

  private getPricingSuggestion(metrics: UserBehaviorMetrics): SmartSuggestion {
    const marginDiff = this.LOW_MARGIN_THRESHOLD - metrics.average_margin;
    
    return {
      id: 'smart_pricing',
      type: 'smart_pricing',
      title: 'Optimiza tus precios',
      description: `Tu margen promedio es ${metrics.average_margin.toFixed(0)}%. Podrías aumentarlo un ${marginDiff}% sin perder competitividad.`,
      benefit: 'Gana más sin trabajar más',
      action: {
        label: 'Ver sugerencias',
        type: 'open_modal',
        payload: { modal: 'pricing_optimizer' }
      },
      priority: 3,
      icon: 'currency-eur',
      color: '#FF9800'
    };
  }

  private getFeatureHighlight(user: User, dismissed: string[]): SmartSuggestion | null {
    const metrics = user.behavior_metrics!;
    
    // Suggest brand conversion if never used
    if (!dismissed.includes('try_conversion') && 
        metrics.feature_usage && 
        (metrics.feature_usage.brand_conversions === 0 || metrics.feature_usage.brand_conversions === undefined)) {
      return {
        id: 'try_conversion',
        type: 'feature_highlight',
        title: '¿Trabajas con varias marcas?',
        description: 'Convierte fórmulas entre marcas instantáneamente',
        action: {
          label: 'Probar ahora',
          type: 'navigate',
          payload: { screen: 'BrandConverter' }
        },
        priority: 4,
        icon: 'swap-horizontal',
        color: '#9C27B0'
      };
    }
    
    return null;
  }

  private calculatePotentialRevenue(metrics: UserBehaviorMetrics): number {
    // Estimate based on improving margin by 20%
    const monthlyServices = (metrics.consultations_completed / 30) * 30; // Rough monthly estimate
    const extraPerService = (metrics.average_service_price || 50) * 0.2;
    return Math.round(monthlyServices * extraPerService);
  }

  private daysBetween(date1: Date, date2: Date): number {
    const oneDay = 24 * 60 * 60 * 1000;
    return Math.round(Math.abs((date2.getTime() - date1.getTime()) / oneDay));
  }

  dismissSuggestion(user: User, suggestionId: string): User {
    if (!user.behavior_metrics) return user;
    
    return {
      ...user,
      behavior_metrics: {
        ...user.behavior_metrics,
        dismissed_suggestions: [...(user.behavior_metrics.dismissed_suggestions || []), suggestionId],
        last_suggestion_date: new Date().toISOString()
      }
    };
  }

  recordSuggestionShown(user: User): User {
    if (!user.behavior_metrics) return user;
    
    return {
      ...user,
      behavior_metrics: {
        ...user.behavior_metrics,
        last_suggestion_date: new Date().toISOString()
      }
    };
  }
}

export const smartSuggestionService = new SmartSuggestionService();