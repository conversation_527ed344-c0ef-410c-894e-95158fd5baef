import Toast from 'react-native-toast-message';

export interface ValidationResult {
  isValid: boolean;
  message?: string;
  hasFace?: boolean;
}

export class FaceValidation {
  /**
   * Valida que la imagen no contenga rostros
   * Por ahora usa validación simulada, en producción usaría ML
   */
  static async validateNoFaces(_imageUri: string): Promise<ValidationResult> {
    try {
      // En desarrollo/Expo Go: simulamos la validación
      // En producción: aquí se integraría con un modelo ML local

      // Simular proceso de validación
      await new Promise(resolve => setTimeout(resolve, 500));

      // Por ahora siempre retornamos válido para desarrollo
      // En producción esto detectaría rostros realmente
      const mockHasFace = false; // Desactivado para desarrollo

      if (mockHasFace) {
        return {
          isValid: false,
          hasFace: true,
          message: 'Por favor, captura solo el cabello desde la parte posterior o lateral.',
        };
      }

      return {
        isValid: true,
        hasFace: false,
        message: 'Imagen válida - no se detectaron rostros',
      };
    } catch (error) {
      console.error('Error validating faces:', error);
      return {
        isValid: true, // En caso de error, permitir continuar
        message: 'No se pudo validar la imagen',
      };
    }
  }

  /**
   * Muestra instrucciones al usuario sobre cómo tomar fotos
   */
  static showCaptureInstructions() {
    Toast.show({
      type: 'info',
      text1: '📸 Consejos de captura',
      text2:
        '• Incluye raíces, medios y puntas\n• Usa luz natural para mejor precisión\n• Captura desde la parte posterior o lateral',
      visibilityTime: 5000,
    });
  }

  /**
   * Valida que la imagen tenga buena calidad para análisis
   */
  static async validateImageQuality(_imageUri: string): Promise<ValidationResult> {
    try {
      // Aquí podrías validar:
      // - Resolución mínima
      // - Buena iluminación
      // - Enfoque adecuado

      // Por ahora validación simple
      return {
        isValid: true,
        message: 'Calidad de imagen adecuada',
      };
    } catch (error) {
      return {
        isValid: false,
        message: 'Error al validar calidad de imagen',
      };
    }
  }

  /**
   * Guía visual para mostrar zonas seguras de captura
   */
  static getSafeZoneGuides() {
    return {
      topMargin: 0.25, // Evitar el 25% superior (donde suele estar la cara)
      bottomMargin: 0.05, // 5% margen inferior
      message: 'Mantén el cabello dentro de la zona verde',
      zones: [
        { name: 'safe', color: 'rgba(0, 255, 0, 0.2)' },
        { name: 'avoid', color: 'rgba(255, 0, 0, 0.2)' },
      ],
    };
  }
}
