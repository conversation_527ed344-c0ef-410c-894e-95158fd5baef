import { useState, useCallback } from 'react';

interface UseErrorHandlerOptions {
  maxRetries?: number;
  retryDelay?: number;
  onError?: (error: any) => void;
  onRetryExhausted?: () => void;
}

interface UseErrorHandlerReturn<T> {
  data: T | null;
  error: any;
  loading: boolean;
  retry: () => void;
  execute: (asyncFunction: () => Promise<T>) => Promise<void>;
  clearError: () => void;
}

export function useErrorHandler<T = any>(
  options: UseErrorHandlerOptions = {}
): UseErrorHandlerReturn<T> {
  const {
    maxRetries = 3,
    retryDelay = 1000,
    onError,
    onRetryExhausted,
  } = options;

  const [data, setData] = useState<T | null>(null);
  const [error, setError] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [retryCount, setRetryCount] = useState(0);

  const execute = useCallback(async (asyncFunction: () => Promise<T>) => {
    setLoading(true);
    setError(null);

    try {
      const result = await asyncFunction();
      setData(result);
      setRetryCount(0);
    } catch (err) {
      console.error('Error in useErrorHandler:', err);
      setError(err);
      
      if (onError) {
        onError(err);
      }

      if (retryCount < maxRetries) {
        setTimeout(() => {
          setRetryCount(prev => prev + 1);
          execute(asyncFunction);
        }, retryDelay * (retryCount + 1));
      } else if (onRetryExhausted) {
        onRetryExhausted();
      }
    } finally {
      setLoading(false);
    }
  }, [retryCount, maxRetries, retryDelay, onError, onRetryExhausted]);

  const retry = useCallback(() => {
    if (error) {
      setRetryCount(0);
      // Re-ejecutar la última función
    }
  }, [error]);

  const clearError = useCallback(() => {
    setError(null);
    setRetryCount(0);
  }, []);

  return {
    data,
    error,
    loading,
    retry,
    execute,
    clearError,
  };
}

// Hook específico para operaciones de red
export function useNetworkRequest<T = any>(
  // requestFunction: () => Promise<T>,
  options: UseErrorHandlerOptions = {}
) {
  const errorHandler = useErrorHandler<T>({
    ...options,
    onError: (error) => {
      // Manejo específico de errores de red
      if (error.message?.includes('Network')) {
        console.error('Error de red detectado:', error);
      }
      options.onError?.(error);
    },
  });

  return errorHandler;
}