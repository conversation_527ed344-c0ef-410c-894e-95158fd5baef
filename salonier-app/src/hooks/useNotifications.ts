import { useEffect, useState, useCallback } from 'react';
import Toast from 'react-native-toast-message';
import { NotificationService } from '../services/notificationService';
import { dataService } from '../services/dataService';
import { notificationTemplates } from '../utils/notificationTemplates';
import { Appointment, Client, Product } from '../types/improved-types';
import * as Notifications from 'expo-notifications';

export const useNotifications = () => {
  const [hasPermission, setHasPermission] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [pushToken, setPushToken] = useState<string | null>(null);

  useEffect(() => {
    initializeNotifications();
    return () => {
      NotificationService.cleanup();
    };
  }, []);

  const initializeNotifications = async () => {
    try {
      setIsLoading(true);
      await NotificationService.initialize();
      const token = await NotificationService.registerForPushNotifications();

      if (token) {
        setPushToken(token);
        setHasPermission(true);
      } else {
        setHasPermission(false);
      }
    } catch (error) {
      console.error('Error initializing notifications:', error);
      setHasPermission(false);
    } finally {
      setIsLoading(false);
    }
  };

  const requestPermission = useCallback(async () => {
    try {
      const token = await NotificationService.registerForPushNotifications();
      if (token) {
        setPushToken(token);
        setHasPermission(true);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error requesting notification permission:', error);
      return false;
    }
  }, []);

  const scheduleAppointmentReminder = useCallback(
    async (appointment: Appointment, reminderMinutes: number = 30) => {
      if (!hasPermission) {
        const granted = await requestPermission();
        if (!granted) {
          Toast.show({
            type: 'warning',
            text1: 'Permisos necesarios',
            text2: 'Necesitas activar las notificaciones para recibir recordatorios de citas',
          });
          return null;
        }
      }

      try {
        const client = await dataService.clients.getById(
          appointment.client_id || appointment.clientId
        );
        if (!client) return null;

        return await NotificationService.scheduleAppointmentReminder(
          appointment,
          reminderMinutes,
          client.full_name || client.fullName
        );
      } catch (error) {
        console.error('Error scheduling appointment reminder:', error);
        return null;
      }
    },
    [hasPermission, requestPermission]
  );

  const scheduleRetouchReminder = useCallback(
    async (client: Client, lastServiceDate: Date, weekDelay: number = 6) => {
      if (!hasPermission) return null;

      try {
        return await NotificationService.scheduleRetouchReminder(
          client.id,
          client.full_name,
          lastServiceDate,
          weekDelay
        );
      } catch (error) {
        console.error('Error scheduling retouch reminder:', error);
        return null;
      }
    },
    [hasPermission]
  );

  const sendLowStockAlert = useCallback(
    async (product: Product) => {
      if (!hasPermission) return;

      try {
        await NotificationService.sendLowStockAlert(product);
      } catch (error) {
        console.error('Error sending low stock alert:', error);
      }
    },
    [hasPermission]
  );

  const sendDailySummary = useCallback(
    async (appointmentCount: number, revenue: number) => {
      if (!hasPermission) return;

      try {
        await NotificationService.sendDailySummary(appointmentCount, revenue);
      } catch (error) {
        console.error('Error sending daily summary:', error);
      }
    },
    [hasPermission]
  );

  const sendWhatsAppNotification = useCallback(
    async (clientName: string) => {
      if (!hasPermission) return;

      try {
        const template = notificationTemplates.whatsappSent(clientName);
        await Notifications.scheduleNotificationAsync({
          content: {
            title: template.title,
            body: template.body,
            data: { type: 'business' },
            sound: true,
          },
          trigger: null,
        });
      } catch (error) {
        console.error('Error sending WhatsApp notification:', error);
      }
    },
    [hasPermission]
  );

  const checkAndScheduleNotifications = useCallback(async () => {
    if (!hasPermission) return;

    try {
      await NotificationService.checkAndScheduleNotifications();
    } catch (error) {
      console.error('Error checking notifications:', error);
    }
  }, [hasPermission]);

  const cancelNotification = useCallback(async (identifier: string) => {
    try {
      await NotificationService.cancelNotification(identifier);
    } catch (error) {
      console.error('Error canceling notification:', error);
    }
  }, []);

  const cancelAllNotifications = useCallback(async () => {
    try {
      await NotificationService.cancelAllNotifications();
    } catch (error) {
      console.error('Error canceling all notifications:', error);
    }
  }, []);

  const getScheduledNotifications = useCallback(async () => {
    try {
      return await NotificationService.getScheduledNotifications();
    } catch (error) {
      console.error('Error getting scheduled notifications:', error);
      return [];
    }
  }, []);

  return {
    hasPermission,
    isLoading,
    pushToken,
    requestPermission,
    scheduleAppointmentReminder,
    scheduleRetouchReminder,
    sendLowStockAlert,
    sendDailySummary,
    sendWhatsAppNotification,
    checkAndScheduleNotifications,
    cancelNotification,
    cancelAllNotifications,
    getScheduledNotifications,
  };
};
