import { useState, useCallback } from 'react';
import * as ImagePicker from 'expo-image-picker';
import { useCameraPermissions } from 'expo-camera';
import Toast from 'react-native-toast-message';
import { PrivacyService } from '../services/privacyService';
import { PatchExtractor, ColorPatch } from '../services/patchExtractor';
import { FaceValidation } from '../services/faceValidation';
import { ColorAnalysis, HairColorProfile } from '../services/colorAnalysis';

export interface PhotoAnalysisData {
  photos: string[];
  mode: 'photo' | 'manual';
  customizeByZones: boolean;
  zoneAnalysis?: {
    roots: ZoneData;
    mids: ZoneData;
    ends: ZoneData;
  };
  patches?: ColorPatch[];
  colorProfile?: HairColorProfile;
  metadata?: {
    capturedAt: string;
    source: 'camera' | 'gallery';
    patchCount: number;
  };
}

export interface ZoneData {
  level: number;
  porosity: 'low' | 'medium' | 'high';
  condition: 'damaged' | 'healthy' | 'very_healthy';
  grayPercentage?: number;
  tone?: string;
}

export function usePhotoAnalysis(initialData?: Partial<PhotoAnalysisData>) {
  const [photos, setPhotos] = useState<string[]>(initialData?.photos || []);
  const [mode, setMode] = useState<'photo' | 'manual'>(initialData?.mode || 'manual');
  const [customizeByZones, setCustomizeByZones] = useState(initialData?.customizeByZones || false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [patches, setPatches] = useState<ColorPatch[]>(initialData?.patches || []);
  const [colorProfile, setColorProfile] = useState<HairColorProfile | undefined>(
    initialData?.colorProfile
  );
  const [zoneAnalysis, setZoneAnalysis] = useState(
    initialData?.zoneAnalysis || {
      roots: { level: 6, porosity: 'medium', condition: 'healthy', grayPercentage: 0 },
      mids: { level: 7, porosity: 'medium', condition: 'healthy', grayPercentage: 0 },
      ends: { level: 8, porosity: 'medium', condition: 'healthy', grayPercentage: 0 },
    }
  );

  const [cameraPermission, requestCameraPermission] = useCameraPermissions();

  const handleCameraPermission = useCallback(async () => {
    if (!cameraPermission) {
      // Permission is still loading
      return false;
    }

    if (!cameraPermission.granted) {
      const result = await requestCameraPermission();
      if (!result.granted) {
        Toast.show({
          type: 'warning',
          text1: 'Permiso requerido',
          text2: 'Necesitamos acceso a la cámara para tomar fotos del cabello.',
          position: 'top',
        });
        return false;
      }
    }
    return true;
  }, [cameraPermission, requestCameraPermission]);

  const requestGalleryPermission = useCallback(async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Toast.show({
        type: 'warning',
        text1: 'Permiso requerido',
        text2: 'Necesitamos acceso a tu galería para seleccionar fotos.',
        position: 'top',
      });
      return false;
    }
    return true;
  }, []);

  const processPhoto = useCallback(async (photoUri: string) => {
    setIsProcessing(true);
    try {
      // 1. Validate image quality
      const validation = await PrivacyService.validateImageQuality(photoUri);
      if (!validation.isValid && validation.issues.length > 0) {
        Toast.show({
          type: 'warning',
          text1: 'Calidad de imagen',
          text2: `Problemas detectados: ${validation.issues.join(', ')}`,
          position: 'top',
          visibilityTime: 4000,
        });
        // Continue anyway for better UX
      }

      // 2. Validate no faces (simulated for now)
      const faceValidation = await FaceValidation.validateNoFaces(photoUri);
      if (!faceValidation.isValid && faceValidation.hasFace) {
        Toast.show({
          type: 'warning',
          text1: 'Ajusta la captura',
          text2:
            faceValidation.message ||
            'Por favor, captura solo el cabello desde la parte posterior o lateral.',
          position: 'top',
        });
        throw new Error('Face detected');
      }

      // 3. Optimize image
      const optimizedUri = await PrivacyService.optimizeForAnalysis(photoUri);

      // 4. Extract color patches
      const extractionResult = await PatchExtractor.extractHairPatches(optimizedUri);
      setPatches(extractionResult.patches);

      // 5. Analyze patches
      const profile = await ColorAnalysis.analyzeMultiplePatches(extractionResult.patches);
      setColorProfile(profile);

      // 6. Update zone analysis based on color profile
      if (profile) {
        setCustomizeByZones(true);
        setZoneAnalysis({
          roots: {
            level: profile.roots.level,
            porosity: 'medium',
            condition: 'healthy',
            grayPercentage: profile.roots.reflectPercentage,
            tone: profile.roots.undertone,
          },
          mids: {
            level: profile.mid.level,
            porosity: 'medium',
            condition: profile.overall.condition === 'damaged' ? 'damaged' : 'healthy',
            grayPercentage: profile.mid.reflectPercentage,
            tone: profile.mid.undertone,
          },
          ends: {
            level: profile.ends.level,
            porosity: profile.overall.condition === 'damaged' ? 'high' : 'medium',
            condition: profile.overall.condition === 'damaged' ? 'damaged' : 'healthy',
            grayPercentage: profile.ends.reflectPercentage,
            tone: profile.ends.undertone,
          },
        });
      }

      return {
        uri: optimizedUri,
        patches: extractionResult.patches,
        colorProfile: profile,
        metadata: {
          capturedAt: new Date().toISOString(),
          source: 'camera' as const,
          patchCount: extractionResult.patches.length,
        },
      };
    } catch (error) {
      console.error('Photo processing error:', error);
      throw error;
    } finally {
      setIsProcessing(false);
    }
  }, []);

  const capturePhoto = useCallback(async () => {
    const hasPermission = await handleCameraPermission();
    if (!hasPermission) return null;

    // Show capture instructions first
    FaceValidation.showCaptureInstructions();

    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        return result.assets[0].uri;
      }
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'No se pudo capturar la foto',
        position: 'top',
      });
      console.error('Camera error:', error);
    }
    return null;
  }, [handleCameraPermission]);

  const pickFromGallery = useCallback(
    async (allowMultiple: boolean = false) => {
      const hasPermission = await requestGalleryPermission();
      if (!hasPermission) return [];

      try {
        const result = await ImagePicker.launchImageLibraryAsync({
          mediaTypes: ImagePicker.MediaTypeOptions.Images,
          allowsMultipleSelection: allowMultiple,
          allowsEditing: !allowMultiple,
          aspect: [4, 3],
          quality: 0.8,
          selectionLimit: 1, // For patch extraction, we only need one photo
        });

        if (!result.canceled && result.assets.length > 0) {
          return result.assets.map(asset => asset.uri);
        }
      } catch (error) {
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: 'No se pudo seleccionar las fotos',
          position: 'top',
        });
        console.error('Gallery error:', error);
      }
      return [];
    },
    [requestGalleryPermission]
  );

  const addPhoto = useCallback(
    async (uri: string) => {
      const processed = await processPhoto(uri);
      setPhotos(prev => [...prev, processed.uri]);
    },
    [processPhoto]
  );

  const removePhoto = useCallback(
    (index: number) => {
      setPhotos(prev => prev.filter((_, i) => i !== index));
      // Also clear patches if removing the analyzed photo
      if (photos.length === 1) {
        setPatches([]);
        setColorProfile(undefined);
        // Return to photo mode when removing the last photo
        setMode('photo');
      }
    },
    [photos.length]
  );

  const clearPhotos = useCallback(() => {
    setPhotos([]);
    setPatches([]);
    setColorProfile(undefined);
  }, []);

  const getAnalysisData = useCallback((): PhotoAnalysisData => {
    return {
      photos,
      mode,
      customizeByZones,
      zoneAnalysis: customizeByZones ? (zoneAnalysis as any) : undefined,
      patches,
      colorProfile,
      metadata:
        photos.length > 0
          ? {
              capturedAt: new Date().toISOString(),
              source: 'camera',
              patchCount: patches.length,
            }
          : undefined,
    };
  }, [photos, mode, customizeByZones, zoneAnalysis, patches, colorProfile]);

  return {
    // State
    photos,
    mode,
    customizeByZones,
    zoneAnalysis,
    isProcessing,
    patches,
    colorProfile,

    // Actions
    setMode,
    setCustomizeByZones,
    setZoneAnalysis,
    capturePhoto,
    pickFromGallery,
    addPhoto,
    removePhoto,
    clearPhotos,
    getAnalysisData,
  };
}
