import { useState, useCallback } from 'react';

export interface ValidationRule {
  required?: boolean | { value: boolean; message: string };
  minLength?: number | { value: number; message: string };
  maxLength?: number | { value: number; message: string };
  pattern?: RegExp | { value: RegExp; message: string };
  validate?: (value: any) => boolean | string;
}

export interface FormErrors {
  [key: string]: string | undefined;
}

export interface FormValues {
  [key: string]: any;
}

interface UseFormValidationReturn {
  values: FormValues;
  errors: FormErrors;
  touched: { [key: string]: boolean };
  isValid: boolean;
  setValue: (name: string, value: any) => void;
  setValues: (values: FormValues) => void;
  setError: (name: string, error: string) => void;
  clearError: (name: string) => void;
  clearErrors: () => void;
  validate: () => boolean;
  validateField: (name: string) => boolean;
  handleChange: (name: string) => (value: string) => void;
  handleBlur: (name: string) => () => void;
  reset: () => void;
}

export function useFormValidation(
  initialValues: FormValues = {},
  validationRules: { [key: string]: ValidationRule } = {}
): UseFormValidationReturn {
  const [values, setValuesState] = useState<FormValues>(initialValues);
  const [errors, setErrors] = useState<FormErrors>({});
  const [touched, setTouched] = useState<{ [key: string]: boolean }>({});

  const validateField = useCallback((name: string): boolean => {
    const value = values[name];
    const rules = validationRules[name];

    if (!rules) return true;

    // Required validation
    if (rules.required) {
      const isEmpty = value === undefined || value === null || value === '' || 
        (Array.isArray(value) && value.length === 0);
      
      if (isEmpty) {
        const message = typeof rules.required === 'object' 
          ? rules.required.message 
          : 'Este campo es requerido';
        setErrors(prev => ({ ...prev, [name]: message }));
        return false;
      }
    }

    // MinLength validation
    if (rules.minLength && value && typeof value === 'string') {
      const minLength = typeof rules.minLength === 'object' 
        ? rules.minLength.value 
        : rules.minLength;
      const message = typeof rules.minLength === 'object' 
        ? rules.minLength.message 
        : `Mínimo ${minLength} caracteres`;

      if (value.length < minLength) {
        setErrors(prev => ({ ...prev, [name]: message }));
        return false;
      }
    }

    // MaxLength validation
    if (rules.maxLength && value && typeof value === 'string') {
      const maxLength = typeof rules.maxLength === 'object' 
        ? rules.maxLength.value 
        : rules.maxLength;
      const message = typeof rules.maxLength === 'object' 
        ? rules.maxLength.message 
        : `Máximo ${maxLength} caracteres`;

      if (value.length > maxLength) {
        setErrors(prev => ({ ...prev, [name]: message }));
        return false;
      }
    }

    // Pattern validation
    if (rules.pattern && value && typeof value === 'string') {
      const pattern = typeof rules.pattern === 'object' && 'value' in rules.pattern
        ? rules.pattern.value 
        : rules.pattern;
      const message = typeof rules.pattern === 'object' && 'message' in rules.pattern
        ? rules.pattern.message 
        : 'Formato inválido';

      if (!pattern.test(value)) {
        setErrors(prev => ({ ...prev, [name]: message }));
        return false;
      }
    }

    // Custom validation
    if (rules.validate && value !== undefined) {
      const result = rules.validate(value);
      if (typeof result === 'string') {
        setErrors(prev => ({ ...prev, [name]: result }));
        return false;
      } else if (!result) {
        setErrors(prev => ({ ...prev, [name]: 'Valor inválido' }));
        return false;
      }
    }

    // Clear error if validation passes
    setErrors(prev => ({ ...prev, [name]: undefined }));
    return true;
  }, [values, validationRules]);

  const validate = useCallback((): boolean => {
    let isValid = true;
    // const newErrors: FormErrors = {};

    Object.keys(validationRules).forEach(name => {
      if (!validateField(name)) {
        isValid = false;
      }
    });

    return isValid;
  }, [validationRules, validateField]);

  const setValue = useCallback((name: string, value: any) => {
    setValuesState(prev => ({ ...prev, [name]: value }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: undefined }));
    }
  }, [errors]);

  const setValues = useCallback((newValues: FormValues) => {
    setValuesState(newValues);
  }, []);

  const setError = useCallback((name: string, error: string) => {
    setErrors(prev => ({ ...prev, [name]: error }));
  }, []);

  const clearError = useCallback((name: string) => {
    setErrors(prev => ({ ...prev, [name]: undefined }));
  }, []);

  const clearErrors = useCallback(() => {
    setErrors({});
  }, []);

  const handleChange = useCallback((name: string) => (value: string) => {
    setValue(name, value);
  }, [setValue]);

  const handleBlur = useCallback((name: string) => () => {
    setTouched(prev => ({ ...prev, [name]: true }));
    validateField(name);
  }, [validateField]);

  const reset = useCallback(() => {
    setValuesState(initialValues);
    setErrors({});
    setTouched({});
  }, [initialValues]);

  const isValid = Object.keys(errors).every(key => !errors[key]) && 
    Object.keys(validationRules).every(key => values[key] !== undefined || !validationRules[key].required);

  return {
    values,
    errors,
    touched,
    isValid,
    setValue,
    setValues,
    setError,
    clearError,
    clearErrors,
    validate,
    validateField,
    handleChange,
    handleBlur,
    reset,
  };
}

// Validaciones comunes predefinidas
export const validationPatterns = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  phone: /^[\d\s\-\+\(\)]+$/,
  alphanumeric: /^[a-zA-Z0-9]+$/,
  numeric: /^\d+$/,
  decimal: /^\d+(\.\d{1,2})?$/,
};

export const commonValidations = {
  email: {
    required: { value: true, message: 'El email es requerido' },
    pattern: { value: validationPatterns.email, message: 'Email inválido' },
  },
  phone: {
    pattern: { value: validationPatterns.phone, message: 'Teléfono inválido' },
    minLength: { value: 9, message: 'Mínimo 9 dígitos' },
  },
  name: {
    required: { value: true, message: 'El nombre es requerido' },
    minLength: { value: 2, message: 'Mínimo 2 caracteres' },
    maxLength: { value: 50, message: 'Máximo 50 caracteres' },
  },
  password: {
    required: { value: true, message: 'La contraseña es requerida' },
    minLength: { value: 8, message: 'Mínimo 8 caracteres' },
    validate: (value: string) => {
      if (!/[A-Z]/.test(value)) return 'Debe contener al menos una mayúscula';
      if (!/[a-z]/.test(value)) return 'Debe contener al menos una minúscula';
      if (!/[0-9]/.test(value)) return 'Debe contener al menos un número';
      return true;
    },
  },
};