import React, { createContext, useContext, useState, ReactNode } from 'react';
import { Client, HairAnalysis, ColorFormulation } from '../types';
import { dataService } from '../services/dataService';
import Toast from 'react-native-toast-message';

// Estados del flujo simplificado
export enum SimpleConsultationStep {
  CLIENT = 'CLIENT',           // Cliente + Safety integrado
  ANALYSIS = 'ANALYSIS',       // Análisis actual
  OBJECTIVE = 'OBJECTIVE',     // Color objetivo
  FORMULA = 'FORMULA',         // Fórmula inteligente (técnica + formulación)
  APPLY = 'APPLY',            // Aplicar y guardar
}

// Estado de la consulta simplificada
export interface SimpleConsultationState {
  // Información básica
  currentStep: SimpleConsultationStep;
  startTime: Date;
  consultationId?: string;
  
  // Datos del cliente
  client: Client | null;
  
  // Análisis del cabello
  hairAnalysis: HairAnalysis | null;
  
  // Color objetivo
  desiredColor?: any;
  technique: 'global' | 'roots' | 'highlights' | 'balayage' | 'correction';
  
  // Formulación
  formulation: ColorFormulation | null;
  
  // Seguridad (integrado en paso 1)
  safetyChecks: {
    allergyTest: boolean;
    consentSigned: boolean;
  };
  
  // Modo corrección
  isCorrection: boolean;
  correctionProblem?: string;
  correctionTimeframe?: string;
}

// Contexto
interface SimpleConsultationContextType {
  state: SimpleConsultationState;
  
  // Navegación
  goToStep: (step: SimpleConsultationStep) => void;
  nextStep: () => void;
  previousStep: () => void;
  
  // Actualizaciones de estado
  setClient: (client: Client) => void;
  setSafetyChecks: (checks: SimpleConsultationState['safetyChecks']) => void;
  setHairAnalysis: (analysis: HairAnalysis) => void;
  setDesiredColor: (desiredColor: any) => void;
  setFormulation: (formulation: ColorFormulation) => void;
  setCorrection: (isCorrection: boolean, problem?: string, timeframe?: string) => void;
  
  // Utilidades
  resetConsultation: () => void;
  saveConsultation: () => Promise<void>;
  canProceed: () => boolean;
  getProgress: () => number;
}

const SimpleConsultationContext = createContext<SimpleConsultationContextType | undefined>(undefined);

// Orden de los pasos
const STEP_ORDER = [
  SimpleConsultationStep.CLIENT,
  SimpleConsultationStep.ANALYSIS,
  SimpleConsultationStep.OBJECTIVE,
  SimpleConsultationStep.FORMULA,
  SimpleConsultationStep.APPLY,
];

// Estado inicial
const initialState: SimpleConsultationState = {
  currentStep: SimpleConsultationStep.CLIENT,
  startTime: new Date(),
  client: null,
  hairAnalysis: null,
  formulation: null,
  safetyChecks: {
    allergyTest: false,
    consentSigned: false,
  },
  technique: 'global',
  isCorrection: false,
};

export function SimpleConsultationProvider({ children }: { children: ReactNode }) {
  const [state, setState] = useState<SimpleConsultationState>(initialState);

  const goToStep = (step: SimpleConsultationStep) => {
    setState(prev => ({ ...prev, currentStep: step }));
  };

  const nextStep = () => {
    const currentIndex = STEP_ORDER.indexOf(state.currentStep);
    if (currentIndex < STEP_ORDER.length - 1) {
      goToStep(STEP_ORDER[currentIndex + 1]);
    }
  };

  const previousStep = () => {
    const currentIndex = STEP_ORDER.indexOf(state.currentStep);
    if (currentIndex > 0) {
      goToStep(STEP_ORDER[currentIndex - 1]);
    }
  };

  const setClient = (client: Client) => {
    setState(prev => ({ ...prev, client }));
  };

  const setSafetyChecks = (checks: SimpleConsultationState['safetyChecks']) => {
    setState(prev => ({ ...prev, safetyChecks: checks }));
  };

  const setHairAnalysis = (analysis: HairAnalysis) => {
    setState(prev => ({ ...prev, hairAnalysis: analysis }));
  };

  const setDesiredColor = (desiredColor: any) => {
    setState(prev => ({ ...prev, desiredColor }));
  };

  const setFormulation = (formulation: ColorFormulation) => {
    setState(prev => ({ ...prev, formulation }));
  };

  const setCorrection = (isCorrection: boolean, problem?: string, timeframe?: string) => {
    setState(prev => ({ 
      ...prev, 
      isCorrection,
      correctionProblem: problem,
      correctionTimeframe: timeframe,
      technique: isCorrection ? 'correction' : 'global'
    }));
  };

  const resetConsultation = () => {
    setState({
      ...initialState,
      startTime: new Date(),
    });
  };

  const saveConsultation = async () => {
    if (!state.client || !state.hairAnalysis || !state.formulation) {
      throw new Error('Faltan datos requeridos para guardar la consulta');
    }

    try {
      const consultation = {
        id: Date.now().toString(),
        client_id: state.client.id,
        created_at: state.startTime.toISOString(),
        current_hair_analysis: state.hairAnalysis,
        desired_color: state.desiredColor,
        technique: state.technique,
        formulation: state.formulation,
        status: 'completed' as const,
        notes: '',
        is_correction: state.isCorrection,
        correction_problem: state.correctionProblem,
        correction_timeframe: state.correctionTimeframe,
      };

      await dataService.consultations.create(consultation);
      
      Toast.show({
        type: 'success',
        text1: '✅ Consulta Guardada',
        text2: 'La consulta se guardó exitosamente',
        position: 'top',
      });
    } catch (error) {
      console.error('Error saving consultation:', error);
      Toast.show({
        type: 'error',
        text1: 'Error al guardar',
        text2: 'No se pudo guardar la consulta',
        position: 'top',
      });
      throw error;
    }
  };

  const canProceed = (): boolean => {
    switch (state.currentStep) {
      case SimpleConsultationStep.CLIENT:
        return !!state.client && state.safetyChecks.allergyTest && state.safetyChecks.consentSigned;
      case SimpleConsultationStep.ANALYSIS:
        return !!state.hairAnalysis;
      case SimpleConsultationStep.OBJECTIVE:
        return !!state.desiredColor;
      case SimpleConsultationStep.FORMULA:
        return !!state.formulation;
      case SimpleConsultationStep.APPLY:
        return true;
      default:
        return false;
    }
  };

  const getProgress = (): number => {
    const currentIndex = STEP_ORDER.indexOf(state.currentStep);
    return ((currentIndex + 1) / STEP_ORDER.length) * 100;
  };

  const value: SimpleConsultationContextType = {
    state,
    goToStep,
    nextStep,
    previousStep,
    setClient,
    setSafetyChecks,
    setHairAnalysis,
    setDesiredColor,
    setFormulation,
    setCorrection,
    resetConsultation,
    saveConsultation,
    canProceed,
    getProgress,
  };

  return (
    <SimpleConsultationContext.Provider value={value}>
      {children}
    </SimpleConsultationContext.Provider>
  );
}

export function useSimpleConsultation() {
  const context = useContext(SimpleConsultationContext);
  if (!context) {
    throw new Error('useSimpleConsultation must be used within SimpleConsultationProvider');
  }
  return context;
}