import { useEffect, useCallback } from 'react';
import { User, UserBehaviorMetrics } from '../types';
// import { dataService } from '../services/dataService';

interface BehaviorTrackingProps {
  user: User | null;
  onUserUpdate?: (user: User) => void;
}

export const useBehaviorTracking = ({ user, onUserUpdate }: BehaviorTrackingProps) => {
  // Initialize behavior metrics if not present
  useEffect(() => {
    if (user && !user.behavior_metrics) {
      const initialMetrics: UserBehaviorMetrics = {
        consultations_completed: 0,
        favorite_products: [],
        average_service_price: 0,
        average_margin: 0,
        dismissed_suggestions: [],
        feature_usage: {
          inventory_checked: 0,
          costs_viewed: 0,
          reports_generated: 0,
          brand_conversions: 0,
        },
        last_active_date: new Date().toISOString(),
      };

      const updatedUser = {
        ...user,
        behavior_metrics: initialMetrics,
      };

      onUserUpdate?.(updatedUser);
    }
  }, [user, onUserUpdate]);

  const trackConsultationCompleted = useCallback(async (servicePrice: number, margin: number, productsUsed: string[]) => {
    if (!user?.behavior_metrics) return;

    const metrics = user.behavior_metrics;
    const newConsultationCount = metrics.consultations_completed + 1;

    // Update average price
    const newAveragePrice = (
      (metrics.average_service_price * metrics.consultations_completed + servicePrice) / 
      newConsultationCount
    );

    // Update average margin
    const newAverageMargin = (
      (metrics.average_margin * metrics.consultations_completed + margin) / 
      newConsultationCount
    );

    // Update favorite products
    const updatedProducts = [...metrics.favorite_products];
    productsUsed.forEach(productName => {
      const existingIndex = updatedProducts.findIndex(p => p.name === productName);
      if (existingIndex >= 0) {
        updatedProducts[existingIndex].count++;
      } else {
        updatedProducts.push({ name: productName, count: 1 });
      }
    });

    // Sort by count descending
    updatedProducts.sort((a, b) => b.count - a.count);

    const updatedMetrics: UserBehaviorMetrics = {
      ...metrics,
      consultations_completed: newConsultationCount,
      average_service_price: newAveragePrice,
      average_margin: newAverageMargin,
      favorite_products: updatedProducts.slice(0, 10), // Keep top 10
      last_active_date: new Date().toISOString(),
      first_consultation_date: metrics.first_consultation_date || new Date().toISOString(),
    };

    const updatedUser = {
      ...user,
      behavior_metrics: updatedMetrics,
    };

    onUserUpdate?.(updatedUser);
    // TODO: Save to backend
  }, [user, onUserUpdate]);

  const trackFeatureUsage = useCallback(async (feature: keyof UserBehaviorMetrics['feature_usage']) => {
    if (!user?.behavior_metrics) return;

    const updatedMetrics: UserBehaviorMetrics = {
      ...user.behavior_metrics,
      feature_usage: {
        ...user.behavior_metrics.feature_usage,
        [feature]: user.behavior_metrics.feature_usage[feature] + 1,
      },
      last_active_date: new Date().toISOString(),
    };

    const updatedUser = {
      ...user,
      behavior_metrics: updatedMetrics,
    };

    onUserUpdate?.(updatedUser);
    // TODO: Save to backend
  }, [user, onUserUpdate]);

  const trackInventoryLevelChange = useCallback(async (newLevel: 'none' | 'smart_cost' | 'full_control') => {
    if (!user) return;

    // Track this as a significant event
    if (user.inventory_level === 'none' && newLevel !== 'none') {
      // User adopted inventory features
      await trackFeatureUsage('costs_viewed');
    }

    const updatedUser = {
      ...user,
      inventory_level: newLevel,
    };

    onUserUpdate?.(updatedUser);
    // TODO: Save to backend
  }, [user, onUserUpdate, trackFeatureUsage]);

  const updateLastActiveDate = useCallback(async () => {
    if (!user?.behavior_metrics) return;

    const updatedUser = {
      ...user,
      behavior_metrics: {
        ...user.behavior_metrics,
        last_active_date: new Date().toISOString(),
      },
    };

    onUserUpdate?.(updatedUser);
  }, [user, onUserUpdate]);

  return {
    trackConsultationCompleted,
    trackFeatureUsage,
    trackInventoryLevelChange,
    updateLastActiveDate,
  };
};