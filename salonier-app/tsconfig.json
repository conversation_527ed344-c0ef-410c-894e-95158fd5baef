{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictBindCallApply": true, "strictPropertyInitialization": true, "noImplicitThis": true, "alwaysStrict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "esModuleInterop": true, "skipLibCheck": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "isolatedModules": true, "jsx": "react-native", "lib": ["es2021"], "moduleResolution": "bundler", "allowJs": true, "forceConsistentCasingInFileNames": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}}, "include": ["src/**/*", "index.ts", "App.tsx"], "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js", "**/*.spec.ts", "**/*.test.ts"]}