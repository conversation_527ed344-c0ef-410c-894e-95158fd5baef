import 'react-native-gesture-handler';
import React, { useEffect, useState } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { StatusBar } from 'expo-status-bar';
import { ActivityIndicator, View, Text } from 'react-native';
import { COLORS } from './src/constants';
import { COMPONENTS, TYPOGRAPHY } from './src/constants/design-system';
import { stackScreenOptions, tabScreenOptions } from './src/constants/navigation';
// import { dataService } from './src/services/dataService';
import { AppProvider } from './src/contexts/AppContext';
import { ErrorBoundary } from './src/components/common/ErrorBoundary';
import { NotificationService } from './src/services/notificationService';
import { Icon } from './src/components/common/Icon';
import Toast from 'react-native-toast-message';
import { toastConfig } from './src/constants/toastConfig';

// Screens
import DashboardScreen from './src/screens/DashboardScreen';
import ConsultationFlowScreen from './src/screens/ConsultationFlowScreen';
import SimpleColoristFlow from './src/screens/SimpleColoristFlow';
import ClientsScreen from './src/screens/ClientsScreen';
import ClientDetailScreen from './src/screens/ClientDetailScreen';
import AppointmentsScreen from './src/screens/AppointmentsScreen';
import BrandConverterScreen from './src/screens/BrandConverterScreen';
import SettingsScreen from './src/screens/SettingsScreen';
import InventoryScreen from './src/screens/InventoryScreen';
import StockMovementHistoryScreen from './src/screens/StockMovementHistoryScreen';

// Por ahora crearemos placeholders para las otras pantallas
const PlaceholderScreen = ({ route }: any) => (
  <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
    <Text>{route.name} Screen</Text>
  </View>
);

const Stack = createStackNavigator();
const Tab = createBottomTabNavigator();

// Navegación principal con tabs
function MainTabs() {
  return (
    <Tab.Navigator screenOptions={tabScreenOptions}>
      <Tab.Screen
        name="Dashboard"
        component={DashboardScreen}
        options={{
          title: 'Inicio',
          tabBarLabel: 'Inicio',
          tabBarIcon: ({ color }) => <Icon name="home" color={color} size="md" />,
        }}
      />
      <Tab.Screen
        name="Clients"
        component={ClientsScreen}
        options={{
          title: 'Clientes',
          tabBarLabel: 'Clientes',
          tabBarIcon: ({ color }) => <Icon name="users" color={color} size="md" />,
        }}
      />
      <Tab.Screen
        name="Appointments"
        component={AppointmentsScreen}
        options={{
          title: 'Agenda',
          tabBarLabel: 'Agenda',
          tabBarIcon: ({ color }) => <Icon name="calendar" color={color} size="md" />,
        }}
      />
      <Tab.Screen
        name="Inventory"
        component={InventoryScreen}
        options={{
          title: 'Inventario',
          tabBarLabel: 'Inventario',
          tabBarIcon: ({ color }) => <Icon name="box" color={color} size="md" />,
        }}
      />
      <Tab.Screen
        name="Settings"
        component={SettingsScreen}
        options={{
          title: 'Ajustes',
          tabBarLabel: 'Ajustes',
          tabBarIcon: ({ color }) => <Icon name="settings" color={color} size="md" />,
        }}
      />
    </Tab.Navigator>
  );
}

function App() {
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      // Initialize notifications
      await NotificationService.initialize();

      // Check authentication
      // BYPASS TEMPORAL: Forzar autenticación para pruebas
      // const { session } = await dataService.auth.getSession();
      // setIsAuthenticated(!!session);
      setIsAuthenticated(true); // TEMPORAL: Siempre autenticado para pruebas
    } catch (error) {
      console.error('Error initializing app:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" color={COLORS.primary} />
      </View>
    );
  }

  return (
    <>
      <StatusBar style="dark" />
      <NavigationContainer>
        <Stack.Navigator screenOptions={{ headerShown: false }}>
          {isAuthenticated ? (
            <>
              <Stack.Screen name="Main" component={MainTabs} />
              <Stack.Screen
                name="NewConsultation"
                component={ConsultationFlowScreen}
                options={{
                  ...stackScreenOptions,
                  title: 'Nueva Consulta',
                }}
              />
              <Stack.Screen
                name="SimpleColoristFlow"
                component={SimpleColoristFlow}
                options={{
                  ...stackScreenOptions,
                  headerShown: true,
                  title: '',
                }}
              />
              <Stack.Screen
                name="BrandConverter"
                component={BrandConverterScreen}
                options={{
                  ...stackScreenOptions,
                  title: 'Conversor de Marcas',
                }}
              />
              <Stack.Screen
                name="ClientDetail"
                component={ClientDetailScreen}
                options={{
                  ...stackScreenOptions,
                }}
              />
              <Stack.Screen
                name="StockMovementHistory"
                component={StockMovementHistoryScreen}
                options={{
                  ...stackScreenOptions,
                  title: 'Historial de Movimientos',
                }}
              />
            </>
          ) : (
            <>
              {/* Aquí irán las pantallas de autenticación */}
              <Stack.Screen name="Login" component={PlaceholderScreen} />
              <Stack.Screen name="SignUp" component={PlaceholderScreen} />
              <Stack.Screen name="Onboarding" component={PlaceholderScreen} />
            </>
          )}
        </Stack.Navigator>
      </NavigationContainer>
      <Toast config={toastConfig} />
    </>
  );
}

// Envolver la app con providers
export default function AppWithProviders() {
  return (
    <ErrorBoundary>
      <AppProvider>
        <App />
      </AppProvider>
    </ErrorBoundary>
  );
}
