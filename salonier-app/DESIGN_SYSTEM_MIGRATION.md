# Design System Migration Guide

## Card Component Changes

The Card component's `elevation` prop has changed from:
- `'low' | 'medium' | 'high'`

To the new design system values:
- `'none' | 'sm' | 'md' | 'lg' | 'xl'`

### Migration mapping:
- `elevation="low"` → `elevation="sm"`
- `elevation="medium"` → `elevation="md"` or `elevation="lg"`
- `elevation="high"` → `elevation="lg"` or `elevation="xl"`

### Example:
```tsx
// Old
<Card elevation="medium">

// New
<Card elevation="md">
```

## Updated Components

### 1. Card.tsx
- Now uses the new design system from `constants/design-system.ts`
- Default elevation changed from `'low'` to `'sm'`
- Default padding changed from `'lg'` to `'md'`
- Default borderRadius changed from `'lg'` to `'xl'`
- Added `variant` prop: `'base' | 'elevated'`

### 2. FormInput.tsx
- Now uses gray background (`COLORS.surface`) instead of white
- No borders by default, only on focus/error states
- Rounded corners using `BORDER_RADIUS.lg`
- Uses new typography and spacing from design system

### 3. ProgressButton.tsx
- Full rounded buttons (`BORDER_RADIUS.full`)
- Updated to use new color scheme
- Primary button uses `COLORS.secondary` (blue)
- Secondary button uses `COLORS.accent` (soft blue)

### 4. EmptyState.tsx
- Updated typography using new design system
- Icon container uses `COLORS.surface` background
- Buttons match new design system styling

## Color Changes
- Primary: `#131516` (charcoal)
- Secondary: `#1184e3` (blue) 
- Accent: `#caddec` (soft blue)
- Surface: `#f1f2f3` (light gray for inputs/cards)

## Next Steps
To complete the migration:
1. Update all Card component usages to use new elevation values
2. Review and update any custom styling that references old colors
3. Test all forms to ensure the new input styling works correctly