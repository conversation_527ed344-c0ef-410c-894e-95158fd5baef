{"name": "salonier-app", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx,json,css,md}\"", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "prepare": "husky install"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "^8.4.1", "@react-native-community/slider": "^4.5.6", "@react-native-picker/picker": "^2.11.1", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "@supabase/supabase-js": "^2.50.2", "date-fns": "^4.1.0", "expo": "~53.0.13", "expo-camera": "^16.1.9", "expo-device": "~7.1.4", "expo-face-detector": "^13.0.2", "expo-file-system": "~18.1.10", "expo-haptics": "~14.1.4", "expo-image-manipulator": "^13.1.7", "expo-image-picker": "^16.1.4", "expo-mail-composer": "~14.1.5", "expo-media-library": "^17.1.7", "expo-notifications": "~0.31.3", "expo-print": "^14.1.4", "expo-secure-store": "^14.2.3", "expo-sharing": "~13.1.5", "expo-status-bar": "~2.2.3", "expo-task-manager": "~13.1.5", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.4", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "^4.11.1", "react-native-signature-canvas": "^5.0.1", "react-native-svg": "^15.11.2", "react-native-toast-message": "^2.3.2", "react-native-url-polyfill": "^2.0.0", "react-native-vector-icons": "^10.2.0", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@react-native/eslint-config": "^0.73.2", "@types/jest": "^29.5.12", "@types/react": "~19.0.10", "@types/react-test-renderer": "^18.0.7", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.57.0", "eslint-config-expo": "^9.2.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-native": "^4.1.0", "husky": "^8.0.3", "jest": "^29.7.0", "jest-expo": "~53.0.0", "lint-staged": "^15.2.0", "prettier": "^3.2.5", "react-test-renderer": "19.0.0", "typescript": "~5.8.3"}, "private": true, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}}