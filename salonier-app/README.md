# Salonier - Asistente de Coloración con IA

<p align="center">
  <img src="assets/icon.png" alt="Salonier Logo" width="120" />
</p>

Salonier es una aplicación multiplataforma (iOS, Android, Web) diseñada como el asistente personal definitivo para estilistas especializados en coloración capilar. Combina inteligencia artificial avanzada con la experiencia profesional del colorista.

## 🚀 Características Principales

### Implementadas ✅
- **Asistente de Coloración IA**: Flujo de consulta paso a paso con análisis inteligente
- **100+ Marcas Profesionales**: Base de datos completa incluyendo L'Oréal, Wella, <PERSON><PERSON>, <PERSON>rm, Lendan, J Beverly Hills
- **Conversión de Marcas**: Calculadora de conversión entre diferentes marcas con nivel de confianza
- **Análisis por Zonas**: Análisis detallado de raíces, medios y puntas
- **Aná<PERSON>is <PERSON>an<PERSON>**: Cálculo automático de costos y márgenes de ganancia
- **Firma Digital**: Consentimiento digital para tratamientos
- **Modo Foto/Manual**: Flexibilidad en el análisis del cabello

### En Desarrollo 🚧
- **Gestión de Clientes**: CRUD completo de clientes
- **Agenda Inteligente**: Sistema de citas con recordatorios
- **Inventario**: Control de stock con alertas
- **Integración Supabase**: Migración de datos mock a base de datos real

## 📋 Requisitos Previos

- Node.js >= 18
- npm o yarn
- Expo CLI (`npm install -g expo-cli`)
- iOS Simulator (Mac) o Android Emulator
- App Expo Go en tu dispositivo móvil

## 🛠️ Instalación

1. **Clonar el repositorio**
```bash
git clone https://github.com/[tu-usuario]/salonier-app.git
cd salonier-app
```

2. **Instalar dependencias**
```bash
npm install
```

3. **Configurar variables de entorno**
```bash
cp .env.example .env
# Edita .env con tus credenciales de Supabase
```

4. **Iniciar el servidor de desarrollo**
```bash
npm start
```

5. **Ejecutar en tu dispositivo**
- Escanea el código QR con Expo Go (Android) o la app de Cámara (iOS)
- Presiona `i` para iOS simulator o `a` para Android emulator

## 📱 Flujo de Consulta

1. **Selección de Cliente**: Elegir cliente existente o crear nuevo
2. **Revisión de Seguridad**: Cuestionario digital de alergias y consentimiento
3. **Análisis del Cabello**: 
   - Modo Foto: Análisis automático con IA (simulado)
   - Modo Manual: Selección visual directa
   - Personalización por zonas opcional
4. **Color Deseado**: 
   - Selección visual del color objetivo
   - Técnicas: Global, Mechas, Balayage, Ombré, Babylights
   - Análisis por zonas opcional
5. **Formulación**: Generación automática de fórmula basada en el análisis
6. **Conversión de Marcas**: Alternativas en 4 marcas diferentes
7. **Finalización**: Guardar consulta, generar PDF, enviar por WhatsApp

## 📁 Estructura del Proyecto

```
salonier-app/
├── src/
│   ├── components/      # Componentes reutilizables
│   │   ├── colorist/   # Pasos del flujo de consulta
│   │   ├── common/     # Componentes compartidos
│   │   └── dashboard/  # Widgets del dashboard
│   ├── constants/      # Tokens de diseño y constantes
│   ├── contexts/       # Contextos de React
│   ├── hooks/          # Custom hooks
│   ├── navigation/     # Configuración de navegación
│   ├── screens/        # Pantallas principales
│   ├── services/       # Servicios y APIs
│   └── types/          # Definiciones TypeScript
├── supabase/           # Configuración de Supabase
├── assets/             # Imágenes y recursos
└── App.tsx             # Punto de entrada
```

## 🎨 Sistema de Diseño

- **Estilo**: Neumorfismo minimalista con estética beauty-tech
- **Colores**: Paleta neutra con acentos dorados
- **Tipografía**: Sistema tipográfico consistente
- **Componentes**: Card system con elevaciones suaves
- **Navegación**: Tab + Stack navigation optimizada para uso con una mano

## 🔧 Tecnologías

- **Frontend**: React Native + Expo
- **Lenguaje**: TypeScript
- **Estado**: React Context API con hook personalizado `useConsultation`
- **Backend**: Configuración Supabase (actualmente usando datos mock)
- **UI**: Sistema de diseño personalizado con constantes compartidas
- **Navegación**: React Navigation v7

## 🚧 Estado Actual del Desarrollo

### Completado ✅
- Flujo completo de consulta (7 pasos)
- Sistema de análisis por zonas
- Conversión entre marcas
- Análisis de costos y rentabilidad
- Mock data comprehensivo (100+ marcas y productos)
- UI/UX consistente y profesional

### Próximos Pasos 📋
- [ ] Implementar gestión de clientes (modal de creación)
- [ ] Completar CompletionStep con funcionalidades reales
- [ ] Integrar cámara real para captura de fotos
- [ ] Conectar con Supabase para persistencia de datos
- [ ] Implementar pantallas de Clientes, Citas e Inventario
- [ ] Añadir autenticación de usuarios
- [ ] Implementar generación real de PDFs
- [ ] Integración con WhatsApp Business API

## 📝 Scripts Disponibles

- `npm start` - Inicia Expo en modo desarrollo
- `npm run android` - Ejecuta en Android
- `npm run ios` - Ejecuta en iOS
- `npm run web` - Ejecuta en navegador

## 🔐 Seguridad y Privacidad

- Preparado para encriptación de datos sensibles
- Arquitectura lista para Row Level Security en Supabase
- Consentimiento digital para tratamientos
- Estructura preparada para cumplimiento GDPR

## 🤝 Contribuir

Este es un proyecto privado. Por favor, contacta al propietario para las pautas de contribución.

## 📄 Licencia

Este proyecto es software propietario. Todos los derechos reservados.

## 👥 Equipo

- Oscar Cortijo - Fundador y Desarrollador Principal

## 📞 Soporte

Para soporte, envía un email a: [tu-email]

---

Construido con ❤️ para profesionales del color