{"permissions": {"allow": ["<PERSON><PERSON>(touch:*)", "Bash(npx create-expo-app:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npm install:*)", "Bash(npx expo install:*)", "Bash(npm start)", "Bash(npx expo:*)", "Bash(lsof:*)", "<PERSON><PERSON>(pkill:*)", "Bash(rm:*)", "Bash(find:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git branch:*)", "<PERSON><PERSON>(true)", "Bash(git push:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(killall:*)", "<PERSON><PERSON>(watchman watch-del:*)", "<PERSON><PERSON>(watchman:*)", "Bash(rg:*)", "<PERSON><PERSON>(mv:*)", "Bash(npm start:*)", "Bash(grep:*)", "<PERSON><PERSON>(chmod:*)", "Bash(npm run type-check:*)", "<PERSON><PERSON>(cat:*)", "Bash(ls:*)", "Bash(npx tsc:*)", "mcp__ide__getDiagnostics", "Bash(npm run lint)", "Bash(npm run tsc:*)", "Bash(npx eslint:*)", "Bash(awk:*)", "Bash(cp:*)", "Bash(npx:*)", "<PERSON><PERSON>(sed:*)", "Bash(./update-constants.sh:*)", "Bash(./fix-imports.sh)", "Bash(npm run:*)", "Bash(for:*)", "Bash(do echo \"=== $file ===\")", "Bash(done)", "Bash(do if grep -q \"FONT_WEIGHTS\" \"$file\")", "Bash(then echo \"FONT_WEIGHTS still found in: $file\")", "Bash(fi)", "WebFetch(domain:apps.apple.com)", "Bash(EXPO_DEVTOOLS_LISTEN_ADDRESS=0.0.0.0 npx expo start --lan)", "Bash(REACT_NATIVE_PACKAGER_HOSTNAME=************* npx expo start --clear --lan)", "<PERSON><PERSON>(sudo:*)", "Bash(/usr/libexec/ApplicationFirewall/socketfilterfw:*)", "Bash(RCT_METRO_PORT=8082 npx expo start --port 8082)"], "deny": []}}