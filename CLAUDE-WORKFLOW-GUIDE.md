# Flujo de Trabajo Estándar para Claude Code

## 1. Fase de Análisis
Primero, analiza exhaustivamente el problema:
- Lee TODOS los archivos relevantes del código base antes de hacer cambios
- Comprende la estructura del proyecto y las dependencias
- Identifica impactos potenciales y efectos secundarios
- Documenta tu comprensión de la implementación actual

## 2. Fase de Planificación
Crea un plan detallado con:
- **Lista de tareas**: Divide el trabajo en tareas atómicas e independientes (máx. 5-10 líneas por tarea)
- **Dependencias**: Nota qué tareas dependen de otras
- **Archivos a modificar**: Lista archivos exactos con breve descripción de cambios
- **Nuevos archivos/funciones**: Especifica convenciones de nombres y propósito
- **Estrategia de pruebas**: ¿Cómo verificarás cada cambio?

### Plantilla del Plan:
```
## Plan de Implementación
### Tareas:
- [ ] Tarea 1: [Descripción] (Archivo: xyz.js)
- [ ] Tarea 2: [Descripción] (Archivos: abc.py, def.py)

### Dependencias:
- La Tarea 2 depende de la Tarea 1

### Evaluación de Riesgos:
- Posibles cambios disruptivos en...
- Necesidad de mantener compatibilidad con...
```

## 3. Fase de Implementación
- **Un cambio a la vez**: Completa cada tarea antes de pasar a la siguiente
- **Prueba sobre la marcha**: Ejecuta pruebas relevantes después de cada cambio
- **Mantén cambios mínimos**: Prefiere múltiples commits pequeños sobre uno grande
- **Mantén consistencia**: Sigue el estilo y patrones de código existentes
- **Agrega comentarios**: Documenta lógica compleja o decisiones no obvias

## 4. Lineamientos de Calidad de Código
- **Simplicidad primero**: Elige la solución más simple que funcione
- **Sin optimización prematura**: Enfócate en la corrección antes que en el rendimiento
- **Principio DRY**: No te repitas, pero no sobre-abstraigas
- **Manejo de errores**: Siempre maneja casos límite y fallas potenciales
- **Seguridad de tipos**: Usa anotaciones de tipos donde sea aplicable

## 5. Requisitos de Documentación
Después de cada tarea, actualiza:
- `todo.md` con elementos completados marcados
- Comentarios en el código para lógica compleja
- README si la funcionalidad cambia
- Documentación de API si las interfaces cambian

## 6. Lista de Verificación de Revisión
Antes de considerar el trabajo completo:
- [ ] Todas las pruebas pasan
- [ ] Sin errores de linting
- [ ] El código sigue las convenciones del proyecto
- [ ] Los cambios son retrocompatibles (o los cambios disruptivos están documentados)
- [ ] Impacto en el rendimiento considerado
- [ ] Implicaciones de seguridad revisadas
- [ ] Documentación actualizada

## 7. Resumen Final
Agrega a `todo.md`:
```markdown
## Sección de Revisión - [Fecha]
### Cambios Realizados:
- [Componente/Archivo]: [Qué cambió y por qué]
- [Componente/Archivo]: [Qué cambió y por qué]

### Pruebas Realizadas:
- [Tipo de prueba]: [Resultado]

### Problemas Conocidos/Trabajo Futuro:
- [Problema]: [Descripción y solución potencial]

### Cambios Disruptivos:
- [Si hay]: [Guía de migración]
```

## 8. Mejores Prácticas para Claude Code
- **Usa rutas explícitas**: Siempre especifica rutas completas desde la raíz del proyecto
- **Guardados incrementales**: Guarda archivos frecuentemente para evitar perder trabajo
- **Nombres de variables claros**: Prefiere nombres descriptivos sobre comentarios
- **Diseño modular**: Mantén funciones pequeñas y enfocadas
- **Mentalidad de control de versiones**: Piensa en términos de commits atómicos
- **Pide aclaraciones**: Cuando los requisitos sean ambiguos, pregunta antes de implementar

## 9. Errores Comunes a Evitar
- No modifiques múltiples sistemas no relacionados en una tarea
- No asumas contenido de archivos - siempre lee primero
- No ignores mensajes de error - abórdalos inmediatamente
- No omitas pruebas en cambios "simples"
- No dejes comentarios TODO sin rastrearlos

## 10. Procedimientos de Emergencia
Si algo se rompe:
1. Detente y evalúa el daño
2. Revierte el último cambio si es necesario
3. Documenta qué salió mal
4. Crea un plan de corrección antes de proceder
5. Prueba la corrección exhaustivamente

## 11. Comandos Útiles Frecuentes
```bash
# Ver estructura del proyecto
find . -type f -name "*.py" | head -20

# Buscar en archivos
grep -r "función_específica" --include="*.js"

# Verificar sintaxis Python
python -m py_compile archivo.py

# Ejecutar pruebas específicas
pytest tests/test_modulo.py::test_funcion
```

## 12. Flujo de Comunicación
- **Reporta progreso**: Actualiza después de cada tarea completada
- **Comunica bloqueos**: Si algo te detiene, repórtalo inmediatamente
- **Sugiere mejoras**: Si ves oportunidades de refactorización, documéntalas
- **Confirma entendimiento**: Resume requisitos complejos antes de implementar